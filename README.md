# JSON Preview Application

## 项目概述
这是一个基于Spring Boot的JSON预览应用程序。

## 技术栈
- Spring Boot 3.2.0
- Java 17
- Maven
- Spring Web
- Jackson (JSON处理)

## 项目结构
```
json_preview/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/example/jsonpreview/
│   │   │       ├── JsonPreviewApplication.java     # 主应用类
│   │   │       └── controller/
│   │   │           └── HomeController.java         # 测试控制器
│   │   └── resources/
│   │       └── application.properties              # 应用配置
│   └── test/
│       └── java/
├── pom.xml                                         # Maven配置
└── README.md                                       # 项目文档
```

## 已完成功能
- ✅ 初始化Spring Boot项目结构
- ✅ 配置Maven依赖管理
- ✅ 创建主应用类
- ✅ 添加基本的Web控制器
- ✅ 配置JSON序列化设置
- ✅ 添加健康检查端点

## 运行方式

### 开发环境启动
```bash
# 使用Maven运行
mvn spring-boot:run

# 或者先编译再运行
mvn clean package
java -jar target/json-preview-0.0.1-SNAPSHOT.jar
```

### 访问地址
- 主页：http://localhost:8080/
- 健康检查：http://localhost:8080/health

## 配置说明
- 默认端口：8080
- JSON格式化输出已启用
- 开发工具已配置（支持热重载）

## 开发者备注
- 项目使用代理设置：http://127.0.0.1:7890
- Java版本要求：17+
- 支持热重载开发

## 版本历史
- v0.0.1-SNAPSHOT：初始化项目结构，基础Web功能 