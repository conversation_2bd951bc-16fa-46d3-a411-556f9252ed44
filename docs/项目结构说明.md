# 企业级工作流引擎项目结构说明

## 1. 整体项目结构

```
workflow-engine/
├── README.md                           # 项目说明文档
├── pom.xml                            # Maven 主配置文件
├── docker-compose.yml                 # Docker Compose 配置
├── Dockerfile                         # Docker 镜像构建文件
├── docs/                              # 文档目录
│   ├── 技术设计文档.md                 # 技术设计文档
│   ├── 项目结构说明.md                 # 项目结构说明
│   ├── API接口文档.md                  # API 接口文档
│   ├── 部署运维手册.md                 # 部署运维手册
│   └── 用户使用指南.md                 # 用户使用指南
├── sql/                               # 数据库脚本
│   ├── init.sql                       # 初始化脚本
│   ├── schema.sql                     # 表结构脚本
│   └── data.sql                       # 初始数据脚本
├── k8s/                               # Kubernetes 部署配置
│   ├── namespace.yaml                 # 命名空间配置
│   ├── workflow-engine-deployment.yaml # 应用部署配置
│   ├── workflow-configmap.yaml        # 配置管理
│   ├── workflow-hpa.yaml              # 水平扩展配置
│   └── monitoring/                    # 监控配置
│       ├── prometheus-config.yaml     # Prometheus 配置
│       └── grafana-dashboard.json     # Grafana 仪表板
├── backend/                           # 后端服务
│   ├── pom.xml                        # 后端 Maven 配置
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/com/company/workflow/
│   │   │   │   ├── WorkflowEngineApplication.java  # 启动类
│   │   │   │   ├── config/            # 配置类
│   │   │   │   │   ├── SecurityConfig.java        # 安全配置
│   │   │   │   │   ├── CacheConfig.java           # 缓存配置
│   │   │   │   │   ├── TaskSchedulerConfig.java   # 任务调度配置
│   │   │   │   │   ├── RabbitConfig.java          # 消息队列配置
│   │   │   │   │   └── SwaggerConfig.java         # API 文档配置
│   │   │   │   ├── controller/        # 控制器层
│   │   │   │   │   ├── WorkflowController.java    # 工作流管理
│   │   │   │   │   ├── WorkflowExecutionController.java # 执行控制
│   │   │   │   │   ├── MonitoringController.java  # 监控接口
│   │   │   │   │   └── UserController.java        # 用户管理
│   │   │   │   ├── service/           # 服务层
│   │   │   │   │   ├── WorkflowService.java       # 工作流服务
│   │   │   │   │   ├── WorkflowExecutionService.java # 执行服务
│   │   │   │   │   ├── UserService.java           # 用户服务
│   │   │   │   │   └── NotificationService.java   # 通知服务
│   │   │   │   ├── engine/            # 执行引擎
│   │   │   │   │   ├── WorkflowExecutionEngine.java # 执行引擎
│   │   │   │   │   ├── StateManager.java          # 状态管理
│   │   │   │   │   ├── TaskScheduler.java         # 任务调度
│   │   │   │   │   ├── executor/      # 节点执行器
│   │   │   │   │   │   ├── NodeExecutorFactory.java
│   │   │   │   │   │   ├── TaskNodeExecutor.java
│   │   │   │   │   │   ├── DecisionNodeExecutor.java
│   │   │   │   │   │   ├── ParallelNodeExecutor.java
│   │   │   │   │   │   └── NotificationNodeExecutor.java
│   │   │   │   │   └── context/       # 执行上下文
│   │   │   │   │       ├── ExecutionContext.java
│   │   │   │   │       └── WorkflowVariables.java
│   │   │   │   ├── dsl/               # DSL 解析
│   │   │   │   │   ├── WorkflowDslParser.java     # DSL 解析器
│   │   │   │   │   ├── ASTTransformer.java        # AST 转换器
│   │   │   │   │   ├── ValidationEngine.java      # 验证引擎
│   │   │   │   │   └── WorkflowBuilder.java       # 工作流构建器
│   │   │   │   ├── model/             # 数据模型
│   │   │   │   │   ├── entity/        # 实体类
│   │   │   │   │   │   ├── WorkflowDefinition.java
│   │   │   │   │   │   ├── WorkflowInstance.java
│   │   │   │   │   │   ├── NodeExecution.java
│   │   │   │   │   │   ├── User.java
│   │   │   │   │   │   ├── Role.java
│   │   │   │   │   │   └── Permission.java
│   │   │   │   │   ├── dto/           # 数据传输对象
│   │   │   │   │   │   ├── WorkflowDefinitionDTO.java
│   │   │   │   │   │   ├── WorkflowInstanceDTO.java
│   │   │   │   │   │   └── NodeExecutionDTO.java
│   │   │   │   │   └── vo/            # 视图对象
│   │   │   │   │       ├── WorkflowListVO.java
│   │   │   │   │       └── ExecutionStatusVO.java
│   │   │   │   ├── repository/        # 数据访问层
│   │   │   │   │   ├── WorkflowDefinitionRepository.java
│   │   │   │   │   ├── WorkflowInstanceRepository.java
│   │   │   │   │   ├── NodeExecutionRepository.java
│   │   │   │   │   └── UserRepository.java
│   │   │   │   ├── security/          # 安全模块
│   │   │   │   │   ├── JwtAuthenticationEntryPoint.java
│   │   │   │   │   ├── JwtRequestFilter.java
│   │   │   │   │   ├── JwtTokenUtil.java
│   │   │   │   │   └── EncryptionService.java
│   │   │   │   ├── monitoring/        # 监控模块
│   │   │   │   │   ├── MetricsCollector.java
│   │   │   │   │   ├── AlertManager.java
│   │   │   │   │   └── AuditLogger.java
│   │   │   │   ├── integration/       # 集成模块
│   │   │   │   │   ├── email/         # 邮件集成
│   │   │   │   │   │   ├── EmailService.java
│   │   │   │   │   │   └── EmailTemplate.java
│   │   │   │   │   ├── wechat/        # 企业微信集成
│   │   │   │   │   │   └── WeChatService.java
│   │   │   │   │   └── http/          # HTTP 集成
│   │   │   │   │       └── HttpClientService.java
│   │   │   │   ├── exception/         # 异常处理
│   │   │   │   │   ├── GlobalExceptionHandler.java
│   │   │   │   │   ├── WorkflowException.java
│   │   │   │   │   └── ValidationException.java
│   │   │   │   └── util/              # 工具类
│   │   │   │       ├── JsonUtil.java
│   │   │   │       ├── DateUtil.java
│   │   │   │       ├── AESUtil.java
│   │   │   │       └── RSAUtil.java
│   │   │   └── resources/
│   │   │       ├── application.yml    # 主配置文件
│   │   │       ├── application-dev.yml # 开发环境配置
│   │   │       ├── application-prod.yml # 生产环境配置
│   │   │       ├── application-docker.yml # Docker 环境配置
│   │   │       ├── logback-spring.xml # 日志配置
│   │   │       └── templates/         # 模板文件
│   │   │           ├── email/         # 邮件模板
│   │   │           └── notification/  # 通知模板
│   │   └── test/
│   │       ├── java/com/company/workflow/
│   │       │   ├── controller/        # 控制器测试
│   │       │   ├── service/           # 服务测试
│   │       │   ├── engine/            # 引擎测试
│   │       │   └── integration/       # 集成测试
│   │       └── resources/
│   │           ├── application-test.yml # 测试配置
│   │           └── test-data/         # 测试数据
└── frontend/                          # 前端应用
    ├── package.json                   # 前端依赖配置
    ├── vite.config.ts                 # Vite 配置
    ├── tsconfig.json                  # TypeScript 配置
    ├── Dockerfile                     # 前端 Docker 配置
    ├── nginx.conf                     # Nginx 配置
    ├── public/                        # 静态资源
    │   ├── index.html
    │   └── favicon.ico
    ├── src/
    │   ├── main.ts                    # 应用入口
    │   ├── App.vue                    # 根组件
    │   ├── router/                    # 路由配置
    │   │   └── index.ts
    │   ├── store/                     # 状态管理
    │   │   ├── index.ts
    │   │   ├── modules/
    │   │   │   ├── workflow.ts        # 工作流状态
    │   │   │   ├── user.ts            # 用户状态
    │   │   │   └── monitoring.ts      # 监控状态
    │   ├── api/                       # API 接口
    │   │   ├── workflow.ts            # 工作流 API
    │   │   ├── execution.ts           # 执行 API
    │   │   ├── user.ts                # 用户 API
    │   │   └── monitoring.ts          # 监控 API
    │   ├── components/                # 公共组件
    │   │   ├── WorkflowDesigner/      # 工作流设计器
    │   │   │   ├── index.vue          # 设计器主组件
    │   │   │   ├── config.ts          # 配置文件
    │   │   │   ├── nodes/             # 自定义节点
    │   │   │   │   ├── index.ts
    │   │   │   │   ├── TaskNode.ts
    │   │   │   │   ├── DecisionNode.ts
    │   │   │   │   ├── ParallelNode.ts
    │   │   │   │   └── NotificationNode.ts
    │   │   │   ├── NodePalette.vue    # 节点面板
    │   │   │   ├── PropertyEditor.vue # 属性编辑器
    │   │   │   └── DslEditor.vue      # DSL 编辑器
    │   │   ├── MonitoringDashboard/   # 监控仪表板
    │   │   │   ├── index.vue
    │   │   │   ├── ExecutionChart.vue
    │   │   │   ├── PerformanceChart.vue
    │   │   │   └── AlertPanel.vue
    │   │   └── Common/                # 通用组件
    │   │       ├── Layout.vue
    │   │       ├── Header.vue
    │   │       ├── Sidebar.vue
    │   │       └── Breadcrumb.vue
    │   ├── views/                     # 页面组件
    │   │   ├── Login.vue              # 登录页面
    │   │   ├── Dashboard.vue          # 仪表板
    │   │   ├── WorkflowList.vue       # 工作流列表
    │   │   ├── WorkflowDesign.vue     # 工作流设计
    │   │   ├── ExecutionList.vue      # 执行列表
    │   │   ├── ExecutionDetail.vue    # 执行详情
    │   │   ├── Monitoring.vue         # 监控页面
    │   │   └── UserManagement.vue     # 用户管理
    │   ├── utils/                     # 工具函数
    │   │   ├── request.ts             # HTTP 请求工具
    │   │   ├── auth.ts                # 认证工具
    │   │   ├── date.ts                # 日期工具
    │   │   └── dslConverter.ts        # DSL 转换工具
    │   ├── styles/                    # 样式文件
    │   │   ├── index.scss             # 全局样式
    │   │   ├── variables.scss         # 样式变量
    │   │   └── components/            # 组件样式
    │   └── types/                     # TypeScript 类型定义
    │       ├── workflow.ts            # 工作流类型
    │       ├── user.ts                # 用户类型
    │       └── api.ts                 # API 类型
    └── dist/                          # 构建输出目录
```

## 2. 核心模块说明

### 2.1 后端核心模块

#### 2.1.1 执行引擎模块 (engine/)
- **WorkflowExecutionEngine**: 工作流执行引擎核心类
- **StateManager**: 工作流和节点状态管理
- **TaskScheduler**: 任务调度和延迟执行
- **executor/**: 各种节点类型的执行器实现

#### 2.1.2 DSL 解析模块 (dsl/)
- **WorkflowDslParser**: Groovy DSL 解析器
- **ASTTransformer**: 抽象语法树转换器
- **ValidationEngine**: DSL 语法和语义验证
- **WorkflowBuilder**: 工作流定义构建器

#### 2.1.3 数据模型模块 (model/)
- **entity/**: JPA 实体类定义
- **dto/**: 数据传输对象
- **vo/**: 视图对象

#### 2.1.4 集成模块 (integration/)
- **email/**: 邮件服务集成
- **wechat/**: 企业微信集成
- **http/**: HTTP 服务调用集成

### 2.2 前端核心模块

#### 2.2.1 工作流设计器 (WorkflowDesigner/)
- **index.vue**: 设计器主界面
- **nodes/**: LogicFlow 自定义节点定义
- **NodePalette.vue**: 节点拖拽面板
- **PropertyEditor.vue**: 节点属性编辑器
- **DslEditor.vue**: DSL 代码编辑器

#### 2.2.2 监控仪表板 (MonitoringDashboard/)
- **ExecutionChart.vue**: 执行统计图表
- **PerformanceChart.vue**: 性能监控图表
- **AlertPanel.vue**: 告警信息面板

## 3. 配置文件说明

### 3.1 后端配置
- **application.yml**: 主配置文件，包含数据库、Redis、消息队列等配置
- **application-{profile}.yml**: 不同环境的配置文件
- **logback-spring.xml**: 日志配置文件

### 3.2 前端配置
- **package.json**: 依赖管理和脚本配置
- **vite.config.ts**: 构建工具配置
- **tsconfig.json**: TypeScript 编译配置

### 3.3 部署配置
- **docker-compose.yml**: 本地开发环境部署
- **k8s/**: Kubernetes 生产环境部署配置
- **monitoring/**: 监控系统配置

## 4. 开发规范

### 4.1 代码规范
- 遵循 Google Java Style Guide
- 使用 ESLint 和 Prettier 进行前端代码格式化
- 所有公共方法必须添加 Javadoc 注释
- 单元测试覆盖率不低于 80%

### 4.2 Git 规范
- 使用 Git Flow 分支管理策略
- Commit 消息遵循 Conventional Commits 规范
- 代码合并前必须通过 Code Review

### 4.3 API 规范
- 遵循 RESTful API 设计原则
- 使用 OpenAPI 3.0 规范编写 API 文档
- 统一的错误码和响应格式

这个项目结构为企业级工作流引擎系统提供了清晰的代码组织和模块划分，确保系统的可维护性和可扩展性。
