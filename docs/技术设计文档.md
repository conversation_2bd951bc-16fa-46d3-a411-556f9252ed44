# 企业级工作流引擎系统技术设计文档

## 1. 系统概述

### 1.1 项目背景
本项目旨在构建一个基于 Groovy DSL 的企业级工作流引擎系统，提供可视化流程设计、强大的执行引擎、完善的监控管理等功能，满足企业复杂业务流程自动化需求。

### 1.2 核心特性
- **可视化设计**：基于滴滴 LogicFlow 的流程可视化编辑器
- **DSL 语法**：直观的 Groovy DSL 语法支持
- **高性能执行**：支持并行、异步、分布式执行
- **企业级特性**：完善的权限控制、监控告警、高可用部署
- **丰富集成**：支持多种第三方服务和系统集成

### 1.3 技术栈
- **后端框架**：Spring Boot 3.x + Spring Cloud
- **DSL 引擎**：Groovy 4.x
- **前端框架**：Vue 3 + LogicFlow
- **数据库**：MySQL 8.0 + Redis 7.x
- **消息队列**：RabbitMQ / Apache Kafka
- **容器化**：Docker + Kubernetes
- **监控**：Prometheus + Grafana + ELK Stack

## 2. 系统架构设计

### 2.1 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    前端展示层                                │
├─────────────────────────────────────────────────────────────┤
│  LogicFlow 可视化编辑器  │  工作流管理界面  │  监控仪表板    │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    API 网关层                               │
├─────────────────────────────────────────────────────────────┤
│  Spring Cloud Gateway │ 认证授权 │ 限流熔断 │ 路由转发      │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   微服务应用层                              │
├─────────────────────────────────────────────────────────────┤
│ 工作流设计服务 │ 执行引擎服务 │ 监控服务 │ 通知服务 │ 用户服务│
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   基础设施层                                │
├─────────────────────────────────────────────────────────────┤
│   MySQL   │   Redis   │  RabbitMQ  │  文件存储  │  配置中心  │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心服务架构

#### 2.2.1 工作流设计服务 (Workflow Design Service)
- **职责**：工作流定义管理、DSL 解析、版本控制
- **核心组件**：
  - DSL Parser：Groovy DSL 解析器
  - Workflow Repository：工作流存储仓库
  - Version Manager：版本管理器
  - Validation Engine：工作流验证引擎

#### 2.2.2 工作流执行引擎 (Workflow Execution Engine)
- **职责**：工作流实例执行、状态管理、任务调度
- **核心组件**：
  - Execution Coordinator：执行协调器
  - Task Scheduler：任务调度器
  - State Manager：状态管理器
  - Resource Manager：资源管理器

#### 2.2.3 监控告警服务 (Monitoring Service)
- **职责**：执行监控、性能统计、告警通知
- **核心组件**：
  - Metrics Collector：指标收集器
  - Alert Manager：告警管理器
  - Dashboard Service：仪表板服务

## 3. Groovy DSL 语法规范

### 3.1 基础语法结构

```groovy
workflow "订单处理流程" {
    version "1.0.0"
    description "处理用户订单的完整流程"
    
    // 全局变量定义
    variables {
        orderId = ""
        userId = ""
        amount = 0.0
        status = "pending"
    }
    
    // 流程定义
    process {
        // 开始节点
        start "开始" {
            next "验证订单"
        }
        
        // 任务节点
        task "验证订单" {
            type "service"
            service "orderService.validate"
            input {
                orderId = variables.orderId
                userId = variables.userId
            }
            output {
                variables.status = result.status
            }
            timeout 30.seconds
            retry {
                maxAttempts 3
                backoff exponential(1.second, 2.0)
            }
            onSuccess "检查库存"
            onFailure "发送失败通知"
        }
        
        // 条件分支
        decision "检查库存" {
            condition { variables.amount > 1000 }
            onTrue "人工审核"
            onFalse "自动处理"
        }
        
        // 并行网关
        parallel "并行处理" {
            branch "扣减库存" {
                task "库存扣减" {
                    service "inventoryService.deduct"
                }
            }
            branch "生成订单" {
                task "创建订单" {
                    service "orderService.create"
                }
            }
            join "合并结果"
        }
        
        // 延迟任务
        delay "等待支付" {
            duration 30.minutes
            next "检查支付状态"
        }
        
        // 循环节点
        loop "重试支付检查" {
            condition { variables.paymentStatus != "success" && loopCount < 5 }
            task "检查支付" {
                service "paymentService.check"
            }
            delay 1.minute
        }
        
        // 通知节点
        notification "发送通知" {
            email {
                to variables.userEmail
                subject "订单处理完成"
                template "order-completion"
                data variables
            }
            wechat {
                webhook "https://qyapi.weixin.qq.com/cgi-bin/webhook/send"
                message "订单 ${variables.orderId} 处理完成"
            }
        }
        
        // 结束节点
        end "完成" {
            output {
                orderId = variables.orderId
                finalStatus = variables.status
            }
        }
    }
    
    // 异常处理
    errorHandling {
        catch "ValidationException" {
            task "记录验证错误" {
                service "logService.error"
            }
            notification "发送错误通知" {
                email {
                    to "<EMAIL>"
                    subject "订单验证失败"
                }
            }
            end "验证失败结束"
        }
        
        catch "TimeoutException" {
            task "处理超时" {
                service "timeoutHandler.handle"
            }
            retry "重新执行"
        }
    }
    
    // 监听器配置
    listeners {
        onStart {
            log "工作流开始执行: ${workflowId}"
            metrics.counter("workflow.start").increment()
        }
        
        onComplete {
            log "工作流执行完成: ${workflowId}"
            metrics.timer("workflow.duration").record(executionTime)
        }
        
        onError {
            log "工作流执行异常: ${error.message}"
            alert.send("workflow.error", error)
        }
    }
}
```

### 3.2 节点类型定义

#### 3.2.1 任务节点 (Task Node)
```groovy
task "任务名称" {
    type "service" | "script" | "http" | "sql"
    
    // 服务调用
    service "serviceName.methodName"
    
    // 脚本执行
    script {
        language "groovy" | "javascript" | "python"
        code """
            // 脚本内容
        """
    }
    
    // HTTP 调用
    http {
        method "GET" | "POST" | "PUT" | "DELETE"
        url "http://api.example.com/endpoint"
        headers {
            "Content-Type" = "application/json"
            "Authorization" = "Bearer ${token}"
        }
        body variables.requestData
    }
    
    // SQL 执行
    sql {
        datasource "primary"
        query "SELECT * FROM orders WHERE id = ?"
        parameters [variables.orderId]
    }
    
    // 输入输出
    input { /* 输入参数映射 */ }
    output { /* 输出结果映射 */ }
    
    // 执行配置
    timeout 30.seconds
    async true
    
    // 重试策略
    retry {
        maxAttempts 3
        backoff fixed(5.seconds) | exponential(1.second, 2.0) | linear(1.second)
        retryOn ["TimeoutException", "ConnectException"]
        stopOn ["ValidationException"]
    }
    
    // 流转控制
    onSuccess "下一个节点"
    onFailure "错误处理节点"
    onTimeout "超时处理节点"
}
```

#### 3.2.2 条件节点 (Decision Node)
```groovy
decision "条件判断" {
    condition {
        // 支持复杂条件表达式
        variables.amount > 1000 && variables.userLevel == "VIP"
    }

    // 多分支条件
    when {
        case { variables.amount > 10000 } then "高额订单处理"
        case { variables.amount > 1000 } then "中额订单处理"
        default "普通订单处理"
    }

    onTrue "真分支"
    onFalse "假分支"
}
```

#### 3.2.3 并行网关 (Parallel Gateway)
```groovy
parallel "并行执行" {
    // 并行分支定义
    branch "分支1" {
        task "任务1" { /* 任务配置 */ }
        task "任务2" { /* 任务配置 */ }
    }

    branch "分支2" {
        task "任务3" { /* 任务配置 */ }
    }

    // 合并策略
    join "合并点" {
        strategy "waitAll" | "waitAny" | "waitN(2)"
        timeout 5.minutes
        onTimeout "超时处理"
    }

    // 结果聚合
    aggregate {
        results = branches.collect { it.result }
        success = branches.every { it.success }
    }
}
```

#### 3.2.4 循环节点 (Loop Node)
```groovy
loop "循环处理" {
    // 循环条件
    condition { variables.retryCount < 5 && !variables.success }

    // 循环体
    body {
        task "执行任务" { /* 任务配置 */ }

        // 更新循环变量
        update {
            variables.retryCount++
            variables.success = result.success
        }
    }

    // 循环间隔
    interval 30.seconds

    // 最大循环次数
    maxIterations 10

    // 循环完成后
    onComplete "循环结束处理"
    onBreak "循环中断处理"
}
```

#### 3.2.5 子流程节点 (Sub-Process Node)
```groovy
subprocess "子流程调用" {
    workflow "子流程名称"
    version "1.0.0"

    // 输入参数映射
    input {
        subOrderId = variables.orderId
        subUserId = variables.userId
    }

    // 输出参数映射
    output {
        variables.subResult = result.status
    }

    // 执行模式
    mode "sync" | "async"

    // 异步回调
    callback {
        onComplete "子流程完成处理"
        onError "子流程错误处理"
    }
}
```

### 3.3 内置函数和工具类

#### 3.3.1 时间处理函数
```groovy
// 时间单位
30.seconds
5.minutes
2.hours
1.day

// 时间计算
now()
today()
tomorrow()
addDays(date, 7)
formatDate(date, "yyyy-MM-dd HH:mm:ss")
parseDate("2024-01-01", "yyyy-MM-dd")
```

#### 3.3.2 数据处理函数
```groovy
// JSON 处理
json.parse(jsonString)
json.stringify(object)

// XML 处理
xml.parse(xmlString)
xml.stringify(object)

// 字符串处理
string.isEmpty(str)
string.contains(str, substring)
string.format(template, args)

// 集合处理
list.filter(collection) { it.status == "active" }
list.map(collection) { it.name }
list.reduce(collection, initialValue) { acc, item -> acc + item.value }
```

#### 3.3.3 加密和安全函数
```groovy
// 加密解密
crypto.encrypt(data, key)
crypto.decrypt(encryptedData, key)
crypto.hash(data, "SHA256")

// 签名验证
crypto.sign(data, privateKey)
crypto.verify(data, signature, publicKey)

// 随机生成
random.uuid()
random.string(length)
random.number(min, max)
```

## 4. 核心模块详细设计

### 4.1 DSL 解析引擎

#### 4.1.1 解析器架构
```java
@Component
public class WorkflowDslParser {

    private final GroovyShell groovyShell;
    private final WorkflowBuilder workflowBuilder;
    private final ValidationEngine validationEngine;

    public WorkflowDefinition parse(String dslContent) {
        // 1. 词法分析和语法分析
        Script script = groovyShell.parse(dslContent);

        // 2. AST 转换
        WorkflowAST ast = transformToAST(script);

        // 3. 语义分析和验证
        ValidationResult validation = validationEngine.validate(ast);
        if (!validation.isValid()) {
            throw new WorkflowValidationException(validation.getErrors());
        }

        // 4. 构建工作流定义
        return workflowBuilder.build(ast);
    }

    private WorkflowAST transformToAST(Script script) {
        // AST 转换逻辑
        return new ASTTransformer().transform(script);
    }
}
```

#### 4.1.2 工作流定义模型
```java
@Entity
@Table(name = "workflow_definitions")
public class WorkflowDefinition {

    @Id
    private String id;

    @Column(nullable = false)
    private String name;

    @Column(nullable = false)
    private String version;

    @Column(columnDefinition = "TEXT")
    private String description;

    @Column(columnDefinition = "LONGTEXT")
    private String dslContent;

    @Column(columnDefinition = "LONGTEXT")
    private String compiledDefinition;

    @Enumerated(EnumType.STRING)
    private WorkflowStatus status;

    @OneToMany(mappedBy = "workflowDefinition", cascade = CascadeType.ALL)
    private List<WorkflowNode> nodes;

    @OneToMany(mappedBy = "workflowDefinition", cascade = CascadeType.ALL)
    private List<WorkflowEdge> edges;

    // 审计字段
    private String createdBy;
    private LocalDateTime createdAt;
    private String updatedBy;
    private LocalDateTime updatedAt;

    // getters and setters
}
```

### 4.2 工作流执行引擎

#### 4.2.1 执行引擎架构
```java
@Service
public class WorkflowExecutionEngine {

    private final TaskScheduler taskScheduler;
    private final StateManager stateManager;
    private final ResourceManager resourceManager;
    private final EventPublisher eventPublisher;

    public WorkflowInstance execute(String workflowId, Map<String, Object> inputData) {
        // 1. 创建工作流实例
        WorkflowInstance instance = createInstance(workflowId, inputData);

        // 2. 初始化执行上下文
        ExecutionContext context = initializeContext(instance);

        // 3. 开始执行
        executeAsync(context);

        return instance;
    }

    private void executeAsync(ExecutionContext context) {
        CompletableFuture.runAsync(() -> {
            try {
                executeWorkflow(context);
            } catch (Exception e) {
                handleExecutionError(context, e);
            }
        }, taskScheduler.getExecutor());
    }

    private void executeWorkflow(ExecutionContext context) {
        WorkflowNode currentNode = context.getCurrentNode();

        while (currentNode != null && !context.isCompleted()) {
            // 执行当前节点
            NodeExecutionResult result = executeNode(currentNode, context);

            // 更新状态
            stateManager.updateNodeState(context.getInstanceId(),
                                       currentNode.getId(),
                                       result.getStatus());

            // 确定下一个节点
            currentNode = determineNextNode(currentNode, result, context);

            // 发布执行事件
            eventPublisher.publishNodeCompleted(context.getInstanceId(),
                                              currentNode.getId(),
                                              result);
        }

        // 工作流完成
        completeWorkflow(context);
    }
}
```

#### 4.2.2 节点执行器
```java
@Component
public class NodeExecutorFactory {

    private final Map<NodeType, NodeExecutor> executors;

    public NodeExecutor getExecutor(NodeType nodeType) {
        return executors.get(nodeType);
    }
}

public interface NodeExecutor {
    NodeExecutionResult execute(WorkflowNode node, ExecutionContext context);
}

@Component
public class TaskNodeExecutor implements NodeExecutor {

    private final ServiceInvoker serviceInvoker;
    private final ScriptEngine scriptEngine;
    private final HttpClient httpClient;

    @Override
    public NodeExecutionResult execute(WorkflowNode node, ExecutionContext context) {
        TaskNode taskNode = (TaskNode) node;

        try {
            // 执行前置处理
            preprocessTask(taskNode, context);

            // 根据任务类型执行
            Object result = switch (taskNode.getTaskType()) {
                case SERVICE -> executeService(taskNode, context);
                case SCRIPT -> executeScript(taskNode, context);
                case HTTP -> executeHttp(taskNode, context);
                case SQL -> executeSql(taskNode, context);
            };

            // 执行后置处理
            postprocessTask(taskNode, context, result);

            return NodeExecutionResult.success(result);

        } catch (Exception e) {
            return handleTaskError(taskNode, context, e);
        }
    }

    private Object executeService(TaskNode taskNode, ExecutionContext context) {
        String serviceName = taskNode.getServiceName();
        String methodName = taskNode.getMethodName();
        Object[] parameters = prepareParameters(taskNode, context);

        return serviceInvoker.invoke(serviceName, methodName, parameters);
    }
}
```

#### 4.2.3 状态管理器
```java
@Service
public class StateManager {

    private final WorkflowInstanceRepository instanceRepository;
    private final RedisTemplate<String, Object> redisTemplate;
    private final EventPublisher eventPublisher;

    public void updateInstanceState(String instanceId, WorkflowStatus status) {
        // 更新数据库状态
        WorkflowInstance instance = instanceRepository.findById(instanceId)
            .orElseThrow(() -> new WorkflowInstanceNotFoundException(instanceId));

        WorkflowStatus oldStatus = instance.getStatus();
        instance.setStatus(status);
        instance.setUpdatedAt(LocalDateTime.now());
        instanceRepository.save(instance);

        // 更新缓存状态
        String cacheKey = "workflow:instance:" + instanceId;
        redisTemplate.opsForHash().put(cacheKey, "status", status.name());

        // 发布状态变更事件
        eventPublisher.publishStatusChanged(instanceId, oldStatus, status);
    }

    public void updateNodeState(String instanceId, String nodeId, NodeStatus status) {
        // 更新节点执行状态
        NodeExecution execution = new NodeExecution();
        execution.setInstanceId(instanceId);
        execution.setNodeId(nodeId);
        execution.setStatus(status);
        execution.setExecutedAt(LocalDateTime.now());

        nodeExecutionRepository.save(execution);

        // 更新缓存
        String cacheKey = "workflow:instance:" + instanceId + ":nodes";
        redisTemplate.opsForHash().put(cacheKey, nodeId, status.name());
    }
}
```

### 4.3 任务调度器

#### 4.3.1 调度器配置
```java
@Configuration
@EnableScheduling
public class TaskSchedulerConfig {

    @Bean("workflowTaskExecutor")
    public ThreadPoolTaskExecutor workflowTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(50);
        executor.setQueueCapacity(200);
        executor.setThreadNamePrefix("workflow-task-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    @Bean("delayedTaskExecutor")
    public ScheduledThreadPoolExecutor delayedTaskExecutor() {
        ScheduledThreadPoolExecutor executor = new ScheduledThreadPoolExecutor(5);
        executor.setThreadFactory(r -> {
            Thread thread = new Thread(r, "delayed-task-" + System.currentTimeMillis());
            thread.setDaemon(true);
            return thread;
        });
        return executor;
    }
}
```

#### 4.3.2 延迟任务处理
```java
@Service
public class DelayedTaskScheduler {

    private final ScheduledThreadPoolExecutor delayedExecutor;
    private final WorkflowExecutionEngine executionEngine;

    public void scheduleDelayedTask(String instanceId, String nodeId, Duration delay) {
        delayedExecutor.schedule(() -> {
            try {
                executionEngine.resumeExecution(instanceId, nodeId);
            } catch (Exception e) {
                log.error("Failed to resume delayed task: instanceId={}, nodeId={}",
                         instanceId, nodeId, e);
            }
        }, delay.toMillis(), TimeUnit.MILLISECONDS);
    }

    public void scheduleCronTask(String instanceId, String nodeId, String cronExpression) {
        // 使用 Spring 的 CronTrigger 实现定时任务
        CronTrigger trigger = new CronTrigger(cronExpression);

        taskScheduler.schedule(() -> {
            executionEngine.resumeExecution(instanceId, nodeId);
        }, trigger);
    }
}
```

## 5. 数据库设计

### 5.1 核心表结构

#### 5.1.1 工作流定义表
```sql
CREATE TABLE workflow_definitions (
    id VARCHAR(64) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    version VARCHAR(32) NOT NULL,
    description TEXT,
    dsl_content LONGTEXT NOT NULL,
    compiled_definition LONGTEXT,
    status ENUM('DRAFT', 'PUBLISHED', 'DEPRECATED') DEFAULT 'DRAFT',
    category VARCHAR(100),
    tags JSON,
    created_by VARCHAR(64) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(64),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_name_version (name, version),
    INDEX idx_status (status),
    INDEX idx_category (category),
    INDEX idx_created_by (created_by)
);
```

#### 5.1.2 工作流实例表
```sql
CREATE TABLE workflow_instances (
    id VARCHAR(64) PRIMARY KEY,
    workflow_definition_id VARCHAR(64) NOT NULL,
    name VARCHAR(255),
    status ENUM('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED', 'SUSPENDED') DEFAULT 'PENDING',
    input_data JSON,
    output_data JSON,
    variables JSON,
    current_node_id VARCHAR(64),
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    duration_ms BIGINT,
    created_by VARCHAR(64),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (workflow_definition_id) REFERENCES workflow_definitions(id),
    INDEX idx_status (status),
    INDEX idx_workflow_definition (workflow_definition_id),
    INDEX idx_created_by (created_by),
    INDEX idx_start_time (start_time)
);
```

#### 5.1.3 节点执行记录表
```sql
CREATE TABLE node_executions (
    id VARCHAR(64) PRIMARY KEY,
    instance_id VARCHAR(64) NOT NULL,
    node_id VARCHAR(64) NOT NULL,
    node_name VARCHAR(255),
    node_type ENUM('START', 'END', 'TASK', 'DECISION', 'PARALLEL', 'LOOP', 'SUBPROCESS', 'NOTIFICATION') NOT NULL,
    status ENUM('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'SKIPPED', 'CANCELLED') DEFAULT 'PENDING',
    input_data JSON,
    output_data JSON,
    error_message TEXT,
    retry_count INT DEFAULT 0,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    duration_ms BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (instance_id) REFERENCES workflow_instances(id) ON DELETE CASCADE,
    INDEX idx_instance_node (instance_id, node_id),
    INDEX idx_status (status),
    INDEX idx_start_time (start_time)
);
```

#### 5.1.4 工作流版本管理表
```sql
CREATE TABLE workflow_versions (
    id VARCHAR(64) PRIMARY KEY,
    workflow_name VARCHAR(255) NOT NULL,
    version VARCHAR(32) NOT NULL,
    definition_id VARCHAR(64) NOT NULL,
    change_log TEXT,
    is_active BOOLEAN DEFAULT FALSE,
    published_by VARCHAR(64),
    published_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (definition_id) REFERENCES workflow_definitions(id),
    UNIQUE KEY uk_name_version (workflow_name, version),
    INDEX idx_workflow_name (workflow_name),
    INDEX idx_is_active (is_active)
);
```

#### 5.1.5 任务队列表
```sql
CREATE TABLE task_queue (
    id VARCHAR(64) PRIMARY KEY,
    instance_id VARCHAR(64) NOT NULL,
    node_id VARCHAR(64) NOT NULL,
    task_type VARCHAR(50) NOT NULL,
    priority INT DEFAULT 0,
    scheduled_time TIMESTAMP NOT NULL,
    max_retry_count INT DEFAULT 3,
    retry_count INT DEFAULT 0,
    status ENUM('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED') DEFAULT 'PENDING',
    task_data JSON,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_status_scheduled (status, scheduled_time),
    INDEX idx_instance_id (instance_id),
    INDEX idx_priority (priority DESC)
);
```

### 5.2 监控和审计表

#### 5.2.1 执行日志表
```sql
CREATE TABLE execution_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    instance_id VARCHAR(64) NOT NULL,
    node_id VARCHAR(64),
    log_level ENUM('DEBUG', 'INFO', 'WARN', 'ERROR') DEFAULT 'INFO',
    message TEXT NOT NULL,
    details JSON,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_instance_id (instance_id),
    INDEX idx_timestamp (timestamp),
    INDEX idx_log_level (log_level)
);
```

#### 5.2.2 性能指标表
```sql
CREATE TABLE performance_metrics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    metric_name VARCHAR(100) NOT NULL,
    metric_type ENUM('COUNTER', 'GAUGE', 'HISTOGRAM', 'TIMER') NOT NULL,
    metric_value DECIMAL(20,6) NOT NULL,
    tags JSON,
    instance_id VARCHAR(64),
    node_id VARCHAR(64),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_metric_name (metric_name),
    INDEX idx_timestamp (timestamp),
    INDEX idx_instance_id (instance_id)
);
```

## 6. API 接口设计

### 6.1 工作流管理 API

#### 6.1.1 工作流定义管理
```java
@RestController
@RequestMapping("/api/v1/workflows")
@Validated
public class WorkflowController {

    private final WorkflowService workflowService;

    /**
     * 创建工作流定义
     */
    @PostMapping
    public ResponseEntity<WorkflowDefinitionDTO> createWorkflow(
            @Valid @RequestBody CreateWorkflowRequest request) {

        WorkflowDefinitionDTO workflow = workflowService.createWorkflow(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(workflow);
    }

    /**
     * 更新工作流定义
     */
    @PutMapping("/{workflowId}")
    public ResponseEntity<WorkflowDefinitionDTO> updateWorkflow(
            @PathVariable String workflowId,
            @Valid @RequestBody UpdateWorkflowRequest request) {

        WorkflowDefinitionDTO workflow = workflowService.updateWorkflow(workflowId, request);
        return ResponseEntity.ok(workflow);
    }

    /**
     * 发布工作流版本
     */
    @PostMapping("/{workflowId}/publish")
    public ResponseEntity<WorkflowVersionDTO> publishWorkflow(
            @PathVariable String workflowId,
            @Valid @RequestBody PublishWorkflowRequest request) {

        WorkflowVersionDTO version = workflowService.publishWorkflow(workflowId, request);
        return ResponseEntity.ok(version);
    }

    /**
     * 获取工作流列表
     */
    @GetMapping
    public ResponseEntity<PageResult<WorkflowDefinitionDTO>> getWorkflows(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String keyword) {

        WorkflowQueryRequest query = WorkflowQueryRequest.builder()
            .page(page)
            .size(size)
            .category(category)
            .status(status)
            .keyword(keyword)
            .build();

        PageResult<WorkflowDefinitionDTO> result = workflowService.getWorkflows(query);
        return ResponseEntity.ok(result);
    }

    /**
     * 获取工作流详情
     */
    @GetMapping("/{workflowId}")
    public ResponseEntity<WorkflowDefinitionDTO> getWorkflow(@PathVariable String workflowId) {
        WorkflowDefinitionDTO workflow = workflowService.getWorkflow(workflowId);
        return ResponseEntity.ok(workflow);
    }

    /**
     * 删除工作流
     */
    @DeleteMapping("/{workflowId}")
    public ResponseEntity<Void> deleteWorkflow(@PathVariable String workflowId) {
        workflowService.deleteWorkflow(workflowId);
        return ResponseEntity.noContent().build();
    }

    /**
     * 验证工作流 DSL
     */
    @PostMapping("/validate")
    public ResponseEntity<ValidationResult> validateWorkflow(
            @Valid @RequestBody ValidateWorkflowRequest request) {

        ValidationResult result = workflowService.validateDsl(request.getDslContent());
        return ResponseEntity.ok(result);
    }
}
```

#### 6.1.2 工作流执行 API
```java
@RestController
@RequestMapping("/api/v1/executions")
public class WorkflowExecutionController {

    private final WorkflowExecutionService executionService;

    /**
     * 启动工作流实例
     */
    @PostMapping("/start")
    public ResponseEntity<WorkflowInstanceDTO> startWorkflow(
            @Valid @RequestBody StartWorkflowRequest request) {

        WorkflowInstanceDTO instance = executionService.startWorkflow(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(instance);
    }

    /**
     * 暂停工作流实例
     */
    @PostMapping("/{instanceId}/suspend")
    public ResponseEntity<Void> suspendWorkflow(@PathVariable String instanceId) {
        executionService.suspendWorkflow(instanceId);
        return ResponseEntity.ok().build();
    }

    /**
     * 恢复工作流实例
     */
    @PostMapping("/{instanceId}/resume")
    public ResponseEntity<Void> resumeWorkflow(@PathVariable String instanceId) {
        executionService.resumeWorkflow(instanceId);
        return ResponseEntity.ok().build();
    }

    /**
     * 取消工作流实例
     */
    @PostMapping("/{instanceId}/cancel")
    public ResponseEntity<Void> cancelWorkflow(@PathVariable String instanceId) {
        executionService.cancelWorkflow(instanceId);
        return ResponseEntity.ok().build();
    }

    /**
     * 获取工作流实例详情
     */
    @GetMapping("/{instanceId}")
    public ResponseEntity<WorkflowInstanceDTO> getInstance(@PathVariable String instanceId) {
        WorkflowInstanceDTO instance = executionService.getInstance(instanceId);
        return ResponseEntity.ok(instance);
    }

    /**
     * 获取工作流实例列表
     */
    @GetMapping
    public ResponseEntity<PageResult<WorkflowInstanceDTO>> getInstances(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String workflowId,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String createdBy) {

        InstanceQueryRequest query = InstanceQueryRequest.builder()
            .page(page)
            .size(size)
            .workflowId(workflowId)
            .status(status)
            .createdBy(createdBy)
            .build();

        PageResult<WorkflowInstanceDTO> result = executionService.getInstances(query);
        return ResponseEntity.ok(result);
    }

    /**
     * 获取节点执行历史
     */
    @GetMapping("/{instanceId}/nodes")
    public ResponseEntity<List<NodeExecutionDTO>> getNodeExecutions(
            @PathVariable String instanceId) {

        List<NodeExecutionDTO> executions = executionService.getNodeExecutions(instanceId);
        return ResponseEntity.ok(executions);
    }
}
```

## 7. LogicFlow 前端集成方案

### 7.1 前端架构设计

#### 7.1.1 技术栈选择
```json
{
  "dependencies": {
    "vue": "^3.3.0",
    "vue-router": "^4.2.0",
    "pinia": "^2.1.0",
    "@logicflow/core": "^1.2.0",
    "@logicflow/extension": "^1.2.0",
    "element-plus": "^2.3.0",
    "axios": "^1.4.0",
    "monaco-editor": "^0.39.0",
    "echarts": "^5.4.0"
  },
  "devDependencies": {
    "vite": "^4.3.0",
    "@vitejs/plugin-vue": "^4.2.0",
    "typescript": "^5.0.0",
    "sass": "^1.62.0"
  }
}
```

#### 7.1.2 LogicFlow 配置和扩展
```typescript
// src/components/WorkflowDesigner/index.vue
<template>
  <div class="workflow-designer">
    <div class="designer-toolbar">
      <el-button-group>
        <el-button @click="save">保存</el-button>
        <el-button @click="validate">验证</el-button>
        <el-button @click="preview">预览</el-button>
        <el-button @click="deploy">发布</el-button>
      </el-button-group>
    </div>

    <div class="designer-content">
      <!-- 节点面板 -->
      <div class="node-panel">
        <NodePalette @drag-node="handleDragNode" />
      </div>

      <!-- 画布区域 -->
      <div class="canvas-container">
        <div ref="containerRef" class="logic-flow-container"></div>
      </div>

      <!-- 属性面板 -->
      <div class="property-panel">
        <PropertyEditor
          :selected-element="selectedElement"
          @update-properties="handleUpdateProperties"
        />
      </div>
    </div>

    <!-- DSL 编辑器 -->
    <el-drawer v-model="showDslEditor" title="DSL 编辑器" size="50%">
      <DslEditor
        v-model="dslContent"
        @save="handleDslSave"
      />
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import LogicFlow from '@logicflow/core'
import { BpmnElement, Menu, SelectionSelect } from '@logicflow/extension'
import { WorkflowDesignerConfig } from './config'
import { registerCustomNodes } from './nodes'
import { workflowApi } from '@/api/workflow'

// LogicFlow 实例
let lf: LogicFlow | null = null
const containerRef = ref<HTMLElement>()

// 状态管理
const selectedElement = ref(null)
const dslContent = ref('')
const showDslEditor = ref(false)

// 初始化 LogicFlow
onMounted(async () => {
  await nextTick()

  if (containerRef.value) {
    // 创建 LogicFlow 实例
    lf = new LogicFlow({
      container: containerRef.value,
      ...WorkflowDesignerConfig,
      plugins: [BpmnElement, Menu, SelectionSelect]
    })

    // 注册自定义节点
    registerCustomNodes(lf)

    // 设置事件监听
    setupEventListeners()

    // 渲染画布
    lf.render()
  }
})

// 设置事件监听器
const setupEventListeners = () => {
  if (!lf) return

  // 节点选择事件
  lf.on('selection:selected', ({ data }) => {
    selectedElement.value = data
  })

  // 节点属性变更事件
  lf.on('node:properties-change', ({ data }) => {
    updateNodeProperties(data)
  })

  // 连线创建事件
  lf.on('edge:add', ({ data }) => {
    validateConnection(data)
  })
}

// 保存工作流
const save = async () => {
  if (!lf) return

  const graphData = lf.getGraphData()
  const dsl = await convertToDsl(graphData)

  try {
    await workflowApi.saveWorkflow({
      name: 'workflow-name',
      dslContent: dsl,
      graphData: graphData
    })

    ElMessage.success('保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

// 转换为 DSL
const convertToDsl = async (graphData: any): Promise<string> => {
  // 调用后端 API 将图形数据转换为 DSL
  const response = await workflowApi.convertToDsl(graphData)
  return response.data.dslContent
}
</script>
```

#### 7.1.3 自定义节点定义
```typescript
// src/components/WorkflowDesigner/nodes/index.ts
import LogicFlow from '@logicflow/core'
import { TaskNode } from './TaskNode'
import { DecisionNode } from './DecisionNode'
import { ParallelNode } from './ParallelNode'
import { NotificationNode } from './NotificationNode'

export const registerCustomNodes = (lf: LogicFlow) => {
  // 注册任务节点
  lf.register(TaskNode)

  // 注册决策节点
  lf.register(DecisionNode)

  // 注册并行网关
  lf.register(ParallelNode)

  // 注册通知节点
  lf.register(NotificationNode)
}

// src/components/WorkflowDesigner/nodes/TaskNode.ts
import { RectNode, RectNodeModel } from '@logicflow/core'

class TaskNodeModel extends RectNodeModel {
  static type = 'task-node'

  constructor(data: any, graphModel: any) {
    super(data, graphModel)

    // 设置节点样式
    this.width = 120
    this.height = 80
    this.radius = 8
  }

  // 设置节点样式
  getNodeStyle() {
    return {
      fill: '#E3F2FD',
      stroke: '#1976D2',
      strokeWidth: 2
    }
  }

  // 设置文本样式
  getTextStyle() {
    return {
      fontSize: 12,
      fill: '#333'
    }
  }

  // 设置锚点
  getDefaultAnchor() {
    return [
      { x: this.x, y: this.y - this.height / 2, type: 'top' },
      { x: this.x + this.width / 2, y: this.y, type: 'right' },
      { x: this.x, y: this.y + this.height / 2, type: 'bottom' },
      { x: this.x - this.width / 2, y: this.y, type: 'left' }
    ]
  }
}

class TaskNodeView extends RectNode {
  // 自定义节点渲染
  getShape() {
    const { model } = this.props
    const { x, y, width, height, radius } = model

    return h('g', {}, [
      h('rect', {
        x: x - width / 2,
        y: y - height / 2,
        width,
        height,
        rx: radius,
        ry: radius,
        ...model.getNodeStyle()
      }),
      h('text', {
        x,
        y,
        textAnchor: 'middle',
        dominantBaseline: 'middle',
        ...model.getTextStyle()
      }, model.text?.value || 'Task')
    ])
  }
}

export const TaskNode = {
  type: 'task-node',
  view: TaskNodeView,
  model: TaskNodeModel
}
```

### 7.2 DSL 与图形的双向转换

#### 7.2.1 图形转 DSL
```typescript
// src/utils/dslConverter.ts
export class DslConverter {

  /**
   * 将 LogicFlow 图形数据转换为 Groovy DSL
   */
  static convertToDsl(graphData: GraphData): string {
    const { nodes, edges } = graphData

    // 构建 DSL 结构
    const dslBuilder = new DslBuilder()

    // 添加工作流头部信息
    dslBuilder.addHeader({
      name: graphData.name || 'Untitled Workflow',
      version: graphData.version || '1.0.0',
      description: graphData.description || ''
    })

    // 添加变量定义
    if (graphData.variables) {
      dslBuilder.addVariables(graphData.variables)
    }

    // 开始处理流程
    dslBuilder.startProcess()

    // 处理节点
    nodes.forEach(node => {
      this.convertNode(node, dslBuilder)
    })

    // 处理连线关系
    edges.forEach(edge => {
      this.convertEdge(edge, dslBuilder)
    })

    dslBuilder.endProcess()

    return dslBuilder.build()
  }

  /**
   * 转换节点
   */
  private static convertNode(node: NodeData, builder: DslBuilder) {
    switch (node.type) {
      case 'start-node':
        builder.addStartNode(node)
        break
      case 'task-node':
        builder.addTaskNode(node)
        break
      case 'decision-node':
        builder.addDecisionNode(node)
        break
      case 'parallel-node':
        builder.addParallelNode(node)
        break
      case 'notification-node':
        builder.addNotificationNode(node)
        break
      case 'end-node':
        builder.addEndNode(node)
        break
    }
  }

  /**
   * 将 DSL 转换为 LogicFlow 图形数据
   */
  static convertToGraph(dslContent: string): GraphData {
    // 解析 DSL
    const ast = this.parseDsl(dslContent)

    const nodes: NodeData[] = []
    const edges: EdgeData[] = []

    // 转换节点
    ast.nodes.forEach((astNode, index) => {
      const node = this.convertAstToNode(astNode, index)
      nodes.push(node)
    })

    // 转换连线
    ast.edges.forEach(astEdge => {
      const edge = this.convertAstToEdge(astEdge)
      edges.push(edge)
    })

    return {
      nodes,
      edges,
      name: ast.name,
      version: ast.version,
      description: ast.description
    }
  }
}

class DslBuilder {
  private dsl: string[] = []

  addHeader(header: { name: string; version: string; description: string }) {
    this.dsl.push(`workflow "${header.name}" {`)
    this.dsl.push(`    version "${header.version}"`)
    if (header.description) {
      this.dsl.push(`    description "${header.description}"`)
    }
    this.dsl.push('')
  }

  addTaskNode(node: NodeData) {
    const props = node.properties || {}

    this.dsl.push(`    task "${node.text?.value || node.id}" {`)

    if (props.serviceType) {
      this.dsl.push(`        type "${props.serviceType}"`)
    }

    if (props.serviceName) {
      this.dsl.push(`        service "${props.serviceName}"`)
    }

    if (props.timeout) {
      this.dsl.push(`        timeout ${props.timeout}`)
    }

    this.dsl.push(`    }`)
    this.dsl.push('')
  }

  build(): string {
    this.dsl.push('}')
    return this.dsl.join('\n')
  }
}
```

## 8. 部署和运维方案

### 8.1 容器化部署

#### 8.1.1 Docker 配置
```dockerfile
# Dockerfile
FROM openjdk:17-jdk-slim

LABEL maintainer="<EMAIL>"
LABEL version="1.0.0"

# 设置工作目录
WORKDIR /app

# 复制应用文件
COPY target/workflow-engine-*.jar app.jar

# 设置环境变量
ENV JAVA_OPTS="-Xmx2g -Xms1g -XX:+UseG1GC"
ENV SPRING_PROFILES_ACTIVE=prod

# 暴露端口
EXPOSE 8080 8081

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8081/actuator/health || exit 1

# 启动应用
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
```

#### 8.1.2 Docker Compose 配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  # 工作流引擎服务
  workflow-engine:
    build: .
    ports:
      - "8080:8080"
      - "8081:8081"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
      - RABBITMQ_HOST=rabbitmq
    depends_on:
      - mysql
      - redis
      - rabbitmq
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
    networks:
      - workflow-network
    restart: unless-stopped

  # MySQL 数据库
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: workflow_engine
      MYSQL_USER: workflow
      MYSQL_PASSWORD: workflow123
    ports:
      - "3306:3306"
    volumes:
      - mysql-data:/var/lib/mysql
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - workflow-network
    restart: unless-stopped

  # Redis 缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - workflow-network
    restart: unless-stopped

  # RabbitMQ 消息队列
  rabbitmq:
    image: rabbitmq:3-management
    environment:
      RABBITMQ_DEFAULT_USER: workflow
      RABBITMQ_DEFAULT_PASS: workflow123
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq-data:/var/lib/rabbitmq
    networks:
      - workflow-network
    restart: unless-stopped

  # 前端应用
  workflow-frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - workflow-engine
    networks:
      - workflow-network
    restart: unless-stopped

volumes:
  mysql-data:
  redis-data:
  rabbitmq-data:

networks:
  workflow-network:
    driver: bridge
```

### 8.2 Kubernetes 部署

#### 8.2.1 应用部署配置
```yaml
# k8s/workflow-engine-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: workflow-engine
  namespace: workflow
  labels:
    app: workflow-engine
    version: v1.0.0
spec:
  replicas: 3
  selector:
    matchLabels:
      app: workflow-engine
  template:
    metadata:
      labels:
        app: workflow-engine
        version: v1.0.0
    spec:
      containers:
      - name: workflow-engine
        image: workflow-engine:1.0.0
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 8081
          name: management
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "k8s"
        - name: MYSQL_HOST
          value: "mysql-service"
        - name: REDIS_HOST
          value: "redis-service"
        - name: RABBITMQ_HOST
          value: "rabbitmq-service"
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /actuator/health/liveness
            port: 8081
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 8081
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
        - name: logs-volume
          mountPath: /app/logs
      volumes:
      - name: config-volume
        configMap:
          name: workflow-config
      - name: logs-volume
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: workflow-engine-service
  namespace: workflow
spec:
  selector:
    app: workflow-engine
  ports:
  - name: http
    port: 8080
    targetPort: 8080
  - name: management
    port: 8081
    targetPort: 8081
  type: ClusterIP
```

#### 8.2.2 配置管理
```yaml
# k8s/workflow-configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: workflow-config
  namespace: workflow
data:
  application-k8s.yml: |
    server:
      port: 8080

    management:
      server:
        port: 8081
      endpoints:
        web:
          exposure:
            include: health,info,metrics,prometheus

    spring:
      datasource:
        url: jdbc:mysql://${MYSQL_HOST}:3306/workflow_engine?useSSL=false&serverTimezone=UTC
        username: ${MYSQL_USERNAME}
        password: ${MYSQL_PASSWORD}
        driver-class-name: com.mysql.cj.jdbc.Driver
        hikari:
          maximum-pool-size: 20
          minimum-idle: 5
          connection-timeout: 30000
          idle-timeout: 600000
          max-lifetime: 1800000

      redis:
        host: ${REDIS_HOST}
        port: 6379
        password: ${REDIS_PASSWORD}
        database: 0
        jedis:
          pool:
            max-active: 20
            max-idle: 10
            min-idle: 5

      rabbitmq:
        host: ${RABBITMQ_HOST}
        port: 5672
        username: ${RABBITMQ_USERNAME}
        password: ${RABBITMQ_PASSWORD}
        virtual-host: /

    workflow:
      engine:
        thread-pool:
          core-size: 10
          max-size: 50
          queue-capacity: 200
        execution:
          timeout: 3600
          retry:
            max-attempts: 3
            backoff-delay: 1000

    logging:
      level:
        com.company.workflow: INFO
        org.springframework: WARN
      pattern:
        console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
        file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
      file:
        name: /app/logs/workflow-engine.log
        max-size: 100MB
        max-history: 30
---
apiVersion: v1
kind: Secret
metadata:
  name: workflow-secrets
  namespace: workflow
type: Opaque
data:
  mysql-username: d29ya2Zsb3c=  # workflow (base64)
  mysql-password: d29ya2Zsb3cxMjM=  # workflow123 (base64)
  redis-password: ""
  rabbitmq-username: d29ya2Zsb3c=  # workflow (base64)
  rabbitmq-password: d29ya2Zsb3cxMjM=  # workflow123 (base64)
```

#### 8.2.3 水平扩展配置
```yaml
# k8s/workflow-hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: workflow-engine-hpa
  namespace: workflow
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: workflow-engine
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
```

### 8.3 监控告警系统

#### 8.3.1 Prometheus 监控配置
```yaml
# monitoring/prometheus-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: monitoring
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s

    rule_files:
      - "workflow_rules.yml"

    alerting:
      alertmanagers:
        - static_configs:
            - targets:
              - alertmanager:9093

    scrape_configs:
      - job_name: 'workflow-engine'
        static_configs:
          - targets: ['workflow-engine-service:8081']
        metrics_path: '/actuator/prometheus'
        scrape_interval: 30s
        scrape_timeout: 10s

      - job_name: 'mysql'
        static_configs:
          - targets: ['mysql-exporter:9104']

      - job_name: 'redis'
        static_configs:
          - targets: ['redis-exporter:9121']

      - job_name: 'rabbitmq'
        static_configs:
          - targets: ['rabbitmq-exporter:9419']

  workflow_rules.yml: |
    groups:
    - name: workflow.rules
      rules:
      # 工作流执行失败率告警
      - alert: WorkflowHighFailureRate
        expr: rate(workflow_executions_failed_total[5m]) / rate(workflow_executions_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "工作流执行失败率过高"
          description: "工作流执行失败率在过去5分钟内超过10%"

      # 工作流执行时间过长告警
      - alert: WorkflowLongExecution
        expr: histogram_quantile(0.95, rate(workflow_execution_duration_seconds_bucket[5m])) > 3600
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "工作流执行时间过长"
          description: "95%的工作流执行时间超过1小时"

      # 任务队列积压告警
      - alert: TaskQueueBacklog
        expr: workflow_task_queue_size > 1000
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "任务队列积压严重"
          description: "任务队列中待处理任务数量超过1000个"

      # 应用实例下线告警
      - alert: WorkflowInstanceDown
        expr: up{job="workflow-engine"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "工作流引擎实例下线"
          description: "工作流引擎实例 {{ $labels.instance }} 已下线"
```

#### 8.3.2 Grafana 仪表板配置
```json
{
  "dashboard": {
    "id": null,
    "title": "工作流引擎监控仪表板",
    "tags": ["workflow", "monitoring"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "工作流执行统计",
        "type": "stat",
        "targets": [
          {
            "expr": "sum(rate(workflow_executions_total[5m]))",
            "legendFormat": "执行速率 (次/秒)"
          },
          {
            "expr": "sum(workflow_executions_running)",
            "legendFormat": "运行中实例"
          },
          {
            "expr": "sum(rate(workflow_executions_completed_total[5m]))",
            "legendFormat": "完成速率 (次/秒)"
          },
          {
            "expr": "sum(rate(workflow_executions_failed_total[5m]))",
            "legendFormat": "失败速率 (次/秒)"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
      },
      {
        "id": 2,
        "title": "工作流执行时间分布",
        "type": "heatmap",
        "targets": [
          {
            "expr": "rate(workflow_execution_duration_seconds_bucket[5m])",
            "legendFormat": "{{le}}"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
      },
      {
        "id": 3,
        "title": "系统资源使用情况",
        "type": "graph",
        "targets": [
          {
            "expr": "process_cpu_usage",
            "legendFormat": "CPU 使用率"
          },
          {
            "expr": "jvm_memory_used_bytes / jvm_memory_max_bytes",
            "legendFormat": "内存使用率"
          }
        ],
        "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "30s"
  }
}
```

## 9. 安全机制设计

### 9.1 认证授权体系

#### 9.1.1 JWT 认证配置
```java
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class SecurityConfig {

    private final JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;
    private final JwtRequestFilter jwtRequestFilter;

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public AuthenticationManager authenticationManager(
            AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.csrf().disable()
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/api/v1/auth/**").permitAll()
                .requestMatchers("/actuator/health").permitAll()
                .requestMatchers("/api/v1/workflows/**").hasRole("WORKFLOW_USER")
                .requestMatchers("/api/v1/admin/**").hasRole("ADMIN")
                .anyRequest().authenticated()
            )
            .exceptionHandling().authenticationEntryPoint(jwtAuthenticationEntryPoint)
            .and()
            .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS);

        http.addFilterBefore(jwtRequestFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }
}
```

#### 9.1.2 权限控制模型
```java
@Entity
@Table(name = "users")
public class User {
    @Id
    private String id;

    @Column(unique = true, nullable = false)
    private String username;

    @Column(nullable = false)
    private String password;

    @Column(unique = true, nullable = false)
    private String email;

    @Enumerated(EnumType.STRING)
    private UserStatus status;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
        name = "user_roles",
        joinColumns = @JoinColumn(name = "user_id"),
        inverseJoinColumns = @JoinColumn(name = "role_id")
    )
    private Set<Role> roles = new HashSet<>();
}

@Entity
@Table(name = "roles")
public class Role {
    @Id
    private String id;

    @Column(unique = true, nullable = false)
    private String name;

    private String description;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
        name = "role_permissions",
        joinColumns = @JoinColumn(name = "role_id"),
        inverseJoinColumns = @JoinColumn(name = "permission_id")
    )
    private Set<Permission> permissions = new HashSet<>();
}

@Entity
@Table(name = "permissions")
public class Permission {
    @Id
    private String id;

    @Column(unique = true, nullable = false)
    private String name;

    private String description;

    @Enumerated(EnumType.STRING)
    private ResourceType resourceType;

    @Enumerated(EnumType.STRING)
    private ActionType actionType;
}
```

### 9.2 数据安全

#### 9.2.1 敏感数据加密
```java
@Service
public class EncryptionService {

    private final AESUtil aesUtil;
    private final RSAUtil rsaUtil;

    @Value("${workflow.security.encryption.key}")
    private String encryptionKey;

    /**
     * 加密敏感数据
     */
    public String encryptSensitiveData(String data) {
        try {
            return aesUtil.encrypt(data, encryptionKey);
        } catch (Exception e) {
            throw new EncryptionException("Failed to encrypt sensitive data", e);
        }
    }

    /**
     * 解密敏感数据
     */
    public String decryptSensitiveData(String encryptedData) {
        try {
            return aesUtil.decrypt(encryptedData, encryptionKey);
        } catch (Exception e) {
            throw new DecryptionException("Failed to decrypt sensitive data", e);
        }
    }

    /**
     * 数据脱敏
     */
    public String maskSensitiveData(String data, SensitiveType type) {
        return switch (type) {
            case EMAIL -> maskEmail(data);
            case PHONE -> maskPhone(data);
            case ID_CARD -> maskIdCard(data);
            case BANK_CARD -> maskBankCard(data);
            default -> data;
        };
    }
}
```

#### 9.2.2 审计日志
```java
@Entity
@Table(name = "audit_logs")
public class AuditLog {
    @Id
    private String id;

    private String userId;
    private String username;
    private String action;
    private String resourceType;
    private String resourceId;
    private String details;
    private String ipAddress;
    private String userAgent;
    private LocalDateTime timestamp;

    @Enumerated(EnumType.STRING)
    private AuditResult result;
}

@Component
@Slf4j
public class AuditLogger {

    private final AuditLogRepository auditLogRepository;

    @EventListener
    public void handleWorkflowCreated(WorkflowCreatedEvent event) {
        AuditLog log = AuditLog.builder()
            .userId(event.getUserId())
            .username(event.getUsername())
            .action("CREATE_WORKFLOW")
            .resourceType("WORKFLOW")
            .resourceId(event.getWorkflowId())
            .details(event.getDetails())
            .ipAddress(event.getIpAddress())
            .timestamp(LocalDateTime.now())
            .result(AuditResult.SUCCESS)
            .build();

        auditLogRepository.save(log);
    }

    @EventListener
    public void handleWorkflowExecuted(WorkflowExecutedEvent event) {
        AuditLog log = AuditLog.builder()
            .userId(event.getUserId())
            .username(event.getUsername())
            .action("EXECUTE_WORKFLOW")
            .resourceType("WORKFLOW_INSTANCE")
            .resourceId(event.getInstanceId())
            .details(event.getDetails())
            .timestamp(LocalDateTime.now())
            .result(event.isSuccess() ? AuditResult.SUCCESS : AuditResult.FAILURE)
            .build();

        auditLogRepository.save(log);
    }
}
```

## 10. 性能优化策略

### 10.1 缓存策略

#### 10.1.1 多级缓存架构
```java
@Configuration
@EnableCaching
public class CacheConfig {

    @Bean
    public CacheManager cacheManager() {
        RedisCacheManager.Builder builder = RedisCacheManager
            .RedisCacheManagerBuilder
            .fromConnectionFactory(redisConnectionFactory())
            .cacheDefaults(cacheConfiguration());

        return builder.build();
    }

    private RedisCacheConfiguration cacheConfiguration() {
        return RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(30))
            .serializeKeysWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new GenericJackson2JsonRedisSerializer()));
    }
}

@Service
public class WorkflowCacheService {

    @Cacheable(value = "workflow-definitions", key = "#workflowId")
    public WorkflowDefinition getWorkflowDefinition(String workflowId) {
        return workflowRepository.findById(workflowId)
            .orElseThrow(() -> new WorkflowNotFoundException(workflowId));
    }

    @Cacheable(value = "workflow-instances", key = "#instanceId")
    public WorkflowInstance getWorkflowInstance(String instanceId) {
        return instanceRepository.findById(instanceId)
            .orElseThrow(() -> new WorkflowInstanceNotFoundException(instanceId));
    }

    @CacheEvict(value = "workflow-definitions", key = "#workflowId")
    public void evictWorkflowDefinition(String workflowId) {
        // 缓存失效
    }
}
```

### 10.2 数据库优化

#### 10.2.1 分库分表策略
```java
@Configuration
public class ShardingConfig {

    @Bean
    public DataSource dataSource() {
        Map<String, DataSource> dataSourceMap = new HashMap<>();

        // 主库
        dataSourceMap.put("master", createDataSource("master"));

        // 分片数据源
        for (int i = 0; i < 4; i++) {
            dataSourceMap.put("shard" + i, createDataSource("shard" + i));
        }

        // 分片规则
        ShardingRuleConfiguration shardingRuleConfig = new ShardingRuleConfiguration();

        // 工作流实例分片规则
        TableRuleConfiguration instanceTableRule = new TableRuleConfiguration(
            "workflow_instances",
            "shard${0..3}.workflow_instances_${0..15}"
        );
        instanceTableRule.setDatabaseShardingStrategyConfig(
            new InlineShardingStrategyConfiguration("created_by", "shard${created_by.hashCode() % 4}")
        );
        instanceTableRule.setTableShardingStrategyConfig(
            new InlineShardingStrategyConfiguration("id", "workflow_instances_${id.hashCode() % 16}")
        );

        shardingRuleConfig.getTableRuleConfigs().add(instanceTableRule);

        return ShardingDataSourceFactory.createDataSource(dataSourceMap, shardingRuleConfig, new Properties());
    }
}
```

### 10.3 异步处理优化

#### 10.3.1 消息队列集成
```java
@Configuration
@EnableRabbit
public class RabbitConfig {

    public static final String WORKFLOW_EXCHANGE = "workflow.exchange";
    public static final String TASK_QUEUE = "workflow.task.queue";
    public static final String NOTIFICATION_QUEUE = "workflow.notification.queue";

    @Bean
    public TopicExchange workflowExchange() {
        return new TopicExchange(WORKFLOW_EXCHANGE, true, false);
    }

    @Bean
    public Queue taskQueue() {
        return QueueBuilder.durable(TASK_QUEUE)
            .withArgument("x-max-priority", 10)
            .withArgument("x-message-ttl", 3600000)
            .build();
    }

    @Bean
    public Binding taskBinding() {
        return BindingBuilder.bind(taskQueue())
            .to(workflowExchange())
            .with("workflow.task.*");
    }

    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setMessageConverter(new Jackson2JsonMessageConverter());
        template.setConfirmCallback((correlationData, ack, cause) -> {
            if (!ack) {
                log.error("Message send failed: {}", cause);
            }
        });
        return template;
    }
}

@Component
@RabbitListener(queues = RabbitConfig.TASK_QUEUE)
public class TaskMessageHandler {

    private final WorkflowExecutionEngine executionEngine;

    @RabbitHandler
    public void handleTaskMessage(TaskMessage message) {
        try {
            executionEngine.executeTask(message.getInstanceId(), message.getNodeId());
        } catch (Exception e) {
            log.error("Failed to execute task: {}", message, e);
            throw new AmqpRejectAndDontRequeueException("Task execution failed", e);
        }
    }
}
```

## 11. 总结

### 11.1 系统特点

本企业级工作流引擎系统具有以下核心特点：

1. **强大的 DSL 支持**：基于 Groovy 的直观 DSL 语法，支持复杂业务逻辑表达
2. **可视化设计**：集成 LogicFlow 提供专业的流程设计体验
3. **高性能执行**：支持并行、异步、分布式执行，满足高并发需求
4. **企业级特性**：完善的权限控制、监控告警、审计日志等功能
5. **云原生架构**：支持容器化部署、微服务架构、弹性扩展

### 11.2 技术优势

- **架构先进**：采用微服务架构，支持水平扩展和高可用部署
- **技术栈成熟**：基于 Spring Boot、Vue 3 等主流技术栈
- **扩展性强**：插件化架构设计，支持自定义节点和功能扩展
- **性能优异**：多级缓存、数据库优化、异步处理等性能优化策略
- **安全可靠**：完善的安全机制和容错处理

### 11.3 应用场景

- **业务流程自动化**：订单处理、审批流程、数据处理等
- **系统集成**：多系统间的数据同步和业务协调
- **任务调度**：定时任务、批处理作业的编排和执行
- **事件驱动处理**：基于事件的业务流程触发和处理

### 11.4 后续规划

1. **AI 集成**：集成机器学习模型，支持智能决策和预测
2. **低代码平台**：提供更丰富的可视化组件和模板
3. **多云部署**：支持多云环境的部署和管理
4. **生态建设**：构建插件市场和开发者社区

本技术设计文档为企业级工作流引擎系统的开发提供了全面的技术指导，涵盖了从架构设计到部署运维的各个方面，确保系统的可靠性、可扩展性和可维护性。
```
```
```
```
```
```
