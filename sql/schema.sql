-- 企业级工作流引擎数据库表结构
-- 创建时间: 2024年
-- 版本: 1.0.0

-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ==================== 工作流定义相关表 ====================

-- 工作流定义表
CREATE TABLE `workflow_definitions` (
  `id` varchar(64) NOT NULL COMMENT '工作流定义ID',
  `name` varchar(255) NOT NULL COMMENT '工作流名称',
  `version` varchar(32) NOT NULL COMMENT '版本号',
  `description` text COMMENT '描述',
  `dsl_content` longtext NOT NULL COMMENT 'DSL内容',
  `compiled_definition` longtext COMMENT '编译后的定义',
  `status` enum('DRAFT','PUBLISHED','DEPRECATED') NOT NULL DEFAULT 'DRAFT' COMMENT '状态',
  `category` varchar(100) DEFAULT NULL COMMENT '分类',
  `tags` json DEFAULT NULL COMMENT '标签',
  `graph_data` longtext COMMENT '图形数据',
  `created_by` varchar(64) NOT NULL COMMENT '创建者',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `published_at` timestamp NULL DEFAULT NULL COMMENT '发布时间',
  `published_by` varchar(64) DEFAULT NULL COMMENT '发布者',
  `is_active` tinyint(1) DEFAULT '0' COMMENT '是否激活',
  `execution_count` bigint DEFAULT '0' COMMENT '执行次数',
  `success_count` bigint DEFAULT '0' COMMENT '成功次数',
  `failure_count` bigint DEFAULT '0' COMMENT '失败次数',
  `avg_execution_time` bigint DEFAULT '0' COMMENT '平均执行时间(毫秒)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name_version` (`name`,`version`),
  KEY `idx_status` (`status`),
  KEY `idx_category` (`category`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工作流定义表';

-- 工作流版本表
CREATE TABLE `workflow_versions` (
  `id` varchar(64) NOT NULL COMMENT '版本ID',
  `workflow_name` varchar(255) NOT NULL COMMENT '工作流名称',
  `version` varchar(32) NOT NULL COMMENT '版本号',
  `definition_id` varchar(64) NOT NULL COMMENT '定义ID',
  `change_log` text COMMENT '变更日志',
  `is_active` tinyint(1) DEFAULT '0' COMMENT '是否激活',
  `published_by` varchar(64) DEFAULT NULL COMMENT '发布者',
  `published_at` timestamp NULL DEFAULT NULL COMMENT '发布时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `deprecated_at` timestamp NULL DEFAULT NULL COMMENT '弃用时间',
  `deprecated_by` varchar(64) DEFAULT NULL COMMENT '弃用者',
  `deprecation_reason` text COMMENT '弃用原因',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name_version` (`workflow_name`,`version`),
  KEY `idx_workflow_name` (`workflow_name`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_published_at` (`published_at`),
  KEY `fk_definition_id` (`definition_id`),
  CONSTRAINT `fk_workflow_versions_definition` FOREIGN KEY (`definition_id`) REFERENCES `workflow_definitions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工作流版本表';

-- ==================== 工作流实例相关表 ====================

-- 工作流实例表
CREATE TABLE `workflow_instances` (
  `id` varchar(64) NOT NULL COMMENT '实例ID',
  `workflow_definition_id` varchar(64) NOT NULL COMMENT '工作流定义ID',
  `instance_name` varchar(255) DEFAULT NULL COMMENT '实例名称',
  `status` enum('PENDING','RUNNING','COMPLETED','FAILED','CANCELLED','SUSPENDED') NOT NULL DEFAULT 'PENDING' COMMENT '状态',
  `input_data` json DEFAULT NULL COMMENT '输入数据',
  `output_data` json DEFAULT NULL COMMENT '输出数据',
  `variables` json DEFAULT NULL COMMENT '变量',
  `current_node_id` varchar(64) DEFAULT NULL COMMENT '当前节点ID',
  `current_node_name` varchar(255) DEFAULT NULL COMMENT '当前节点名称',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `duration_ms` bigint DEFAULT NULL COMMENT '执行时长(毫秒)',
  `priority` int DEFAULT '0' COMMENT '优先级',
  `created_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `error_message` text COMMENT '错误信息',
  `retry_count` int DEFAULT '0' COMMENT '重试次数',
  `max_retry_count` int DEFAULT '3' COMMENT '最大重试次数',
  `parent_instance_id` varchar(64) DEFAULT NULL COMMENT '父实例ID',
  `root_instance_id` varchar(64) DEFAULT NULL COMMENT '根实例ID',
  `business_key` varchar(255) DEFAULT NULL COMMENT '业务键',
  `tenant_id` varchar(64) DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_workflow_definition` (`workflow_definition_id`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_end_time` (`end_time`),
  KEY `idx_business_key` (`business_key`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_parent_instance` (`parent_instance_id`),
  CONSTRAINT `fk_workflow_instances_definition` FOREIGN KEY (`workflow_definition_id`) REFERENCES `workflow_definitions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工作流实例表';

-- 节点执行记录表
CREATE TABLE `node_executions` (
  `id` varchar(64) NOT NULL COMMENT '执行记录ID',
  `instance_id` varchar(64) NOT NULL COMMENT '实例ID',
  `node_id` varchar(64) NOT NULL COMMENT '节点ID',
  `node_name` varchar(255) DEFAULT NULL COMMENT '节点名称',
  `node_type` enum('START','END','TASK','DECISION','PARALLEL','LOOP','SUBPROCESS','NOTIFICATION','DELAY','SCRIPT','HTTP','SQL','MANUAL','TIMER') NOT NULL COMMENT '节点类型',
  `status` enum('PENDING','RUNNING','COMPLETED','FAILED','SKIPPED','CANCELLED','WAITING','TIMEOUT') NOT NULL DEFAULT 'PENDING' COMMENT '执行状态',
  `input_data` json DEFAULT NULL COMMENT '输入数据',
  `output_data` json DEFAULT NULL COMMENT '输出数据',
  `error_message` text COMMENT '错误信息',
  `error_stack_trace` longtext COMMENT '错误堆栈',
  `retry_count` int DEFAULT '0' COMMENT '重试次数',
  `max_retry_count` int DEFAULT '3' COMMENT '最大重试次数',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `duration_ms` bigint DEFAULT NULL COMMENT '执行时长(毫秒)',
  `executor_id` varchar(64) DEFAULT NULL COMMENT '执行器ID',
  `executor_name` varchar(255) DEFAULT NULL COMMENT '执行器名称',
  `execution_context` json DEFAULT NULL COMMENT '执行上下文',
  `timeout_ms` bigint DEFAULT NULL COMMENT '超时时间(毫秒)',
  `priority` int DEFAULT '0' COMMENT '优先级',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `scheduled_time` timestamp NULL DEFAULT NULL COMMENT '调度时间',
  `parent_execution_id` varchar(64) DEFAULT NULL COMMENT '父执行ID',
  `business_data` json DEFAULT NULL COMMENT '业务数据',
  PRIMARY KEY (`id`),
  KEY `idx_instance_node` (`instance_id`,`node_id`),
  KEY `idx_status` (`status`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_node_type` (`node_type`),
  KEY `idx_scheduled_time` (`scheduled_time`),
  KEY `idx_parent_execution` (`parent_execution_id`),
  CONSTRAINT `fk_node_executions_instance` FOREIGN KEY (`instance_id`) REFERENCES `workflow_instances` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='节点执行记录表';

-- ==================== 任务调度相关表 ====================

-- 任务队列表
CREATE TABLE `task_queue` (
  `id` varchar(64) NOT NULL COMMENT '任务ID',
  `instance_id` varchar(64) NOT NULL COMMENT '实例ID',
  `node_id` varchar(64) NOT NULL COMMENT '节点ID',
  `task_type` varchar(50) NOT NULL COMMENT '任务类型',
  `priority` int DEFAULT '0' COMMENT '优先级',
  `scheduled_time` timestamp NOT NULL COMMENT '调度时间',
  `max_retry_count` int DEFAULT '3' COMMENT '最大重试次数',
  `retry_count` int DEFAULT '0' COMMENT '重试次数',
  `status` enum('PENDING','PROCESSING','COMPLETED','FAILED') DEFAULT 'PENDING' COMMENT '状态',
  `task_data` json DEFAULT NULL COMMENT '任务数据',
  `error_message` text COMMENT '错误信息',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status_scheduled` (`status`,`scheduled_time`),
  KEY `idx_instance_id` (`instance_id`),
  KEY `idx_priority` (`priority` DESC),
  KEY `idx_task_type` (`task_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务队列表';

-- ==================== 用户权限相关表 ====================

-- 用户表
CREATE TABLE `users` (
  `id` varchar(64) NOT NULL COMMENT '用户ID',
  `username` varchar(100) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `email` varchar(255) NOT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `real_name` varchar(100) DEFAULT NULL COMMENT '真实姓名',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `status` enum('ACTIVE','INACTIVE','LOCKED') NOT NULL DEFAULT 'ACTIVE' COMMENT '状态',
  `last_login_time` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` varchar(64) DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`),
  KEY `idx_status` (`status`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 角色表
CREATE TABLE `roles` (
  `id` varchar(64) NOT NULL COMMENT '角色ID',
  `name` varchar(100) NOT NULL COMMENT '角色名称',
  `code` varchar(100) NOT NULL COMMENT '角色编码',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `status` enum('ACTIVE','INACTIVE') NOT NULL DEFAULT 'ACTIVE' COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` varchar(64) DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_status` (`status`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- 权限表
CREATE TABLE `permissions` (
  `id` varchar(64) NOT NULL COMMENT '权限ID',
  `name` varchar(100) NOT NULL COMMENT '权限名称',
  `code` varchar(100) NOT NULL COMMENT '权限编码',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `resource_type` enum('WORKFLOW','INSTANCE','USER','ROLE','SYSTEM') NOT NULL COMMENT '资源类型',
  `action_type` enum('CREATE','READ','UPDATE','DELETE','EXECUTE','MANAGE') NOT NULL COMMENT '操作类型',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_resource_type` (`resource_type`),
  KEY `idx_action_type` (`action_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';

-- 用户角色关联表
CREATE TABLE `user_roles` (
  `id` varchar(64) NOT NULL COMMENT 'ID',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `role_id` varchar(64) NOT NULL COMMENT '角色ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_role` (`user_id`,`role_id`),
  KEY `fk_user_roles_role` (`role_id`),
  CONSTRAINT `fk_user_roles_role` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_roles_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色关联表';

-- 角色权限关联表
CREATE TABLE `role_permissions` (
  `id` varchar(64) NOT NULL COMMENT 'ID',
  `role_id` varchar(64) NOT NULL COMMENT '角色ID',
  `permission_id` varchar(64) NOT NULL COMMENT '权限ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_permission` (`role_id`,`permission_id`),
  KEY `fk_role_permissions_permission` (`permission_id`),
  CONSTRAINT `fk_role_permissions_permission` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_role_permissions_role` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色权限关联表';

-- ==================== 监控审计相关表 ====================

-- 执行日志表
CREATE TABLE `execution_logs` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `instance_id` varchar(64) NOT NULL COMMENT '实例ID',
  `node_id` varchar(64) DEFAULT NULL COMMENT '节点ID',
  `log_level` enum('DEBUG','INFO','WARN','ERROR') NOT NULL DEFAULT 'INFO' COMMENT '日志级别',
  `message` text NOT NULL COMMENT '日志消息',
  `details` json DEFAULT NULL COMMENT '详细信息',
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
  `thread_name` varchar(100) DEFAULT NULL COMMENT '线程名称',
  `class_name` varchar(255) DEFAULT NULL COMMENT '类名',
  `method_name` varchar(100) DEFAULT NULL COMMENT '方法名',
  `line_number` int DEFAULT NULL COMMENT '行号',
  PRIMARY KEY (`id`),
  KEY `idx_instance_id` (`instance_id`),
  KEY `idx_timestamp` (`timestamp`),
  KEY `idx_log_level` (`log_level`),
  KEY `idx_node_id` (`node_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='执行日志表';

-- 性能指标表
CREATE TABLE `performance_metrics` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '指标ID',
  `metric_name` varchar(100) NOT NULL COMMENT '指标名称',
  `metric_type` enum('COUNTER','GAUGE','HISTOGRAM','TIMER') NOT NULL COMMENT '指标类型',
  `metric_value` decimal(20,6) NOT NULL COMMENT '指标值',
  `tags` json DEFAULT NULL COMMENT '标签',
  `instance_id` varchar(64) DEFAULT NULL COMMENT '实例ID',
  `node_id` varchar(64) DEFAULT NULL COMMENT '节点ID',
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
  `host_name` varchar(100) DEFAULT NULL COMMENT '主机名',
  `application_name` varchar(100) DEFAULT NULL COMMENT '应用名称',
  PRIMARY KEY (`id`),
  KEY `idx_metric_name` (`metric_name`),
  KEY `idx_timestamp` (`timestamp`),
  KEY `idx_instance_id` (`instance_id`),
  KEY `idx_metric_type` (`metric_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='性能指标表';

-- 审计日志表
CREATE TABLE `audit_logs` (
  `id` varchar(64) NOT NULL COMMENT '审计ID',
  `user_id` varchar(64) DEFAULT NULL COMMENT '用户ID',
  `username` varchar(100) DEFAULT NULL COMMENT '用户名',
  `action` varchar(100) NOT NULL COMMENT '操作',
  `resource_type` varchar(50) NOT NULL COMMENT '资源类型',
  `resource_id` varchar(64) DEFAULT NULL COMMENT '资源ID',
  `details` text COMMENT '详细信息',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
  `result` enum('SUCCESS','FAILURE') NOT NULL COMMENT '结果',
  `error_message` text COMMENT '错误信息',
  `session_id` varchar(100) DEFAULT NULL COMMENT '会话ID',
  `tenant_id` varchar(64) DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_resource_type` (`resource_type`),
  KEY `idx_timestamp` (`timestamp`),
  KEY `idx_result` (`result`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='审计日志表';

-- 告警规则表
CREATE TABLE `alert_rules` (
  `id` varchar(64) NOT NULL COMMENT '规则ID',
  `name` varchar(255) NOT NULL COMMENT '规则名称',
  `description` text COMMENT '描述',
  `metric_name` varchar(100) NOT NULL COMMENT '指标名称',
  `condition_type` enum('GREATER_THAN','LESS_THAN','EQUALS','NOT_EQUALS','CONTAINS') NOT NULL COMMENT '条件类型',
  `threshold_value` decimal(20,6) NOT NULL COMMENT '阈值',
  `duration_seconds` int NOT NULL DEFAULT '60' COMMENT '持续时间(秒)',
  `severity` enum('LOW','MEDIUM','HIGH','CRITICAL') NOT NULL DEFAULT 'MEDIUM' COMMENT '严重程度',
  `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `notification_channels` json DEFAULT NULL COMMENT '通知渠道',
  `created_by` varchar(64) NOT NULL COMMENT '创建者',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_metric_name` (`metric_name`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_severity` (`severity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='告警规则表';

-- 告警记录表
CREATE TABLE `alert_records` (
  `id` varchar(64) NOT NULL COMMENT '告警ID',
  `rule_id` varchar(64) NOT NULL COMMENT '规则ID',
  `rule_name` varchar(255) NOT NULL COMMENT '规则名称',
  `metric_name` varchar(100) NOT NULL COMMENT '指标名称',
  `metric_value` decimal(20,6) NOT NULL COMMENT '指标值',
  `threshold_value` decimal(20,6) NOT NULL COMMENT '阈值',
  `severity` enum('LOW','MEDIUM','HIGH','CRITICAL') NOT NULL COMMENT '严重程度',
  `status` enum('FIRING','RESOLVED') NOT NULL DEFAULT 'FIRING' COMMENT '状态',
  `message` text NOT NULL COMMENT '告警消息',
  `instance_id` varchar(64) DEFAULT NULL COMMENT '实例ID',
  `node_id` varchar(64) DEFAULT NULL COMMENT '节点ID',
  `fired_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '触发时间',
  `resolved_at` timestamp NULL DEFAULT NULL COMMENT '解决时间',
  `notification_sent` tinyint(1) DEFAULT '0' COMMENT '是否已发送通知',
  `notification_channels` json DEFAULT NULL COMMENT '通知渠道',
  PRIMARY KEY (`id`),
  KEY `idx_rule_id` (`rule_id`),
  KEY `idx_status` (`status`),
  KEY `idx_severity` (`severity`),
  KEY `idx_fired_at` (`fired_at`),
  KEY `idx_instance_id` (`instance_id`),
  CONSTRAINT `fk_alert_records_rule` FOREIGN KEY (`rule_id`) REFERENCES `alert_rules` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='告警记录表';

-- ==================== 系统配置相关表 ====================

-- 系统配置表
CREATE TABLE `system_configs` (
  `id` varchar(64) NOT NULL COMMENT '配置ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `config_type` enum('STRING','NUMBER','BOOLEAN','JSON') NOT NULL DEFAULT 'STRING' COMMENT '配置类型',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `category` varchar(50) DEFAULT NULL COMMENT '分类',
  `is_encrypted` tinyint(1) DEFAULT '0' COMMENT '是否加密',
  `is_system` tinyint(1) DEFAULT '0' COMMENT '是否系统配置',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_category` (`category`),
  KEY `idx_is_system` (`is_system`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 数据字典表
CREATE TABLE `data_dictionaries` (
  `id` varchar(64) NOT NULL COMMENT '字典ID',
  `dict_type` varchar(50) NOT NULL COMMENT '字典类型',
  `dict_code` varchar(50) NOT NULL COMMENT '字典编码',
  `dict_label` varchar(100) NOT NULL COMMENT '字典标签',
  `dict_value` varchar(100) NOT NULL COMMENT '字典值',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `status` enum('ACTIVE','INACTIVE') NOT NULL DEFAULT 'ACTIVE' COMMENT '状态',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_type_code` (`dict_type`,`dict_code`),
  KEY `idx_dict_type` (`dict_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据字典表';

-- 设置外键检查
SET FOREIGN_KEY_CHECKS = 1;
