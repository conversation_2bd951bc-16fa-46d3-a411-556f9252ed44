-- 企业级工作流引擎初始化数据
-- 创建时间: 2024年
-- 版本: 1.0.0

SET NAMES utf8mb4;

-- ==================== 权限数据初始化 ====================

-- 插入基础权限
INSERT INTO `permissions` (`id`, `name`, `code`, `description`, `resource_type`, `action_type`) VALUES
('perm_001', '创建工作流', 'WORKFLOW_CREATE', '创建新的工作流定义', 'WORKFLOW', 'CREATE'),
('perm_002', '查看工作流', 'WORKFLOW_READ', '查看工作流定义和详情', 'WORKFLOW', 'READ'),
('perm_003', '编辑工作流', 'WORKFLOW_UPDATE', '编辑和修改工作流定义', 'WORKFLOW', 'UPDATE'),
('perm_004', '删除工作流', 'WORKFLOW_DELETE', '删除工作流定义', 'WORKFLOW', 'DELETE'),
('perm_005', '执行工作流', 'WORKFLOW_EXECUTE', '启动和执行工作流实例', 'WORKFLOW', 'EXECUTE'),
('perm_006', '管理工作流', 'WORKFLOW_MANAGE', '管理工作流的发布、版本等', 'WORKFLOW', 'MANAGE'),

('perm_011', '查看实例', 'INSTANCE_READ', '查看工作流实例信息', 'INSTANCE', 'READ'),
('perm_012', '控制实例', 'INSTANCE_UPDATE', '暂停、恢复、取消工作流实例', 'INSTANCE', 'UPDATE'),
('perm_013', '删除实例', 'INSTANCE_DELETE', '删除工作流实例记录', 'INSTANCE', 'DELETE'),

('perm_021', '创建用户', 'USER_CREATE', '创建新用户账号', 'USER', 'CREATE'),
('perm_022', '查看用户', 'USER_READ', '查看用户信息', 'USER', 'READ'),
('perm_023', '编辑用户', 'USER_UPDATE', '编辑用户信息', 'USER', 'UPDATE'),
('perm_024', '删除用户', 'USER_DELETE', '删除用户账号', 'USER', 'DELETE'),

('perm_031', '创建角色', 'ROLE_CREATE', '创建新角色', 'ROLE', 'CREATE'),
('perm_032', '查看角色', 'ROLE_READ', '查看角色信息', 'ROLE', 'READ'),
('perm_033', '编辑角色', 'ROLE_UPDATE', '编辑角色信息和权限', 'ROLE', 'UPDATE'),
('perm_034', '删除角色', 'ROLE_DELETE', '删除角色', 'ROLE', 'DELETE'),

('perm_041', '系统监控', 'SYSTEM_MONITOR', '查看系统监控信息', 'SYSTEM', 'READ'),
('perm_042', '系统配置', 'SYSTEM_CONFIG', '管理系统配置', 'SYSTEM', 'MANAGE'),
('perm_043', '系统管理', 'SYSTEM_ADMIN', '系统管理员权限', 'SYSTEM', 'MANAGE');

-- 插入基础角色
INSERT INTO `roles` (`id`, `name`, `code`, `description`, `status`) VALUES
('role_001', '系统管理员', 'SYSTEM_ADMIN', '系统管理员，拥有所有权限', 'ACTIVE'),
('role_002', '工作流管理员', 'WORKFLOW_ADMIN', '工作流管理员，可以管理所有工作流', 'ACTIVE'),
('role_003', '工作流设计师', 'WORKFLOW_DESIGNER', '工作流设计师，可以创建和编辑工作流', 'ACTIVE'),
('role_004', '工作流执行者', 'WORKFLOW_EXECUTOR', '工作流执行者，可以执行工作流', 'ACTIVE'),
('role_005', '工作流查看者', 'WORKFLOW_VIEWER', '工作流查看者，只能查看工作流信息', 'ACTIVE');

-- 角色权限关联
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`) VALUES
-- 系统管理员拥有所有权限
('rp_001', 'role_001', 'perm_001'), ('rp_002', 'role_001', 'perm_002'), ('rp_003', 'role_001', 'perm_003'),
('rp_004', 'role_001', 'perm_004'), ('rp_005', 'role_001', 'perm_005'), ('rp_006', 'role_001', 'perm_006'),
('rp_007', 'role_001', 'perm_011'), ('rp_008', 'role_001', 'perm_012'), ('rp_009', 'role_001', 'perm_013'),
('rp_010', 'role_001', 'perm_021'), ('rp_011', 'role_001', 'perm_022'), ('rp_012', 'role_001', 'perm_023'),
('rp_013', 'role_001', 'perm_024'), ('rp_014', 'role_001', 'perm_031'), ('rp_015', 'role_001', 'perm_032'),
('rp_016', 'role_001', 'perm_033'), ('rp_017', 'role_001', 'perm_034'), ('rp_018', 'role_001', 'perm_041'),
('rp_019', 'role_001', 'perm_042'), ('rp_020', 'role_001', 'perm_043'),

-- 工作流管理员权限
('rp_021', 'role_002', 'perm_001'), ('rp_022', 'role_002', 'perm_002'), ('rp_023', 'role_002', 'perm_003'),
('rp_024', 'role_002', 'perm_004'), ('rp_025', 'role_002', 'perm_005'), ('rp_026', 'role_002', 'perm_006'),
('rp_027', 'role_002', 'perm_011'), ('rp_028', 'role_002', 'perm_012'), ('rp_029', 'role_002', 'perm_013'),
('rp_030', 'role_002', 'perm_041'),

-- 工作流设计师权限
('rp_031', 'role_003', 'perm_001'), ('rp_032', 'role_003', 'perm_002'), ('rp_033', 'role_003', 'perm_003'),
('rp_034', 'role_003', 'perm_005'), ('rp_035', 'role_003', 'perm_011'),

-- 工作流执行者权限
('rp_036', 'role_004', 'perm_002'), ('rp_037', 'role_004', 'perm_005'), ('rp_038', 'role_004', 'perm_011'),
('rp_039', 'role_004', 'perm_012'),

-- 工作流查看者权限
('rp_040', 'role_005', 'perm_002'), ('rp_041', 'role_005', 'perm_011');

-- 插入默认管理员用户
INSERT INTO `users` (`id`, `username`, `password`, `email`, `real_name`, `status`) VALUES
('user_001', 'admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKVjzieMwkOBEDQJIrPqJGOqNJba', '<EMAIL>', '系统管理员', 'ACTIVE');

-- 用户角色关联
INSERT INTO `user_roles` (`id`, `user_id`, `role_id`) VALUES
('ur_001', 'user_001', 'role_001');

-- ==================== 系统配置数据初始化 ====================

-- 插入系统配置
INSERT INTO `system_configs` (`id`, `config_key`, `config_value`, `config_type`, `description`, `category`, `is_system`) VALUES
('cfg_001', 'system.name', '企业级工作流引擎', 'STRING', '系统名称', 'SYSTEM', 1),
('cfg_002', 'system.version', '1.0.0', 'STRING', '系统版本', 'SYSTEM', 1),
('cfg_003', 'system.description', '基于Groovy DSL和LogicFlow的企业级工作流引擎系统', 'STRING', '系统描述', 'SYSTEM', 1),

('cfg_011', 'workflow.max_instances', '10000', 'NUMBER', '最大工作流实例数', 'WORKFLOW', 1),
('cfg_012', 'workflow.default_timeout', '3600000', 'NUMBER', '默认超时时间(毫秒)', 'WORKFLOW', 1),
('cfg_013', 'workflow.max_retry_count', '3', 'NUMBER', '默认最大重试次数', 'WORKFLOW', 1),
('cfg_014', 'workflow.cleanup_days', '30', 'NUMBER', '实例清理天数', 'WORKFLOW', 1),

('cfg_021', 'executor.core_pool_size', '10', 'NUMBER', '执行器核心线程数', 'EXECUTOR', 1),
('cfg_022', 'executor.max_pool_size', '50', 'NUMBER', '执行器最大线程数', 'EXECUTOR', 1),
('cfg_023', 'executor.queue_capacity', '200', 'NUMBER', '执行器队列容量', 'EXECUTOR', 1),
('cfg_024', 'executor.keep_alive_seconds', '60', 'NUMBER', '线程保活时间(秒)', 'EXECUTOR', 1),

('cfg_031', 'notification.email.enabled', 'true', 'BOOLEAN', '是否启用邮件通知', 'NOTIFICATION', 0),
('cfg_032', 'notification.email.smtp_host', 'smtp.gmail.com', 'STRING', 'SMTP服务器地址', 'NOTIFICATION', 0),
('cfg_033', 'notification.email.smtp_port', '587', 'NUMBER', 'SMTP服务器端口', 'NOTIFICATION', 0),
('cfg_034', 'notification.wechat.enabled', 'false', 'BOOLEAN', '是否启用企业微信通知', 'NOTIFICATION', 0),

('cfg_041', 'security.jwt.secret', 'workflow-engine-jwt-secret-key-2024', 'STRING', 'JWT密钥', 'SECURITY', 1),
('cfg_042', 'security.jwt.expiration', '86400000', 'NUMBER', 'JWT过期时间(毫秒)', 'SECURITY', 1),
('cfg_043', 'security.password.min_length', '8', 'NUMBER', '密码最小长度', 'SECURITY', 1),
('cfg_044', 'security.login.max_attempts', '5', 'NUMBER', '最大登录尝试次数', 'SECURITY', 1),

('cfg_051', 'monitoring.metrics.enabled', 'true', 'BOOLEAN', '是否启用指标收集', 'MONITORING', 1),
('cfg_052', 'monitoring.alerts.enabled', 'true', 'BOOLEAN', '是否启用告警', 'MONITORING', 1),
('cfg_053', 'monitoring.retention_days', '90', 'NUMBER', '监控数据保留天数', 'MONITORING', 1);

-- ==================== 数据字典初始化 ====================

-- 插入数据字典
INSERT INTO `data_dictionaries` (`id`, `dict_type`, `dict_code`, `dict_label`, `dict_value`, `sort_order`, `status`) VALUES
-- 工作流状态
('dict_001', 'WORKFLOW_STATUS', 'DRAFT', '草稿', 'DRAFT', 1, 'ACTIVE'),
('dict_002', 'WORKFLOW_STATUS', 'PUBLISHED', '已发布', 'PUBLISHED', 2, 'ACTIVE'),
('dict_003', 'WORKFLOW_STATUS', 'DEPRECATED', '已弃用', 'DEPRECATED', 3, 'ACTIVE'),

-- 实例状态
('dict_011', 'INSTANCE_STATUS', 'PENDING', '等待执行', 'PENDING', 1, 'ACTIVE'),
('dict_012', 'INSTANCE_STATUS', 'RUNNING', '运行中', 'RUNNING', 2, 'ACTIVE'),
('dict_013', 'INSTANCE_STATUS', 'COMPLETED', '已完成', 'COMPLETED', 3, 'ACTIVE'),
('dict_014', 'INSTANCE_STATUS', 'FAILED', '执行失败', 'FAILED', 4, 'ACTIVE'),
('dict_015', 'INSTANCE_STATUS', 'CANCELLED', '已取消', 'CANCELLED', 5, 'ACTIVE'),
('dict_016', 'INSTANCE_STATUS', 'SUSPENDED', '已暂停', 'SUSPENDED', 6, 'ACTIVE'),

-- 节点类型
('dict_021', 'NODE_TYPE', 'START', '开始节点', 'START', 1, 'ACTIVE'),
('dict_022', 'NODE_TYPE', 'END', '结束节点', 'END', 2, 'ACTIVE'),
('dict_023', 'NODE_TYPE', 'TASK', '任务节点', 'TASK', 3, 'ACTIVE'),
('dict_024', 'NODE_TYPE', 'DECISION', '决策节点', 'DECISION', 4, 'ACTIVE'),
('dict_025', 'NODE_TYPE', 'PARALLEL', '并行网关', 'PARALLEL', 5, 'ACTIVE'),
('dict_026', 'NODE_TYPE', 'LOOP', '循环节点', 'LOOP', 6, 'ACTIVE'),
('dict_027', 'NODE_TYPE', 'SUBPROCESS', '子流程节点', 'SUBPROCESS', 7, 'ACTIVE'),
('dict_028', 'NODE_TYPE', 'NOTIFICATION', '通知节点', 'NOTIFICATION', 8, 'ACTIVE'),
('dict_029', 'NODE_TYPE', 'DELAY', '延迟节点', 'DELAY', 9, 'ACTIVE'),
('dict_030', 'NODE_TYPE', 'SCRIPT', '脚本节点', 'SCRIPT', 10, 'ACTIVE'),
('dict_031', 'NODE_TYPE', 'HTTP', 'HTTP节点', 'HTTP', 11, 'ACTIVE'),
('dict_032', 'NODE_TYPE', 'SQL', 'SQL节点', 'SQL', 12, 'ACTIVE'),
('dict_033', 'NODE_TYPE', 'MANUAL', '人工任务节点', 'MANUAL', 13, 'ACTIVE'),
('dict_034', 'NODE_TYPE', 'TIMER', '定时器节点', 'TIMER', 14, 'ACTIVE'),

-- 节点状态
('dict_041', 'NODE_STATUS', 'PENDING', '等待执行', 'PENDING', 1, 'ACTIVE'),
('dict_042', 'NODE_STATUS', 'RUNNING', '运行中', 'RUNNING', 2, 'ACTIVE'),
('dict_043', 'NODE_STATUS', 'COMPLETED', '已完成', 'COMPLETED', 3, 'ACTIVE'),
('dict_044', 'NODE_STATUS', 'FAILED', '执行失败', 'FAILED', 4, 'ACTIVE'),
('dict_045', 'NODE_STATUS', 'SKIPPED', '已跳过', 'SKIPPED', 5, 'ACTIVE'),
('dict_046', 'NODE_STATUS', 'CANCELLED', '已取消', 'CANCELLED', 6, 'ACTIVE'),
('dict_047', 'NODE_STATUS', 'WAITING', '等待中', 'WAITING', 7, 'ACTIVE'),
('dict_048', 'NODE_STATUS', 'TIMEOUT', '超时', 'TIMEOUT', 8, 'ACTIVE'),

-- 告警级别
('dict_051', 'ALERT_SEVERITY', 'LOW', '低', 'LOW', 1, 'ACTIVE'),
('dict_052', 'ALERT_SEVERITY', 'MEDIUM', '中', 'MEDIUM', 2, 'ACTIVE'),
('dict_053', 'ALERT_SEVERITY', 'HIGH', '高', 'HIGH', 3, 'ACTIVE'),
('dict_054', 'ALERT_SEVERITY', 'CRITICAL', '严重', 'CRITICAL', 4, 'ACTIVE'),

-- 通知类型
('dict_061', 'NOTIFICATION_TYPE', 'EMAIL', '邮件', 'EMAIL', 1, 'ACTIVE'),
('dict_062', 'NOTIFICATION_TYPE', 'SMS', '短信', 'SMS', 2, 'ACTIVE'),
('dict_063', 'NOTIFICATION_TYPE', 'WECHAT', '企业微信', 'WECHAT', 3, 'ACTIVE'),
('dict_064', 'NOTIFICATION_TYPE', 'DINGTALK', '钉钉', 'DINGTALK', 4, 'ACTIVE'),
('dict_065', 'NOTIFICATION_TYPE', 'WEBHOOK', 'WebHook', 'WEBHOOK', 5, 'ACTIVE');

-- ==================== 默认告警规则 ====================

-- 插入默认告警规则
INSERT INTO `alert_rules` (`id`, `name`, `description`, `metric_name`, `condition_type`, `threshold_value`, `duration_seconds`, `severity`, `enabled`, `notification_channels`, `created_by`) VALUES
('alert_001', '工作流执行失败率过高', '当工作流执行失败率超过10%时触发告警', 'workflow_failure_rate', 'GREATER_THAN', 0.1, 300, 'HIGH', 1, '["EMAIL", "WECHAT"]', 'user_001'),
('alert_002', '工作流执行时间过长', '当工作流平均执行时间超过1小时时触发告警', 'workflow_avg_duration', 'GREATER_THAN', 3600000, 600, 'MEDIUM', 1, '["EMAIL"]', 'user_001'),
('alert_003', '任务队列积压', '当任务队列中待处理任务超过1000个时触发告警', 'task_queue_size', 'GREATER_THAN', 1000, 120, 'CRITICAL', 1, '["EMAIL", "WECHAT"]', 'user_001'),
('alert_004', '系统CPU使用率过高', '当系统CPU使用率超过80%时触发告警', 'system_cpu_usage', 'GREATER_THAN', 0.8, 300, 'HIGH', 1, '["EMAIL"]', 'user_001'),
('alert_005', '系统内存使用率过高', '当系统内存使用率超过85%时触发告警', 'system_memory_usage', 'GREATER_THAN', 0.85, 300, 'HIGH', 1, '["EMAIL"]', 'user_001');
