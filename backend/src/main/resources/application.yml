server:
  port: 8080
  servlet:
    context-path: /api
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024

spring:
  application:
    name: workflow-engine
  
  profiles:
    active: dev
  
  # 数据源配置
  datasource:
    url: ********************************************************************************************************
    username: workflow
    password: workflow123
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
  
  # JPA 配置
  jpa:
    hibernate:
      ddl-auto: validate
      naming:
        physical-strategy: org.hibernate.boot.model.naming.SnakeCasePhysicalNamingStrategy
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
  
  # Redis 配置
  data:
    redis:
      host: localhost
      port: 6379
      password: 
      database: 0
      timeout: 2000ms
      jedis:
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 5
          max-wait: 2000ms
  
  # RabbitMQ 配置
  rabbitmq:
    host: localhost
    port: 5672
    username: workflow
    password: workflow123
    virtual-host: /
    connection-timeout: 15000
    publisher-confirm-type: correlated
    publisher-returns: true
    listener:
      simple:
        acknowledge-mode: manual
        retry:
          enabled: true
          max-attempts: 3
          initial-interval: 1000
          multiplier: 2
          max-interval: 10000
  
  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 1800000
      cache-null-values: false
  
  # 邮件配置
  mail:
    host: smtp.gmail.com
    port: 587
    username: ${MAIL_USERNAME:}
    password: ${MAIL_PASSWORD:}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
  
  # Jackson 配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
      fail-on-empty-beans: false
    deserialization:
      fail-on-unknown-properties: false

# 管理端点配置
management:
  server:
    port: 8081
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,loggers
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
      show-components: when-authorized
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5,0.9,0.95,0.99
      slo:
        http.server.requests: 10ms,50ms,100ms,200ms,500ms,1s,2s,5s

# 工作流引擎配置
workflow:
  engine:
    # 线程池配置
    thread-pool:
      core-size: 10
      max-size: 50
      queue-capacity: 200
      keep-alive-seconds: 60
      thread-name-prefix: workflow-task-
    
    # 执行配置
    execution:
      timeout: 3600
      max-retry-attempts: 3
      retry-backoff-delay: 1000
      max-parallel-executions: 100
    
    # DSL 配置
    dsl:
      max-script-execution-time: 30000
      allowed-packages:
        - java.lang
        - java.util
        - java.time
        - java.math
        - groovy.lang
        - groovy.util
    
    # 监控配置
    monitoring:
      metrics-enabled: true
      audit-enabled: true
      performance-tracking: true

# 安全配置
security:
  jwt:
    secret: ${JWT_SECRET:workflow-engine-jwt-secret-key-2024}
    expiration: 86400000
    refresh-expiration: *********
  
  encryption:
    key: ${ENCRYPTION_KEY:workflow-engine-encryption-key}
    algorithm: AES

# 日志配置
logging:
  level:
    com.company.workflow: INFO
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/workflow-engine.log
    max-size: 100MB
    max-history: 30

# API 文档配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    operations-sorter: method
    tags-sorter: alpha
  info:
    title: Workflow Engine API
    description: Enterprise Workflow Engine REST API Documentation
    version: 1.0.0
    contact:
      name: Workflow Team
      email: <EMAIL>
  
# 国际化配置
spring.messages:
  basename: messages
  encoding: UTF-8
  cache-duration: 3600

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
  
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: update
  
  devtools:
    restart:
      enabled: true
    livereload:
      enabled: true

logging:
  level:
    com.company.workflow: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

---
# 测试环境配置
spring:
  config:
    activate:
      on-profile: test
  
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    database-platform: org.hibernate.dialect.H2Dialect
  
  h2:
    console:
      enabled: true

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
  
  jpa:
    show-sql: false
    hibernate:
      ddl-auto: validate

logging:
  level:
    com.company.workflow: WARN
    org.springframework: WARN
    org.hibernate: WARN
