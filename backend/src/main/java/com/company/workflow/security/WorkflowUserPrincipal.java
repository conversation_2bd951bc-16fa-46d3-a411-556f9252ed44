package com.company.workflow.security;

import com.company.workflow.model.entity.User;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 工作流用户主体
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class WorkflowUserPrincipal implements UserDetails {
    
    private String id;
    private String username;
    private String email;
    private String realName;
    
    @JsonIgnore
    private String password;
    
    private boolean enabled;
    private boolean accountNonExpired;
    private boolean accountNonLocked;
    private boolean credentialsNonExpired;
    
    private Collection<? extends GrantedAuthority> authorities;
    
    public WorkflowUserPrincipal(String id, String username, String email, String realName, 
                                String password, boolean enabled, boolean accountNonExpired, 
                                boolean accountNonLocked, boolean credentialsNonExpired,
                                Collection<? extends GrantedAuthority> authorities) {
        this.id = id;
        this.username = username;
        this.email = email;
        this.realName = realName;
        this.password = password;
        this.enabled = enabled;
        this.accountNonExpired = accountNonExpired;
        this.accountNonLocked = accountNonLocked;
        this.credentialsNonExpired = credentialsNonExpired;
        this.authorities = authorities;
    }
    
    /**
     * 从用户实体创建用户主体
     */
    public static WorkflowUserPrincipal create(User user) {
        List<GrantedAuthority> authorities = user.getRoles().stream()
                .flatMap(role -> role.getPermissions().stream())
                .map(permission -> new SimpleGrantedAuthority(permission.getCode()))
                .collect(Collectors.toList());
        
        return new WorkflowUserPrincipal(
                user.getId(),
                user.getUsername(),
                user.getEmail(),
                user.getRealName(),
                user.getPassword(),
                user.isEnabled(),
                !user.isAccountExpired(),
                !user.isAccountLocked(),
                !user.isCredentialsExpired(),
                authorities
        );
    }
    
    // UserDetails 接口实现
    
    @Override
    public String getUsername() {
        return username;
    }
    
    @Override
    public String getPassword() {
        return password;
    }
    
    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return authorities;
    }
    
    @Override
    public boolean isAccountNonExpired() {
        return accountNonExpired;
    }
    
    @Override
    public boolean isAccountNonLocked() {
        return accountNonLocked;
    }
    
    @Override
    public boolean isCredentialsNonExpired() {
        return credentialsNonExpired;
    }
    
    @Override
    public boolean isEnabled() {
        return enabled;
    }
    
    // 自定义属性的 Getters
    
    public String getId() {
        return id;
    }
    
    public String getEmail() {
        return email;
    }
    
    public String getRealName() {
        return realName;
    }
    
    // 工具方法
    
    /**
     * 检查是否有指定权限
     */
    public boolean hasAuthority(String authority) {
        return authorities.stream()
                .anyMatch(grantedAuthority -> grantedAuthority.getAuthority().equals(authority));
    }
    
    /**
     * 检查是否有任意一个权限
     */
    public boolean hasAnyAuthority(String... authorities) {
        for (String authority : authorities) {
            if (hasAuthority(authority)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查是否有所有权限
     */
    public boolean hasAllAuthorities(String... authorities) {
        for (String authority : authorities) {
            if (!hasAuthority(authority)) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 获取权限代码列表
     */
    public List<String> getAuthorityList() {
        return authorities.stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList());
    }
    
    /**
     * 检查是否为系统管理员
     */
    public boolean isSystemAdmin() {
        return hasAuthority("SYSTEM_ADMIN");
    }
    
    /**
     * 检查是否为租户管理员
     */
    public boolean isTenantAdmin() {
        return hasAuthority("TENANT_ADMIN");
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        WorkflowUserPrincipal that = (WorkflowUserPrincipal) o;
        return Objects.equals(id, that.id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
    
    @Override
    public String toString() {
        return "WorkflowUserPrincipal{" +
                "id='" + id + '\'' +
                ", username='" + username + '\'' +
                ", email='" + email + '\'' +
                ", realName='" + realName + '\'' +
                ", enabled=" + enabled +
                ", authorities=" + authorities.size() +
                '}';
    }
}
