package com.company.workflow.security;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.SecretKey;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * JWT 令牌工具类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Component
public class JwtTokenUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(JwtTokenUtil.class);
    
    @Value("${workflow.jwt.secret:workflow-engine-jwt-secret-key-2024}")
    private String jwtSecret;
    
    @Value("${workflow.jwt.expiration:86400000}")
    private long jwtExpirationMs;
    
    @Value("${workflow.jwt.refresh-expiration:*********}")
    private long jwtRefreshExpirationMs;
    
    private SecretKey getSigningKey() {
        return Keys.hmacShaKeyFor(jwtSecret.getBytes());
    }
    
    /**
     * 生成访问令牌
     */
    public String generateAccessToken(Authentication authentication) {
        WorkflowUserPrincipal userPrincipal = (WorkflowUserPrincipal) authentication.getPrincipal();
        
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userPrincipal.getId());
        claims.put("username", userPrincipal.getUsername());
        claims.put("email", userPrincipal.getEmail());
        claims.put("realName", userPrincipal.getRealName());
        claims.put("authorities", userPrincipal.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList()));
        
        return generateToken(claims, userPrincipal.getUsername(), jwtExpirationMs);
    }
    
    /**
     * 生成刷新令牌
     */
    public String generateRefreshToken(Authentication authentication) {
        WorkflowUserPrincipal userPrincipal = (WorkflowUserPrincipal) authentication.getPrincipal();
        
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userPrincipal.getId());
        claims.put("username", userPrincipal.getUsername());
        claims.put("tokenType", "refresh");
        
        return generateToken(claims, userPrincipal.getUsername(), jwtRefreshExpirationMs);
    }
    
    /**
     * 生成令牌
     */
    private String generateToken(Map<String, Object> claims, String subject, long expiration) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration);
        
        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(getSigningKey(), SignatureAlgorithm.HS512)
                .compact();
    }
    
    /**
     * 从令牌中获取用户名
     */
    public String getUsernameFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.getSubject();
    }
    
    /**
     * 从令牌中获取用户ID
     */
    public String getUserIdFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.get("userId", String.class);
    }
    
    /**
     * 从令牌中获取邮箱
     */
    public String getEmailFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.get("email", String.class);
    }
    
    /**
     * 从令牌中获取真实姓名
     */
    public String getRealNameFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.get("realName", String.class);
    }
    
    /**
     * 从令牌中获取权限列表
     */
    @SuppressWarnings("unchecked")
    public java.util.List<String> getAuthoritiesFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return (java.util.List<String>) claims.get("authorities");
    }
    
    /**
     * 从令牌中获取过期时间
     */
    public Date getExpirationDateFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.getExpiration();
    }
    
    /**
     * 从令牌中获取签发时间
     */
    public Date getIssuedAtFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.getIssuedAt();
    }
    
    /**
     * 检查令牌是否为刷新令牌
     */
    public boolean isRefreshToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return "refresh".equals(claims.get("tokenType"));
    }
    
    /**
     * 从令牌中获取声明
     */
    private Claims getClaimsFromToken(String token) {
        try {
            return Jwts.parserBuilder()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
        } catch (JwtException e) {
            logger.error("解析JWT令牌失败: {}", e.getMessage());
            throw e;
        }
    }
    
    /**
     * 验证令牌
     */
    public boolean validateToken(String token) {
        try {
            Jwts.parserBuilder()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(token);
            return true;
        } catch (SecurityException e) {
            logger.error("JWT签名无效: {}", e.getMessage());
        } catch (MalformedJwtException e) {
            logger.error("JWT令牌格式错误: {}", e.getMessage());
        } catch (ExpiredJwtException e) {
            logger.error("JWT令牌已过期: {}", e.getMessage());
        } catch (UnsupportedJwtException e) {
            logger.error("不支持的JWT令牌: {}", e.getMessage());
        } catch (IllegalArgumentException e) {
            logger.error("JWT令牌为空: {}", e.getMessage());
        }
        return false;
    }
    
    /**
     * 检查令牌是否过期
     */
    public boolean isTokenExpired(String token) {
        try {
            Date expiration = getExpirationDateFromToken(token);
            return expiration.before(new Date());
        } catch (Exception e) {
            return true;
        }
    }
    
    /**
     * 检查令牌是否即将过期（30分钟内）
     */
    public boolean isTokenExpiringSoon(String token) {
        try {
            Date expiration = getExpirationDateFromToken(token);
            Date now = new Date();
            long timeUntilExpiration = expiration.getTime() - now.getTime();
            return timeUntilExpiration < 30 * 60 * 1000; // 30分钟
        } catch (Exception e) {
            return true;
        }
    }
    
    /**
     * 刷新令牌
     */
    public String refreshToken(String refreshToken) {
        if (!validateToken(refreshToken) || !isRefreshToken(refreshToken)) {
            throw new IllegalArgumentException("无效的刷新令牌");
        }
        
        Claims claims = getClaimsFromToken(refreshToken);
        String username = claims.getSubject();
        String userId = claims.get("userId", String.class);
        
        // 创建新的访问令牌
        Map<String, Object> newClaims = new HashMap<>();
        newClaims.put("userId", userId);
        newClaims.put("username", username);
        
        return generateToken(newClaims, username, jwtExpirationMs);
    }
    
    /**
     * 获取令牌剩余有效时间（毫秒）
     */
    public long getTokenRemainingTime(String token) {
        try {
            Date expiration = getExpirationDateFromToken(token);
            Date now = new Date();
            return Math.max(0, expiration.getTime() - now.getTime());
        } catch (Exception e) {
            return 0;
        }
    }
    
    /**
     * 创建令牌信息对象
     */
    public TokenInfo createTokenInfo(String accessToken, String refreshToken) {
        return new TokenInfo(
                accessToken,
                refreshToken,
                "Bearer",
                jwtExpirationMs / 1000, // 转换为秒
                getExpirationDateFromToken(accessToken),
                getIssuedAtFromToken(accessToken)
        );
    }
    
    /**
     * 令牌信息类
     */
    public static class TokenInfo {
        private String accessToken;
        private String refreshToken;
        private String tokenType;
        private long expiresIn;
        private Date expirationTime;
        private Date issuedAt;
        
        public TokenInfo(String accessToken, String refreshToken, String tokenType, 
                        long expiresIn, Date expirationTime, Date issuedAt) {
            this.accessToken = accessToken;
            this.refreshToken = refreshToken;
            this.tokenType = tokenType;
            this.expiresIn = expiresIn;
            this.expirationTime = expirationTime;
            this.issuedAt = issuedAt;
        }
        
        // Getters
        public String getAccessToken() { return accessToken; }
        public String getRefreshToken() { return refreshToken; }
        public String getTokenType() { return tokenType; }
        public long getExpiresIn() { return expiresIn; }
        public Date getExpirationTime() { return expirationTime; }
        public Date getIssuedAt() { return issuedAt; }
    }
}
