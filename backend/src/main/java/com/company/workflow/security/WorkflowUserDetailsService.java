package com.company.workflow.security;

import com.company.workflow.model.entity.User;
import com.company.workflow.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 工作流用户详情服务
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
public class WorkflowUserDetailsService implements UserDetailsService {
    
    private static final Logger logger = LoggerFactory.getLogger(WorkflowUserDetailsService.class);
    
    @Autowired
    private UserRepository userRepository;
    
    @Override
    @Transactional(readOnly = true)
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        logger.debug("加载用户信息: {}", username);
        
        User user = userRepository.findByUsernameOrEmail(username, username)
                .orElseThrow(() -> new UsernameNotFoundException("用户不存在: " + username));
        
        logger.debug("用户 {} 加载成功，角色数量: {}", username, user.getRoles().size());
        
        return WorkflowUserPrincipal.create(user);
    }
    
    /**
     * 根据用户ID加载用户信息
     */
    @Transactional(readOnly = true)
    public UserDetails loadUserById(String userId) throws UsernameNotFoundException {
        logger.debug("根据ID加载用户信息: {}", userId);
        
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new UsernameNotFoundException("用户不存在: " + userId));
        
        return WorkflowUserPrincipal.create(user);
    }
    
    /**
     * 检查用户是否存在
     */
    @Transactional(readOnly = true)
    public boolean userExists(String username) {
        return userRepository.existsByUsernameOrEmail(username, username);
    }
    
    /**
     * 检查用户是否启用
     */
    @Transactional(readOnly = true)
    public boolean isUserEnabled(String username) {
        return userRepository.findByUsernameOrEmail(username, username)
                .map(User::isEnabled)
                .orElse(false);
    }
    
    /**
     * 检查用户账户是否未锁定
     */
    @Transactional(readOnly = true)
    public boolean isUserAccountNonLocked(String username) {
        return userRepository.findByUsernameOrEmail(username, username)
                .map(user -> !user.isAccountLocked())
                .orElse(false);
    }
    
    /**
     * 检查用户账户是否未过期
     */
    @Transactional(readOnly = true)
    public boolean isUserAccountNonExpired(String username) {
        return userRepository.findByUsernameOrEmail(username, username)
                .map(user -> !user.isAccountExpired())
                .orElse(false);
    }
    
    /**
     * 检查用户凭证是否未过期
     */
    @Transactional(readOnly = true)
    public boolean isUserCredentialsNonExpired(String username) {
        return userRepository.findByUsernameOrEmail(username, username)
                .map(user -> !user.isCredentialsExpired())
                .orElse(false);
    }
}
