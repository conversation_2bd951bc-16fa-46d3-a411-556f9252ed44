package com.company.workflow.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Map;

/**
 * 工作流执行请求
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Schema(description = "工作流执行请求")
public class WorkflowExecutionRequest {
    
    @Schema(description = "工作流定义ID", example = "wf_001", required = true)
    @NotBlank(message = "工作流定义ID不能为空")
    private String workflowDefinitionId;
    
    @Schema(description = "业务键", example = "ORDER_12345")
    @Size(max = 100, message = "业务键长度不能超过100字符")
    private String businessKey;
    
    @Schema(description = "输入数据", example = "{\"orderId\": \"12345\", \"amount\": 100.0}")
    private Map<String, Object> inputData;
    
    @Schema(description = "优先级", example = "5")
    @Min(value = 1, message = "优先级最小值为1")
    @Max(value = 10, message = "优先级最大值为10")
    private Integer priority;
    
    @Schema(description = "租户ID", example = "tenant_001")
    @Size(max = 50, message = "租户ID长度不能超过50字符")
    private String tenantId;
    
    @Schema(description = "父实例ID", example = "inst_parent")
    private String parentInstanceId;
    
    @Schema(description = "最大重试次数", example = "3")
    @Min(value = 0, message = "最大重试次数不能小于0")
    @Max(value = 10, message = "最大重试次数不能大于10")
    private Integer maxRetryCount;
    
    @Schema(description = "超时时间(毫秒)", example = "3600000")
    @Min(value = 1000, message = "超时时间不能小于1秒")
    @Max(value = 86400000, message = "超时时间不能大于24小时")
    private Long timeoutMs;
    
    @Schema(description = "是否异步执行", example = "true")
    private Boolean async;
    
    @Schema(description = "回调URL", example = "https://api.example.com/workflow/callback")
    @Size(max = 500, message = "回调URL长度不能超过500字符")
    private String callbackUrl;
    
    @Schema(description = "扩展属性")
    private Map<String, Object> extensions;
    
    // Getters and Setters
    public String getWorkflowDefinitionId() {
        return workflowDefinitionId;
    }
    
    public void setWorkflowDefinitionId(String workflowDefinitionId) {
        this.workflowDefinitionId = workflowDefinitionId;
    }
    
    public String getBusinessKey() {
        return businessKey;
    }
    
    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }
    
    public Map<String, Object> getInputData() {
        return inputData;
    }
    
    public void setInputData(Map<String, Object> inputData) {
        this.inputData = inputData;
    }
    
    public Integer getPriority() {
        return priority;
    }
    
    public void setPriority(Integer priority) {
        this.priority = priority;
    }
    
    public String getTenantId() {
        return tenantId;
    }
    
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
    
    public String getParentInstanceId() {
        return parentInstanceId;
    }
    
    public void setParentInstanceId(String parentInstanceId) {
        this.parentInstanceId = parentInstanceId;
    }
    
    public Integer getMaxRetryCount() {
        return maxRetryCount;
    }
    
    public void setMaxRetryCount(Integer maxRetryCount) {
        this.maxRetryCount = maxRetryCount;
    }
    
    public Long getTimeoutMs() {
        return timeoutMs;
    }
    
    public void setTimeoutMs(Long timeoutMs) {
        this.timeoutMs = timeoutMs;
    }
    
    public Boolean getAsync() {
        return async;
    }
    
    public void setAsync(Boolean async) {
        this.async = async;
    }
    
    public String getCallbackUrl() {
        return callbackUrl;
    }
    
    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }
    
    public Map<String, Object> getExtensions() {
        return extensions;
    }
    
    public void setExtensions(Map<String, Object> extensions) {
        this.extensions = extensions;
    }
    
    @Override
    public String toString() {
        return "WorkflowExecutionRequest{" +
                "workflowDefinitionId='" + workflowDefinitionId + '\'' +
                ", businessKey='" + businessKey + '\'' +
                ", priority=" + priority +
                ", tenantId='" + tenantId + '\'' +
                ", async=" + async +
                '}';
    }
}
