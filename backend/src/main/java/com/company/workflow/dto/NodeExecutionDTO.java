package com.company.workflow.dto;

import com.company.workflow.model.entity.NodeStatus;
import com.company.workflow.model.entity.NodeType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 节点执行数据传输对象
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Schema(description = "节点执行信息")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NodeExecutionDTO {
    
    @Schema(description = "执行ID", example = "exec_001")
    private String id;
    
    @Schema(description = "实例ID", example = "inst_001")
    private String instanceId;
    
    @Schema(description = "节点ID", example = "node_001")
    private String nodeId;
    
    @Schema(description = "节点名称", example = "验证订单")
    private String nodeName;
    
    @Schema(description = "节点类型")
    private NodeType nodeType;
    
    @Schema(description = "执行状态")
    private NodeStatus status;
    
    @Schema(description = "执行器ID", example = "executor_001")
    private String executorId;
    
    @Schema(description = "执行器名称", example = "TaskNodeExecutor")
    private String executorName;
    
    @Schema(description = "优先级", example = "5")
    private Integer priority;
    
    @Schema(description = "重试次数", example = "1")
    private Integer retryCount;
    
    @Schema(description = "最大重试次数", example = "3")
    private Integer maxRetryCount;
    
    @Schema(description = "超时时间(毫秒)", example = "30000")
    private Long timeoutMs;
    
    @Schema(description = "错误信息")
    private String errorMessage;
    
    @Schema(description = "错误堆栈")
    private String errorStackTrace;
    
    @Schema(description = "输入数据")
    private Map<String, Object> inputData;
    
    @Schema(description = "输出数据")
    private Map<String, Object> outputData;
    
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;
    
    @Schema(description = "开始时间")
    private LocalDateTime startTime;
    
    @Schema(description = "结束时间")
    private LocalDateTime endTime;
    
    @Schema(description = "执行时长(毫秒)", example = "5000")
    private Long durationMs;
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getInstanceId() {
        return instanceId;
    }
    
    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }
    
    public String getNodeId() {
        return nodeId;
    }
    
    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }
    
    public String getNodeName() {
        return nodeName;
    }
    
    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }
    
    public NodeType getNodeType() {
        return nodeType;
    }
    
    public void setNodeType(NodeType nodeType) {
        this.nodeType = nodeType;
    }
    
    public NodeStatus getStatus() {
        return status;
    }
    
    public void setStatus(NodeStatus status) {
        this.status = status;
    }
    
    public String getExecutorId() {
        return executorId;
    }
    
    public void setExecutorId(String executorId) {
        this.executorId = executorId;
    }
    
    public String getExecutorName() {
        return executorName;
    }
    
    public void setExecutorName(String executorName) {
        this.executorName = executorName;
    }
    
    public Integer getPriority() {
        return priority;
    }
    
    public void setPriority(Integer priority) {
        this.priority = priority;
    }
    
    public Integer getRetryCount() {
        return retryCount;
    }
    
    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }
    
    public Integer getMaxRetryCount() {
        return maxRetryCount;
    }
    
    public void setMaxRetryCount(Integer maxRetryCount) {
        this.maxRetryCount = maxRetryCount;
    }
    
    public Long getTimeoutMs() {
        return timeoutMs;
    }
    
    public void setTimeoutMs(Long timeoutMs) {
        this.timeoutMs = timeoutMs;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public String getErrorStackTrace() {
        return errorStackTrace;
    }
    
    public void setErrorStackTrace(String errorStackTrace) {
        this.errorStackTrace = errorStackTrace;
    }
    
    public Map<String, Object> getInputData() {
        return inputData;
    }
    
    public void setInputData(Map<String, Object> inputData) {
        this.inputData = inputData;
    }
    
    public Map<String, Object> getOutputData() {
        return outputData;
    }
    
    public void setOutputData(Map<String, Object> outputData) {
        this.outputData = outputData;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getStartTime() {
        return startTime;
    }
    
    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }
    
    public LocalDateTime getEndTime() {
        return endTime;
    }
    
    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
    
    public Long getDurationMs() {
        return durationMs;
    }
    
    public void setDurationMs(Long durationMs) {
        this.durationMs = durationMs;
    }
    
    /**
     * 判断是否正在执行
     */
    public boolean isRunning() {
        return status == NodeStatus.RUNNING;
    }
    
    /**
     * 判断是否已完成
     */
    public boolean isCompleted() {
        return status == NodeStatus.COMPLETED;
    }
    
    /**
     * 判断是否失败
     */
    public boolean isFailed() {
        return status == NodeStatus.FAILED;
    }
    
    /**
     * 判断是否可以重试
     */
    public boolean canRetry() {
        return isFailed() && retryCount != null && maxRetryCount != null && retryCount < maxRetryCount;
    }
    
    /**
     * 获取格式化的执行时长
     */
    public String getFormattedDuration() {
        if (durationMs == null) {
            return null;
        }
        
        if (durationMs < 1000) {
            return durationMs + "ms";
        } else if (durationMs < 60000) {
            return String.format("%.2fs", durationMs / 1000.0);
        } else {
            long minutes = durationMs / 60000;
            long seconds = (durationMs % 60000) / 1000;
            return String.format("%dm %ds", minutes, seconds);
        }
    }
    
    @Override
    public String toString() {
        return "NodeExecutionDTO{" +
                "id='" + id + '\'' +
                ", nodeId='" + nodeId + '\'' +
                ", nodeName='" + nodeName + '\'' +
                ", nodeType=" + nodeType +
                ", status=" + status +
                ", durationMs=" + durationMs +
                '}';
    }
}
