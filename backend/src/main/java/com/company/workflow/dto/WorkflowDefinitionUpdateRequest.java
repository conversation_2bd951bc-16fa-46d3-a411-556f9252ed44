package com.company.workflow.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * 更新工作流定义请求
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Schema(description = "更新工作流定义请求")
public class WorkflowDefinitionUpdateRequest {
    
    @Schema(description = "工作流描述", example = "更新后的订单处理流程描述")
    @Size(max = 500, message = "描述长度不能超过500字符")
    private String description;
    
    @Schema(description = "工作流分类", example = "核心业务流程")
    @Size(max = 50, message = "分类长度不能超过50字符")
    private String category;
    
    @Schema(description = "DSL内容")
    @Size(min = 10, max = 100000, message = "DSL内容长度必须在10-100000字符之间")
    private String dslContent;
    
    @Schema(description = "标签列表", example = "[\"订单\", \"支付\", \"库存\", \"新增标签\"]")
    @Size(max = 10, message = "标签数量不能超过10个")
    private List<String> tags;
    
    // Getters and Setters
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public String getDslContent() {
        return dslContent;
    }
    
    public void setDslContent(String dslContent) {
        this.dslContent = dslContent;
    }
    
    public List<String> getTags() {
        return tags;
    }
    
    public void setTags(List<String> tags) {
        this.tags = tags;
    }
    
    @Override
    public String toString() {
        return "WorkflowDefinitionUpdateRequest{" +
                "description='" + description + '\'' +
                ", category='" + category + '\'' +
                ", tags=" + tags +
                '}';
    }
}
