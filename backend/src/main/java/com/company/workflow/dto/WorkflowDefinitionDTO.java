package com.company.workflow.dto;

import com.company.workflow.model.entity.WorkflowStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 工作流定义数据传输对象
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Schema(description = "工作流定义信息")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WorkflowDefinitionDTO {
    
    @Schema(description = "工作流定义ID", example = "wf_001")
    private String id;
    
    @Schema(description = "工作流名称", example = "订单处理流程")
    private String name;
    
    @Schema(description = "工作流版本", example = "1.0.0")
    private String version;
    
    @Schema(description = "工作流描述", example = "处理订单的完整流程")
    private String description;
    
    @Schema(description = "工作流分类", example = "业务流程")
    private String category;
    
    @Schema(description = "DSL内容")
    private String dslContent;
    
    @Schema(description = "工作流状态")
    private WorkflowStatus status;
    
    @Schema(description = "是否激活", example = "true")
    private Boolean active;
    
    @Schema(description = "标签列表")
    private List<String> tags;
    
    @Schema(description = "工作流变量")
    private Map<String, Object> variables;
    
    @Schema(description = "执行次数", example = "100")
    private Long executionCount;
    
    @Schema(description = "成功次数", example = "95")
    private Long successCount;
    
    @Schema(description = "失败次数", example = "5")
    private Long failureCount;
    
    @Schema(description = "平均执行时间(毫秒)", example = "30000")
    private Long avgExecutionTime;
    
    @Schema(description = "创建者", example = "admin")
    private String createdBy;
    
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;
    
    @Schema(description = "更新者", example = "admin")
    private String updatedBy;
    
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
    
    @Schema(description = "发布者", example = "admin")
    private String publishedBy;
    
    @Schema(description = "发布时间")
    private LocalDateTime publishedAt;
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getVersion() {
        return version;
    }
    
    public void setVersion(String version) {
        this.version = version;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public String getDslContent() {
        return dslContent;
    }
    
    public void setDslContent(String dslContent) {
        this.dslContent = dslContent;
    }
    
    public WorkflowStatus getStatus() {
        return status;
    }
    
    public void setStatus(WorkflowStatus status) {
        this.status = status;
    }
    
    public Boolean getActive() {
        return active;
    }
    
    public void setActive(Boolean active) {
        this.active = active;
    }
    
    public List<String> getTags() {
        return tags;
    }
    
    public void setTags(List<String> tags) {
        this.tags = tags;
    }
    
    public Map<String, Object> getVariables() {
        return variables;
    }
    
    public void setVariables(Map<String, Object> variables) {
        this.variables = variables;
    }
    
    public Long getExecutionCount() {
        return executionCount;
    }
    
    public void setExecutionCount(Long executionCount) {
        this.executionCount = executionCount;
    }
    
    public Long getSuccessCount() {
        return successCount;
    }
    
    public void setSuccessCount(Long successCount) {
        this.successCount = successCount;
    }
    
    public Long getFailureCount() {
        return failureCount;
    }
    
    public void setFailureCount(Long failureCount) {
        this.failureCount = failureCount;
    }
    
    public Long getAvgExecutionTime() {
        return avgExecutionTime;
    }
    
    public void setAvgExecutionTime(Long avgExecutionTime) {
        this.avgExecutionTime = avgExecutionTime;
    }
    
    public String getCreatedBy() {
        return createdBy;
    }
    
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public String getUpdatedBy() {
        return updatedBy;
    }
    
    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public String getPublishedBy() {
        return publishedBy;
    }
    
    public void setPublishedBy(String publishedBy) {
        this.publishedBy = publishedBy;
    }
    
    public LocalDateTime getPublishedAt() {
        return publishedAt;
    }
    
    public void setPublishedAt(LocalDateTime publishedAt) {
        this.publishedAt = publishedAt;
    }
    
    /**
     * 计算成功率
     */
    public Double getSuccessRate() {
        if (executionCount == null || executionCount == 0) {
            return null;
        }
        return (double) (successCount != null ? successCount : 0) / executionCount * 100.0;
    }
    
    /**
     * 计算失败率
     */
    public Double getFailureRate() {
        if (executionCount == null || executionCount == 0) {
            return null;
        }
        return (double) (failureCount != null ? failureCount : 0) / executionCount * 100.0;
    }
    
    /**
     * 判断是否已发布
     */
    public boolean isPublished() {
        return status == WorkflowStatus.PUBLISHED;
    }
    
    /**
     * 判断是否可执行
     */
    public boolean isExecutable() {
        return isPublished() && Boolean.TRUE.equals(active);
    }
    
    @Override
    public String toString() {
        return "WorkflowDefinitionDTO{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", version='" + version + '\'' +
                ", status=" + status +
                ", active=" + active +
                ", executionCount=" + executionCount +
                '}';
    }
}
