package com.company.workflow.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户数据传输对象
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Schema(description = "用户信息")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserDTO {
    
    @Schema(description = "用户ID", example = "user_001")
    private String id;
    
    @Schema(description = "用户名", example = "admin")
    private String username;
    
    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;
    
    @Schema(description = "真实姓名", example = "管理员")
    private String realName;
    
    @Schema(description = "手机号", example = "13800138000")
    private String phone;
    
    @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatar;
    
    @Schema(description = "部门", example = "技术部")
    private String department;
    
    @Schema(description = "职位", example = "系统管理员")
    private String position;
    
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;
    
    @Schema(description = "账户是否未过期", example = "true")
    private Boolean accountNonExpired;
    
    @Schema(description = "账户是否未锁定", example = "true")
    private Boolean accountNonLocked;
    
    @Schema(description = "凭证是否未过期", example = "true")
    private Boolean credentialsNonExpired;
    
    @Schema(description = "角色列表")
    private List<RoleDTO> roles;
    
    @Schema(description = "权限列表")
    private List<String> authorities;
    
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;
    
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
    
    @Schema(description = "最后登录时间")
    private LocalDateTime lastLoginAt;
    
    @Schema(description = "最后登录IP", example = "*************")
    private String lastLoginIp;
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getRealName() {
        return realName;
    }
    
    public void setRealName(String realName) {
        this.realName = realName;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public String getAvatar() {
        return avatar;
    }
    
    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }
    
    public String getDepartment() {
        return department;
    }
    
    public void setDepartment(String department) {
        this.department = department;
    }
    
    public String getPosition() {
        return position;
    }
    
    public void setPosition(String position) {
        this.position = position;
    }
    
    public Boolean getEnabled() {
        return enabled;
    }
    
    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }
    
    public Boolean getAccountNonExpired() {
        return accountNonExpired;
    }
    
    public void setAccountNonExpired(Boolean accountNonExpired) {
        this.accountNonExpired = accountNonExpired;
    }
    
    public Boolean getAccountNonLocked() {
        return accountNonLocked;
    }
    
    public void setAccountNonLocked(Boolean accountNonLocked) {
        this.accountNonLocked = accountNonLocked;
    }
    
    public Boolean getCredentialsNonExpired() {
        return credentialsNonExpired;
    }
    
    public void setCredentialsNonExpired(Boolean credentialsNonExpired) {
        this.credentialsNonExpired = credentialsNonExpired;
    }
    
    public List<RoleDTO> getRoles() {
        return roles;
    }
    
    public void setRoles(List<RoleDTO> roles) {
        this.roles = roles;
    }
    
    public List<String> getAuthorities() {
        return authorities;
    }
    
    public void setAuthorities(List<String> authorities) {
        this.authorities = authorities;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public LocalDateTime getLastLoginAt() {
        return lastLoginAt;
    }
    
    public void setLastLoginAt(LocalDateTime lastLoginAt) {
        this.lastLoginAt = lastLoginAt;
    }
    
    public String getLastLoginIp() {
        return lastLoginIp;
    }
    
    public void setLastLoginIp(String lastLoginIp) {
        this.lastLoginIp = lastLoginIp;
    }
    
    /**
     * 检查是否有指定权限
     */
    public boolean hasAuthority(String authority) {
        return authorities != null && authorities.contains(authority);
    }
    
    /**
     * 检查是否为系统管理员
     */
    public boolean isSystemAdmin() {
        return hasAuthority("SYSTEM_ADMIN");
    }
    
    /**
     * 检查是否为租户管理员
     */
    public boolean isTenantAdmin() {
        return hasAuthority("TENANT_ADMIN");
    }
    
    @Override
    public String toString() {
        return "UserDTO{" +
                "id='" + id + '\'' +
                ", username='" + username + '\'' +
                ", email='" + email + '\'' +
                ", realName='" + realName + '\'' +
                ", enabled=" + enabled +
                ", roles=" + (roles != null ? roles.size() : 0) +
                ", authorities=" + (authorities != null ? authorities.size() : 0) +
                '}';
    }
}
