package com.company.workflow.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 分页结果封装
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Schema(description = "分页结果")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PageResult<T> {
    
    @Schema(description = "数据列表")
    private List<T> content;
    
    @Schema(description = "当前页码（从0开始）", example = "0")
    private Integer page;
    
    @Schema(description = "每页大小", example = "20")
    private Integer size;
    
    @Schema(description = "总页数", example = "5")
    private Integer totalPages;
    
    @Schema(description = "总记录数", example = "100")
    private Long totalElements;
    
    @Schema(description = "是否为第一页", example = "true")
    private Boolean first;
    
    @Schema(description = "是否为最后一页", example = "false")
    private Boolean last;
    
    @Schema(description = "是否有下一页", example = "true")
    private Boolean hasNext;
    
    @Schema(description = "是否有上一页", example = "false")
    private Boolean hasPrevious;
    
    @Schema(description = "当前页记录数", example = "20")
    private Integer numberOfElements;
    
    @Schema(description = "是否为空页", example = "false")
    private Boolean empty;
    
    public PageResult() {
    }
    
    public PageResult(List<T> content, Integer page, Integer size, Long totalElements) {
        this.content = content;
        this.page = page;
        this.size = size;
        this.totalElements = totalElements;
        this.numberOfElements = content != null ? content.size() : 0;
        this.empty = this.numberOfElements == 0;
        
        if (size != null && size > 0) {
            this.totalPages = (int) Math.ceil((double) totalElements / size);
            this.first = page == 0;
            this.last = page >= totalPages - 1;
            this.hasNext = page < totalPages - 1;
            this.hasPrevious = page > 0;
        } else {
            this.totalPages = 1;
            this.first = true;
            this.last = true;
            this.hasNext = false;
            this.hasPrevious = false;
        }
    }
    
    /**
     * 从Spring Data的Page对象创建PageResult
     */
    public static <T> PageResult<T> from(Page<T> page) {
        PageResult<T> result = new PageResult<>();
        result.content = page.getContent();
        result.page = page.getNumber();
        result.size = page.getSize();
        result.totalPages = page.getTotalPages();
        result.totalElements = page.getTotalElements();
        result.first = page.isFirst();
        result.last = page.isLast();
        result.hasNext = page.hasNext();
        result.hasPrevious = page.hasPrevious();
        result.numberOfElements = page.getNumberOfElements();
        result.empty = page.isEmpty();
        return result;
    }
    
    /**
     * 创建空的分页结果
     */
    public static <T> PageResult<T> empty() {
        return new PageResult<>(List.of(), 0, 0, 0L);
    }
    
    /**
     * 创建单页结果
     */
    public static <T> PageResult<T> of(List<T> content) {
        return new PageResult<>(content, 0, content.size(), (long) content.size());
    }
    
    // Getters and Setters
    public List<T> getContent() {
        return content;
    }
    
    public void setContent(List<T> content) {
        this.content = content;
        this.numberOfElements = content != null ? content.size() : 0;
        this.empty = this.numberOfElements == 0;
    }
    
    public Integer getPage() {
        return page;
    }
    
    public void setPage(Integer page) {
        this.page = page;
    }
    
    public Integer getSize() {
        return size;
    }
    
    public void setSize(Integer size) {
        this.size = size;
    }
    
    public Integer getTotalPages() {
        return totalPages;
    }
    
    public void setTotalPages(Integer totalPages) {
        this.totalPages = totalPages;
    }
    
    public Long getTotalElements() {
        return totalElements;
    }
    
    public void setTotalElements(Long totalElements) {
        this.totalElements = totalElements;
    }
    
    public Boolean getFirst() {
        return first;
    }
    
    public void setFirst(Boolean first) {
        this.first = first;
    }
    
    public Boolean getLast() {
        return last;
    }
    
    public void setLast(Boolean last) {
        this.last = last;
    }
    
    public Boolean getHasNext() {
        return hasNext;
    }
    
    public void setHasNext(Boolean hasNext) {
        this.hasNext = hasNext;
    }
    
    public Boolean getHasPrevious() {
        return hasPrevious;
    }
    
    public void setHasPrevious(Boolean hasPrevious) {
        this.hasPrevious = hasPrevious;
    }
    
    public Integer getNumberOfElements() {
        return numberOfElements;
    }
    
    public void setNumberOfElements(Integer numberOfElements) {
        this.numberOfElements = numberOfElements;
    }
    
    public Boolean getEmpty() {
        return empty;
    }
    
    public void setEmpty(Boolean empty) {
        this.empty = empty;
    }
    
    @Override
    public String toString() {
        return "PageResult{" +
                "page=" + page +
                ", size=" + size +
                ", totalPages=" + totalPages +
                ", totalElements=" + totalElements +
                ", numberOfElements=" + numberOfElements +
                ", empty=" + empty +
                '}';
    }
}
