package com.company.workflow.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotBlank;

/**
 * 刷新令牌请求
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Schema(description = "刷新令牌请求")
public class RefreshTokenRequest {
    
    @Schema(description = "刷新令牌", example = "eyJhbGciOiJIUzUxMiJ9...", required = true)
    @NotBlank(message = "刷新令牌不能为空")
    private String refreshToken;
    
    // Getters and Setters
    public String getRefreshToken() {
        return refreshToken;
    }
    
    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }
    
    @Override
    public String toString() {
        return "RefreshTokenRequest{" +
                "refreshToken='" + (refreshToken != null ? refreshToken.substring(0, Math.min(20, refreshToken.length())) + "..." : null) + '\'' +
                '}';
    }
}
