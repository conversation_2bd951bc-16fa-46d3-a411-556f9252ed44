package com.company.workflow.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 创建工作流定义请求
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Schema(description = "创建工作流定义请求")
public class WorkflowDefinitionCreateRequest {
    
    @Schema(description = "工作流名称", example = "订单处理流程", required = true)
    @NotBlank(message = "工作流名称不能为空")
    @Size(min = 1, max = 100, message = "工作流名称长度必须在1-100字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9\\u4e00-\\u9fa5_-]+$", message = "工作流名称只能包含字母、数字、中文、下划线和连字符")
    private String name;
    
    @Schema(description = "工作流版本", example = "1.0.0", required = true)
    @NotBlank(message = "工作流版本不能为空")
    @Pattern(regexp = "^\\d+\\.\\d+\\.\\d+$", message = "版本号格式必须为 x.y.z")
    private String version;
    
    @Schema(description = "工作流描述", example = "处理订单的完整流程，包括验证、库存检查、支付等步骤")
    @Size(max = 500, message = "描述长度不能超过500字符")
    private String description;
    
    @Schema(description = "工作流分类", example = "业务流程")
    @Size(max = 50, message = "分类长度不能超过50字符")
    private String category;
    
    @Schema(description = "DSL内容", required = true)
    @NotBlank(message = "DSL内容不能为空")
    @Size(min = 10, max = 100000, message = "DSL内容长度必须在10-100000字符之间")
    private String dslContent;
    
    @Schema(description = "标签列表", example = "[\"订单\", \"支付\", \"库存\"]")
    @Size(max = 10, message = "标签数量不能超过10个")
    private List<String> tags;
    
    // Getters and Setters
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getVersion() {
        return version;
    }
    
    public void setVersion(String version) {
        this.version = version;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public String getDslContent() {
        return dslContent;
    }
    
    public void setDslContent(String dslContent) {
        this.dslContent = dslContent;
    }
    
    public List<String> getTags() {
        return tags;
    }
    
    public void setTags(List<String> tags) {
        this.tags = tags;
    }
    
    @Override
    public String toString() {
        return "WorkflowDefinitionCreateRequest{" +
                "name='" + name + '\'' +
                ", version='" + version + '\'' +
                ", category='" + category + '\'' +
                ", tags=" + tags +
                '}';
    }
}
