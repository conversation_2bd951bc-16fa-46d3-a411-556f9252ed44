package com.company.workflow.dto;

import com.company.workflow.model.entity.InstanceStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 工作流实例数据传输对象
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Schema(description = "工作流实例信息")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WorkflowInstanceDTO {
    
    @Schema(description = "实例ID", example = "inst_001")
    private String id;
    
    @Schema(description = "工作流定义ID", example = "wf_001")
    private String workflowDefinitionId;
    
    @Schema(description = "实例名称", example = "订单处理流程 - 2024-01-01")
    private String instanceName;
    
    @Schema(description = "业务键", example = "ORDER_12345")
    private String businessKey;
    
    @Schema(description = "实例状态")
    private InstanceStatus status;
    
    @Schema(description = "当前节点ID", example = "node_001")
    private String currentNodeId;
    
    @Schema(description = "当前节点名称", example = "验证订单")
    private String currentNodeName;
    
    @Schema(description = "优先级", example = "5")
    private Integer priority;
    
    @Schema(description = "租户ID", example = "tenant_001")
    private String tenantId;
    
    @Schema(description = "父实例ID", example = "inst_parent")
    private String parentInstanceId;
    
    @Schema(description = "根实例ID", example = "inst_root")
    private String rootInstanceId;
    
    @Schema(description = "重试次数", example = "1")
    private Integer retryCount;
    
    @Schema(description = "最大重试次数", example = "3")
    private Integer maxRetryCount;
    
    @Schema(description = "错误信息")
    private String errorMessage;
    
    @Schema(description = "输入数据")
    private Map<String, Object> inputData;
    
    @Schema(description = "输出数据")
    private Map<String, Object> outputData;
    
    @Schema(description = "变量数据")
    private Map<String, Object> variables;
    
    @Schema(description = "创建者", example = "admin")
    private String createdBy;
    
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;
    
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
    
    @Schema(description = "开始时间")
    private LocalDateTime startTime;
    
    @Schema(description = "结束时间")
    private LocalDateTime endTime;
    
    @Schema(description = "执行时长(毫秒)", example = "30000")
    private Long durationMs;
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getWorkflowDefinitionId() {
        return workflowDefinitionId;
    }
    
    public void setWorkflowDefinitionId(String workflowDefinitionId) {
        this.workflowDefinitionId = workflowDefinitionId;
    }
    
    public String getInstanceName() {
        return instanceName;
    }
    
    public void setInstanceName(String instanceName) {
        this.instanceName = instanceName;
    }
    
    public String getBusinessKey() {
        return businessKey;
    }
    
    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }
    
    public InstanceStatus getStatus() {
        return status;
    }
    
    public void setStatus(InstanceStatus status) {
        this.status = status;
    }
    
    public String getCurrentNodeId() {
        return currentNodeId;
    }
    
    public void setCurrentNodeId(String currentNodeId) {
        this.currentNodeId = currentNodeId;
    }
    
    public String getCurrentNodeName() {
        return currentNodeName;
    }
    
    public void setCurrentNodeName(String currentNodeName) {
        this.currentNodeName = currentNodeName;
    }
    
    public Integer getPriority() {
        return priority;
    }
    
    public void setPriority(Integer priority) {
        this.priority = priority;
    }
    
    public String getTenantId() {
        return tenantId;
    }
    
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
    
    public String getParentInstanceId() {
        return parentInstanceId;
    }
    
    public void setParentInstanceId(String parentInstanceId) {
        this.parentInstanceId = parentInstanceId;
    }
    
    public String getRootInstanceId() {
        return rootInstanceId;
    }
    
    public void setRootInstanceId(String rootInstanceId) {
        this.rootInstanceId = rootInstanceId;
    }
    
    public Integer getRetryCount() {
        return retryCount;
    }
    
    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }
    
    public Integer getMaxRetryCount() {
        return maxRetryCount;
    }
    
    public void setMaxRetryCount(Integer maxRetryCount) {
        this.maxRetryCount = maxRetryCount;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public Map<String, Object> getInputData() {
        return inputData;
    }
    
    public void setInputData(Map<String, Object> inputData) {
        this.inputData = inputData;
    }
    
    public Map<String, Object> getOutputData() {
        return outputData;
    }
    
    public void setOutputData(Map<String, Object> outputData) {
        this.outputData = outputData;
    }
    
    public Map<String, Object> getVariables() {
        return variables;
    }
    
    public void setVariables(Map<String, Object> variables) {
        this.variables = variables;
    }
    
    public String getCreatedBy() {
        return createdBy;
    }
    
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public LocalDateTime getStartTime() {
        return startTime;
    }
    
    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }
    
    public LocalDateTime getEndTime() {
        return endTime;
    }
    
    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
    
    public Long getDurationMs() {
        return durationMs;
    }
    
    public void setDurationMs(Long durationMs) {
        this.durationMs = durationMs;
    }
    
    /**
     * 判断是否正在运行
     */
    public boolean isRunning() {
        return status != null && status.isActive();
    }
    
    /**
     * 判断是否已完成
     */
    public boolean isCompleted() {
        return status == InstanceStatus.COMPLETED;
    }
    
    /**
     * 判断是否失败
     */
    public boolean isFailed() {
        return status == InstanceStatus.FAILED;
    }
    
    /**
     * 判断是否可以重试
     */
    public boolean canRetry() {
        return isFailed() && retryCount != null && maxRetryCount != null && retryCount < maxRetryCount;
    }
    
    /**
     * 获取格式化的执行时长
     */
    public String getFormattedDuration() {
        if (durationMs == null) {
            return null;
        }
        
        if (durationMs < 1000) {
            return durationMs + "ms";
        } else if (durationMs < 60000) {
            return String.format("%.2fs", durationMs / 1000.0);
        } else if (durationMs < 3600000) {
            long minutes = durationMs / 60000;
            long seconds = (durationMs % 60000) / 1000;
            return String.format("%dm %ds", minutes, seconds);
        } else {
            long hours = durationMs / 3600000;
            long minutes = (durationMs % 3600000) / 60000;
            long seconds = (durationMs % 60000) / 1000;
            return String.format("%dh %dm %ds", hours, minutes, seconds);
        }
    }
    
    @Override
    public String toString() {
        return "WorkflowInstanceDTO{" +
                "id='" + id + '\'' +
                ", instanceName='" + instanceName + '\'' +
                ", status=" + status +
                ", currentNodeName='" + currentNodeName + '\'' +
                ", durationMs=" + durationMs +
                '}';
    }
}
