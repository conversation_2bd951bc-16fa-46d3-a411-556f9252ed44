package com.company.workflow.model.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 节点执行记录实体类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Entity
@Table(name = "node_executions",
       indexes = {
           @Index(name = "idx_instance_node", columnList = "instanceId,nodeId"),
           @Index(name = "idx_status", columnList = "status"),
           @Index(name = "idx_start_time", columnList = "startTime"),
           @Index(name = "idx_node_type", columnList = "nodeType")
       })
public class NodeExecution {

    @Id
    @Column(length = 64)
    private String id;

    @NotBlank(message = "实例ID不能为空")
    @Column(name = "instance_id", nullable = false, length = 64)
    private String instanceId;

    @NotBlank(message = "节点ID不能为空")
    @Column(name = "node_id", nullable = false, length = 64)
    private String nodeId;

    @Size(max = 255, message = "节点名称长度不能超过255个字符")
    @Column(name = "node_name")
    private String nodeName;

    @NotNull(message = "节点类型不能为空")
    @Enumerated(EnumType.STRING)
    @Column(name = "node_type", nullable = false, length = 20)
    private NodeType nodeType;

    @NotNull(message = "执行状态不能为空")
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private NodeStatus status = NodeStatus.PENDING;

    @Column(name = "input_data", columnDefinition = "JSON")
    private String inputData;

    @Column(name = "output_data", columnDefinition = "JSON")
    private String outputData;

    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    @Column(name = "error_stack_trace", columnDefinition = "LONGTEXT")
    private String errorStackTrace;

    @Column(name = "retry_count")
    private Integer retryCount = 0;

    @Column(name = "max_retry_count")
    private Integer maxRetryCount = 3;

    @Column(name = "start_time")
    private LocalDateTime startTime;

    @Column(name = "end_time")
    private LocalDateTime endTime;

    @Column(name = "duration_ms")
    private Long durationMs;

    @Column(name = "executor_id", length = 64)
    private String executorId;

    @Column(name = "executor_name")
    private String executorName;

    @Column(name = "execution_context", columnDefinition = "JSON")
    private String executionContext;

    @Column(name = "timeout_ms")
    private Long timeoutMs;

    @Column(name = "priority")
    private Integer priority = 0;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "scheduled_time")
    private LocalDateTime scheduledTime;

    @Column(name = "parent_execution_id", length = 64)
    private String parentExecutionId;

    @Column(name = "business_data", columnDefinition = "JSON")
    private String businessData;

    // 关联关系
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "instance_id", insertable = false, updatable = false)
    private WorkflowInstance workflowInstance;

    // 构造函数
    public NodeExecution() {}

    public NodeExecution(String id, String instanceId, String nodeId, NodeType nodeType) {
        this.id = id;
        this.instanceId = instanceId;
        this.nodeId = nodeId;
        this.nodeType = nodeType;
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public NodeType getNodeType() {
        return nodeType;
    }

    public void setNodeType(NodeType nodeType) {
        this.nodeType = nodeType;
    }

    public NodeStatus getStatus() {
        return status;
    }

    public void setStatus(NodeStatus status) {
        this.status = status;
    }

    public String getInputData() {
        return inputData;
    }

    public void setInputData(String inputData) {
        this.inputData = inputData;
    }

    public String getOutputData() {
        return outputData;
    }

    public void setOutputData(String outputData) {
        this.outputData = outputData;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getErrorStackTrace() {
        return errorStackTrace;
    }

    public void setErrorStackTrace(String errorStackTrace) {
        this.errorStackTrace = errorStackTrace;
    }

    public Integer getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }

    public Integer getMaxRetryCount() {
        return maxRetryCount;
    }

    public void setMaxRetryCount(Integer maxRetryCount) {
        this.maxRetryCount = maxRetryCount;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public Long getDurationMs() {
        return durationMs;
    }

    public void setDurationMs(Long durationMs) {
        this.durationMs = durationMs;
    }

    public String getExecutorId() {
        return executorId;
    }

    public void setExecutorId(String executorId) {
        this.executorId = executorId;
    }

    public String getExecutorName() {
        return executorName;
    }

    public void setExecutorName(String executorName) {
        this.executorName = executorName;
    }

    public String getExecutionContext() {
        return executionContext;
    }

    public void setExecutionContext(String executionContext) {
        this.executionContext = executionContext;
    }

    public Long getTimeoutMs() {
        return timeoutMs;
    }

    public void setTimeoutMs(Long timeoutMs) {
        this.timeoutMs = timeoutMs;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getScheduledTime() {
        return scheduledTime;
    }

    public void setScheduledTime(LocalDateTime scheduledTime) {
        this.scheduledTime = scheduledTime;
    }

    public String getParentExecutionId() {
        return parentExecutionId;
    }

    public void setParentExecutionId(String parentExecutionId) {
        this.parentExecutionId = parentExecutionId;
    }

    public String getBusinessData() {
        return businessData;
    }

    public void setBusinessData(String businessData) {
        this.businessData = businessData;
    }

    public WorkflowInstance getWorkflowInstance() {
        return workflowInstance;
    }

    public void setWorkflowInstance(WorkflowInstance workflowInstance) {
        this.workflowInstance = workflowInstance;
    }

    // 业务方法
    public void start() {
        this.status = NodeStatus.RUNNING;
        this.startTime = LocalDateTime.now();
    }

    public void complete() {
        this.status = NodeStatus.COMPLETED;
        this.endTime = LocalDateTime.now();
        if (this.startTime != null) {
            this.durationMs = java.time.Duration.between(this.startTime, this.endTime).toMillis();
        }
    }

    public void fail(String errorMessage) {
        this.status = NodeStatus.FAILED;
        this.endTime = LocalDateTime.now();
        this.errorMessage = errorMessage;
        if (this.startTime != null) {
            this.durationMs = java.time.Duration.between(this.startTime, this.endTime).toMillis();
        }
    }

    public void fail(String errorMessage, String stackTrace) {
        fail(errorMessage);
        this.errorStackTrace = stackTrace;
    }

    public void skip() {
        this.status = NodeStatus.SKIPPED;
        this.endTime = LocalDateTime.now();
        if (this.startTime != null) {
            this.durationMs = java.time.Duration.between(this.startTime, this.endTime).toMillis();
        }
    }

    public void cancel() {
        this.status = NodeStatus.CANCELLED;
        this.endTime = LocalDateTime.now();
        if (this.startTime != null) {
            this.durationMs = java.time.Duration.between(this.startTime, this.endTime).toMillis();
        }
    }

    public void incrementRetryCount() {
        this.retryCount++;
    }

    public boolean canRetry() {
        return this.retryCount < this.maxRetryCount;
    }

    public boolean isRunning() {
        return this.status == NodeStatus.RUNNING;
    }

    public boolean isCompleted() {
        return this.status == NodeStatus.COMPLETED;
    }

    public boolean isFailed() {
        return this.status == NodeStatus.FAILED;
    }

    public boolean isFinished() {
        return this.status == NodeStatus.COMPLETED || 
               this.status == NodeStatus.FAILED || 
               this.status == NodeStatus.SKIPPED || 
               this.status == NodeStatus.CANCELLED;
    }

    public boolean isTimeout() {
        if (this.timeoutMs == null || this.startTime == null) {
            return false;
        }
        long elapsed = java.time.Duration.between(this.startTime, LocalDateTime.now()).toMillis();
        return elapsed > this.timeoutMs;
    }

    // equals, hashCode, toString
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        NodeExecution that = (NodeExecution) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "NodeExecution{" +
                "id='" + id + '\'' +
                ", instanceId='" + instanceId + '\'' +
                ", nodeId='" + nodeId + '\'' +
                ", nodeName='" + nodeName + '\'' +
                ", nodeType=" + nodeType +
                ", status=" + status +
                ", retryCount=" + retryCount +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", durationMs=" + durationMs +
                '}';
    }
}
