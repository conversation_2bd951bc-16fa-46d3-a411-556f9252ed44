package com.company.workflow.model.entity;

/**
 * 节点执行状态枚举
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public enum NodeStatus {
    
    /**
     * 等待执行状态 - 节点等待执行
     */
    PENDING("等待执行", "节点等待执行"),
    
    /**
     * 运行中状态 - 节点正在执行
     */
    RUNNING("运行中", "节点正在执行"),
    
    /**
     * 已完成状态 - 节点执行成功完成
     */
    COMPLETED("已完成", "节点执行成功完成"),
    
    /**
     * 执行失败状态 - 节点执行失败
     */
    FAILED("执行失败", "节点执行失败"),
    
    /**
     * 已跳过状态 - 节点被跳过执行
     */
    SKIPPED("已跳过", "节点被跳过执行"),
    
    /**
     * 已取消状态 - 节点执行被取消
     */
    CANCELLED("已取消", "节点执行被取消"),
    
    /**
     * 等待中状态 - 节点等待外部条件满足
     */
    WAITING("等待中", "节点等待外部条件满足"),
    
    /**
     * 超时状态 - 节点执行超时
     */
    TIMEOUT("超时", "节点执行超时");

    private final String displayName;
    private final String description;

    NodeStatus(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 判断是否为活跃状态（正在执行或等待）
     */
    public boolean isActive() {
        return this == PENDING || this == RUNNING || this == WAITING;
    }

    /**
     * 判断是否为终止状态（已完成、失败、跳过或取消）
     */
    public boolean isTerminated() {
        return this == COMPLETED || this == FAILED || this == SKIPPED || 
               this == CANCELLED || this == TIMEOUT;
    }

    /**
     * 判断是否为成功状态
     */
    public boolean isSuccess() {
        return this == COMPLETED || this == SKIPPED;
    }

    /**
     * 判断是否为失败状态
     */
    public boolean isFailure() {
        return this == FAILED || this == TIMEOUT;
    }

    /**
     * 判断是否可以取消
     */
    public boolean canCancel() {
        return this == PENDING || this == RUNNING || this == WAITING;
    }

    /**
     * 判断是否可以重试
     */
    public boolean canRetry() {
        return this == FAILED || this == TIMEOUT;
    }

    /**
     * 判断是否可以跳过
     */
    public boolean canSkip() {
        return this == PENDING || this == FAILED;
    }

    /**
     * 获取下一个可能的状态
     */
    public NodeStatus[] getNextPossibleStates() {
        return switch (this) {
            case PENDING -> new NodeStatus[]{RUNNING, SKIPPED, CANCELLED};
            case RUNNING -> new NodeStatus[]{COMPLETED, FAILED, CANCELLED, TIMEOUT, WAITING};
            case WAITING -> new NodeStatus[]{RUNNING, COMPLETED, CANCELLED, TIMEOUT};
            case FAILED, TIMEOUT -> new NodeStatus[]{RUNNING, SKIPPED}; // 重试或跳过
            case COMPLETED, SKIPPED, CANCELLED -> new NodeStatus[]{}; // 终态
        };
    }

    /**
     * 获取状态优先级（用于排序）
     */
    public int getPriority() {
        return switch (this) {
            case RUNNING -> 1;
            case WAITING -> 2;
            case PENDING -> 3;
            case FAILED, TIMEOUT -> 4;
            case CANCELLED -> 5;
            case SKIPPED -> 6;
            case COMPLETED -> 7;
        };
    }

    /**
     * 获取状态颜色（用于UI显示）
     */
    public String getColor() {
        return switch (this) {
            case PENDING -> "#FFA500";      // 橙色
            case RUNNING -> "#1890FF";      // 蓝色
            case WAITING -> "#722ED1";      // 紫色
            case COMPLETED -> "#52C41A";    // 绿色
            case SKIPPED -> "#D9D9D9";      // 灰色
            case FAILED -> "#FF4D4F";       // 红色
            case TIMEOUT -> "#FA8C16";      // 橙红色
            case CANCELLED -> "#8C8C8C";    // 深灰色
        };
    }

    /**
     * 获取状态图标（用于UI显示）
     */
    public String getIcon() {
        return switch (this) {
            case PENDING -> "clock-circle";
            case RUNNING -> "loading";
            case WAITING -> "pause-circle";
            case COMPLETED -> "check-circle";
            case SKIPPED -> "minus-circle";
            case FAILED -> "close-circle";
            case TIMEOUT -> "exclamation-circle";
            case CANCELLED -> "stop";
        };
    }

    @Override
    public String toString() {
        return displayName;
    }
}
