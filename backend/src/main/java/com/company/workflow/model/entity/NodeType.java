package com.company.workflow.model.entity;

/**
 * 节点类型枚举
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public enum NodeType {
    
    /**
     * 开始节点 - 工作流的起始节点
     */
    START("开始节点", "工作流的起始节点", "start"),
    
    /**
     * 结束节点 - 工作流的结束节点
     */
    END("结束节点", "工作流的结束节点", "end"),
    
    /**
     * 任务节点 - 执行具体业务逻辑的节点
     */
    TASK("任务节点", "执行具体业务逻辑的节点", "task"),
    
    /**
     * 决策节点 - 根据条件进行分支判断的节点
     */
    DECISION("决策节点", "根据条件进行分支判断的节点", "decision"),
    
    /**
     * 并行网关 - 并行执行多个分支的节点
     */
    PARALLEL("并行网关", "并行执行多个分支的节点", "parallel"),
    
    /**
     * 循环节点 - 循环执行某些操作的节点
     */
    LOOP("循环节点", "循环执行某些操作的节点", "loop"),
    
    /**
     * 子流程节点 - 调用其他工作流的节点
     */
    SUBPROCESS("子流程节点", "调用其他工作流的节点", "subprocess"),
    
    /**
     * 通知节点 - 发送通知消息的节点
     */
    NOTIFICATION("通知节点", "发送通知消息的节点", "notification"),
    
    /**
     * 延迟节点 - 延迟执行的节点
     */
    DELAY("延迟节点", "延迟执行的节点", "delay"),
    
    /**
     * 脚本节点 - 执行脚本代码的节点
     */
    SCRIPT("脚本节点", "执行脚本代码的节点", "script"),
    
    /**
     * HTTP节点 - 调用HTTP服务的节点
     */
    HTTP("HTTP节点", "调用HTTP服务的节点", "http"),
    
    /**
     * SQL节点 - 执行SQL语句的节点
     */
    SQL("SQL节点", "执行SQL语句的节点", "sql"),
    
    /**
     * 人工任务节点 - 需要人工处理的节点
     */
    MANUAL("人工任务节点", "需要人工处理的节点", "manual"),
    
    /**
     * 定时器节点 - 定时触发的节点
     */
    TIMER("定时器节点", "定时触发的节点", "timer");

    private final String displayName;
    private final String description;
    private final String code;

    NodeType(String displayName, String description, String code) {
        this.displayName = displayName;
        this.description = description;
        this.code = code;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    public String getCode() {
        return code;
    }

    /**
     * 判断是否为控制流节点
     */
    public boolean isControlFlow() {
        return this == START || this == END || this == DECISION || 
               this == PARALLEL || this == LOOP;
    }

    /**
     * 判断是否为执行节点
     */
    public boolean isExecutable() {
        return this == TASK || this == SCRIPT || this == HTTP || 
               this == SQL || this == NOTIFICATION || this == SUBPROCESS;
    }

    /**
     * 判断是否为异步节点
     */
    public boolean isAsync() {
        return this == NOTIFICATION || this == HTTP || this == SUBPROCESS;
    }

    /**
     * 判断是否为等待节点
     */
    public boolean isWaiting() {
        return this == DELAY || this == TIMER || this == MANUAL;
    }

    /**
     * 判断是否支持重试
     */
    public boolean supportsRetry() {
        return this == TASK || this == SCRIPT || this == HTTP || 
               this == SQL || this == NOTIFICATION || this == SUBPROCESS;
    }

    /**
     * 判断是否支持超时
     */
    public boolean supportsTimeout() {
        return this == TASK || this == SCRIPT || this == HTTP || 
               this == SQL || this == MANUAL || this == SUBPROCESS;
    }

    /**
     * 判断是否可以并行执行
     */
    public boolean canParallel() {
        return this != START && this != END && this != PARALLEL;
    }

    /**
     * 获取默认超时时间（毫秒）
     */
    public long getDefaultTimeoutMs() {
        return switch (this) {
            case HTTP -> 30000;      // 30秒
            case SQL -> 60000;       // 1分钟
            case SCRIPT -> 30000;    // 30秒
            case TASK -> 300000;     // 5分钟
            case SUBPROCESS -> 600000; // 10分钟
            case MANUAL -> 3600000;  // 1小时
            default -> 0;            // 无超时
        };
    }

    /**
     * 获取默认重试次数
     */
    public int getDefaultRetryCount() {
        return switch (this) {
            case HTTP, NOTIFICATION -> 3;
            case SQL, TASK -> 2;
            case SCRIPT, SUBPROCESS -> 1;
            default -> 0;
        };
    }

    /**
     * 根据代码获取节点类型
     */
    public static NodeType fromCode(String code) {
        for (NodeType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown node type code: " + code);
    }

    @Override
    public String toString() {
        return displayName;
    }
}
