package com.company.workflow.model.entity;

/**
 * 工作流状态枚举
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public enum WorkflowStatus {
    
    /**
     * 草稿状态 - 工作流正在编辑中，尚未发布
     */
    DRAFT("草稿", "工作流正在编辑中，尚未发布"),
    
    /**
     * 已发布状态 - 工作流已发布，可以执行
     */
    PUBLISHED("已发布", "工作流已发布，可以执行"),
    
    /**
     * 已弃用状态 - 工作流已弃用，不再使用
     */
    DEPRECATED("已弃用", "工作流已弃用，不再使用");

    private final String displayName;
    private final String description;

    WorkflowStatus(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 判断是否可以执行
     */
    public boolean isExecutable() {
        return this == PUBLISHED;
    }

    /**
     * 判断是否可以编辑
     */
    public boolean isEditable() {
        return this == DRAFT;
    }

    /**
     * 判断是否可以发布
     */
    public boolean canPublish() {
        return this == DRAFT;
    }

    /**
     * 判断是否可以弃用
     */
    public boolean canDeprecate() {
        return this == PUBLISHED;
    }

    /**
     * 获取下一个可能的状态
     */
    public WorkflowStatus[] getNextPossibleStates() {
        return switch (this) {
            case DRAFT -> new WorkflowStatus[]{PUBLISHED};
            case PUBLISHED -> new WorkflowStatus[]{DEPRECATED};
            case DEPRECATED -> new WorkflowStatus[]{};
        };
    }

    @Override
    public String toString() {
        return displayName;
    }
}
