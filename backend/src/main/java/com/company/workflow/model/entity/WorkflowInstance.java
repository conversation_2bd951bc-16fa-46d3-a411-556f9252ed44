package com.company.workflow.model.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 工作流实例实体类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Entity
@Table(name = "workflow_instances",
       indexes = {
           @Index(name = "idx_status", columnList = "status"),
           @Index(name = "idx_workflow_definition", columnList = "workflowDefinitionId"),
           @Index(name = "idx_created_by", columnList = "createdBy"),
           @Index(name = "idx_start_time", columnList = "startTime"),
           @Index(name = "idx_end_time", columnList = "endTime")
       })
public class WorkflowInstance {

    @Id
    @Column(length = 64)
    private String id;

    @NotBlank(message = "工作流定义ID不能为空")
    @Column(name = "workflow_definition_id", nullable = false, length = 64)
    private String workflowDefinitionId;

    @Size(max = 255, message = "实例名称长度不能超过255个字符")
    @Column(name = "instance_name")
    private String instanceName;

    @NotNull(message = "状态不能为空")
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private InstanceStatus status = InstanceStatus.PENDING;

    @Column(name = "input_data", columnDefinition = "JSON")
    private String inputData;

    @Column(name = "output_data", columnDefinition = "JSON")
    private String outputData;

    @Column(name = "variables", columnDefinition = "JSON")
    private String variables;

    @Column(name = "current_node_id", length = 64)
    private String currentNodeId;

    @Column(name = "current_node_name")
    private String currentNodeName;

    @Column(name = "start_time")
    private LocalDateTime startTime;

    @Column(name = "end_time")
    private LocalDateTime endTime;

    @Column(name = "duration_ms")
    private Long durationMs;

    @Column(name = "priority")
    private Integer priority = 0;

    @Size(max = 64, message = "创建者ID长度不能超过64个字符")
    @Column(name = "created_by", length = 64)
    private String createdBy;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    @Column(name = "retry_count")
    private Integer retryCount = 0;

    @Column(name = "max_retry_count")
    private Integer maxRetryCount = 3;

    @Column(name = "parent_instance_id", length = 64)
    private String parentInstanceId;

    @Column(name = "root_instance_id", length = 64)
    private String rootInstanceId;

    @Column(name = "business_key")
    private String businessKey;

    @Column(name = "tenant_id", length = 64)
    private String tenantId;

    // 关联关系
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "workflow_definition_id", insertable = false, updatable = false)
    private WorkflowDefinition workflowDefinition;

    @OneToMany(mappedBy = "instanceId", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<NodeExecution> nodeExecutions = new ArrayList<>();

    @OneToMany(mappedBy = "parentInstanceId", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<WorkflowInstance> childInstances = new ArrayList<>();

    // 构造函数
    public WorkflowInstance() {}

    public WorkflowInstance(String id, String workflowDefinitionId, String createdBy) {
        this.id = id;
        this.workflowDefinitionId = workflowDefinitionId;
        this.createdBy = createdBy;
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getWorkflowDefinitionId() {
        return workflowDefinitionId;
    }

    public void setWorkflowDefinitionId(String workflowDefinitionId) {
        this.workflowDefinitionId = workflowDefinitionId;
    }

    public String getInstanceName() {
        return instanceName;
    }

    public void setInstanceName(String instanceName) {
        this.instanceName = instanceName;
    }

    public InstanceStatus getStatus() {
        return status;
    }

    public void setStatus(InstanceStatus status) {
        this.status = status;
    }

    public String getInputData() {
        return inputData;
    }

    public void setInputData(String inputData) {
        this.inputData = inputData;
    }

    public String getOutputData() {
        return outputData;
    }

    public void setOutputData(String outputData) {
        this.outputData = outputData;
    }

    public String getVariables() {
        return variables;
    }

    public void setVariables(String variables) {
        this.variables = variables;
    }

    public String getCurrentNodeId() {
        return currentNodeId;
    }

    public void setCurrentNodeId(String currentNodeId) {
        this.currentNodeId = currentNodeId;
    }

    public String getCurrentNodeName() {
        return currentNodeName;
    }

    public void setCurrentNodeName(String currentNodeName) {
        this.currentNodeName = currentNodeName;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public Long getDurationMs() {
        return durationMs;
    }

    public void setDurationMs(Long durationMs) {
        this.durationMs = durationMs;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Integer getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }

    public Integer getMaxRetryCount() {
        return maxRetryCount;
    }

    public void setMaxRetryCount(Integer maxRetryCount) {
        this.maxRetryCount = maxRetryCount;
    }

    public String getParentInstanceId() {
        return parentInstanceId;
    }

    public void setParentInstanceId(String parentInstanceId) {
        this.parentInstanceId = parentInstanceId;
    }

    public String getRootInstanceId() {
        return rootInstanceId;
    }

    public void setRootInstanceId(String rootInstanceId) {
        this.rootInstanceId = rootInstanceId;
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public WorkflowDefinition getWorkflowDefinition() {
        return workflowDefinition;
    }

    public void setWorkflowDefinition(WorkflowDefinition workflowDefinition) {
        this.workflowDefinition = workflowDefinition;
    }

    public List<NodeExecution> getNodeExecutions() {
        return nodeExecutions;
    }

    public void setNodeExecutions(List<NodeExecution> nodeExecutions) {
        this.nodeExecutions = nodeExecutions;
    }

    public List<WorkflowInstance> getChildInstances() {
        return childInstances;
    }

    public void setChildInstances(List<WorkflowInstance> childInstances) {
        this.childInstances = childInstances;
    }

    // 业务方法
    public void start() {
        this.status = InstanceStatus.RUNNING;
        this.startTime = LocalDateTime.now();
    }

    public void complete() {
        this.status = InstanceStatus.COMPLETED;
        this.endTime = LocalDateTime.now();
        if (this.startTime != null) {
            this.durationMs = java.time.Duration.between(this.startTime, this.endTime).toMillis();
        }
    }

    public void fail(String errorMessage) {
        this.status = InstanceStatus.FAILED;
        this.endTime = LocalDateTime.now();
        this.errorMessage = errorMessage;
        if (this.startTime != null) {
            this.durationMs = java.time.Duration.between(this.startTime, this.endTime).toMillis();
        }
    }

    public void cancel() {
        this.status = InstanceStatus.CANCELLED;
        this.endTime = LocalDateTime.now();
        if (this.startTime != null) {
            this.durationMs = java.time.Duration.between(this.startTime, this.endTime).toMillis();
        }
    }

    public void suspend() {
        this.status = InstanceStatus.SUSPENDED;
    }

    public void resume() {
        this.status = InstanceStatus.RUNNING;
    }

    public void incrementRetryCount() {
        this.retryCount++;
    }

    public boolean canRetry() {
        return this.retryCount < this.maxRetryCount;
    }

    public boolean isRunning() {
        return this.status == InstanceStatus.RUNNING;
    }

    public boolean isCompleted() {
        return this.status == InstanceStatus.COMPLETED;
    }

    public boolean isFailed() {
        return this.status == InstanceStatus.FAILED;
    }

    public boolean isCancelled() {
        return this.status == InstanceStatus.CANCELLED;
    }

    public boolean isSuspended() {
        return this.status == InstanceStatus.SUSPENDED;
    }

    public boolean isFinished() {
        return this.status == InstanceStatus.COMPLETED || 
               this.status == InstanceStatus.FAILED || 
               this.status == InstanceStatus.CANCELLED;
    }

    // equals, hashCode, toString
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        WorkflowInstance that = (WorkflowInstance) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "WorkflowInstance{" +
                "id='" + id + '\'' +
                ", workflowDefinitionId='" + workflowDefinitionId + '\'' +
                ", instanceName='" + instanceName + '\'' +
                ", status=" + status +
                ", currentNodeId='" + currentNodeId + '\'' +
                ", createdBy='" + createdBy + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
}
