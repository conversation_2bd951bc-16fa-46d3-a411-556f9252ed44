package com.company.workflow.model.entity;

/**
 * 工作流实例状态枚举
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public enum InstanceStatus {
    
    /**
     * 等待执行状态 - 实例已创建，等待开始执行
     */
    PENDING("等待执行", "实例已创建，等待开始执行"),
    
    /**
     * 运行中状态 - 实例正在执行
     */
    RUNNING("运行中", "实例正在执行"),
    
    /**
     * 已完成状态 - 实例执行成功完成
     */
    COMPLETED("已完成", "实例执行成功完成"),
    
    /**
     * 执行失败状态 - 实例执行失败
     */
    FAILED("执行失败", "实例执行失败"),
    
    /**
     * 已取消状态 - 实例被用户取消
     */
    CANCELLED("已取消", "实例被用户取消"),
    
    /**
     * 已暂停状态 - 实例被暂停，可以恢复执行
     */
    SUSPENDED("已暂停", "实例被暂停，可以恢复执行");

    private final String displayName;
    private final String description;

    InstanceStatus(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 判断是否为活跃状态（正在执行或等待执行）
     */
    public boolean isActive() {
        return this == PENDING || this == RUNNING || this == SUSPENDED;
    }

    /**
     * 判断是否为终止状态（已完成、失败或取消）
     */
    public boolean isTerminated() {
        return this == COMPLETED || this == FAILED || this == CANCELLED;
    }

    /**
     * 判断是否可以取消
     */
    public boolean canCancel() {
        return this == PENDING || this == RUNNING || this == SUSPENDED;
    }

    /**
     * 判断是否可以暂停
     */
    public boolean canSuspend() {
        return this == RUNNING;
    }

    /**
     * 判断是否可以恢复
     */
    public boolean canResume() {
        return this == SUSPENDED;
    }

    /**
     * 判断是否可以重试
     */
    public boolean canRetry() {
        return this == FAILED;
    }

    /**
     * 判断是否为成功状态
     */
    public boolean isSuccess() {
        return this == COMPLETED;
    }

    /**
     * 判断是否为失败状态
     */
    public boolean isFailure() {
        return this == FAILED;
    }

    /**
     * 获取下一个可能的状态
     */
    public InstanceStatus[] getNextPossibleStates() {
        return switch (this) {
            case PENDING -> new InstanceStatus[]{RUNNING, CANCELLED};
            case RUNNING -> new InstanceStatus[]{COMPLETED, FAILED, CANCELLED, SUSPENDED};
            case SUSPENDED -> new InstanceStatus[]{RUNNING, CANCELLED};
            case COMPLETED, FAILED, CANCELLED -> new InstanceStatus[]{};
        };
    }

    /**
     * 获取状态优先级（用于排序）
     */
    public int getPriority() {
        return switch (this) {
            case RUNNING -> 1;
            case PENDING -> 2;
            case SUSPENDED -> 3;
            case FAILED -> 4;
            case CANCELLED -> 5;
            case COMPLETED -> 6;
        };
    }

    @Override
    public String toString() {
        return displayName;
    }
}
