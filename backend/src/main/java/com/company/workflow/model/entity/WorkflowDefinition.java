package com.company.workflow.model.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 工作流定义实体类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Entity
@Table(name = "workflow_definitions", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"name", "version"}),
       indexes = {
           @Index(name = "idx_status", columnList = "status"),
           @Index(name = "idx_category", columnList = "category"),
           @Index(name = "idx_created_by", columnList = "createdBy"),
           @Index(name = "idx_created_at", columnList = "createdAt")
       })
public class WorkflowDefinition {

    @Id
    @Column(length = 64)
    private String id;

    @NotBlank(message = "工作流名称不能为空")
    @Size(max = 255, message = "工作流名称长度不能超过255个字符")
    @Column(nullable = false)
    private String name;

    @NotBlank(message = "版本号不能为空")
    @Size(max = 32, message = "版本号长度不能超过32个字符")
    @Column(nullable = false, length = 32)
    private String version;

    @Size(max = 1000, message = "描述长度不能超过1000个字符")
    @Column(columnDefinition = "TEXT")
    private String description;

    @NotBlank(message = "DSL内容不能为空")
    @Column(name = "dsl_content", columnDefinition = "LONGTEXT", nullable = false)
    private String dslContent;

    @Column(name = "compiled_definition", columnDefinition = "LONGTEXT")
    private String compiledDefinition;

    @NotNull(message = "状态不能为空")
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private WorkflowStatus status = WorkflowStatus.DRAFT;

    @Size(max = 100, message = "分类长度不能超过100个字符")
    @Column(length = 100)
    private String category;

    @Column(columnDefinition = "JSON")
    private String tags;

    @Column(name = "graph_data", columnDefinition = "LONGTEXT")
    private String graphData;

    @Size(max = 64, message = "创建者ID长度不能超过64个字符")
    @Column(name = "created_by", nullable = false, length = 64)
    private String createdBy;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @Size(max = 64, message = "更新者ID长度不能超过64个字符")
    @Column(name = "updated_by", length = 64)
    private String updatedBy;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "published_at")
    private LocalDateTime publishedAt;

    @Size(max = 64, message = "发布者ID长度不能超过64个字符")
    @Column(name = "published_by", length = 64)
    private String publishedBy;

    @Column(name = "is_active")
    private Boolean isActive = false;

    @Column(name = "execution_count")
    private Long executionCount = 0L;

    @Column(name = "success_count")
    private Long successCount = 0L;

    @Column(name = "failure_count")
    private Long failureCount = 0L;

    @Column(name = "avg_execution_time")
    private Long avgExecutionTime = 0L;

    // 关联关系
    @OneToMany(mappedBy = "workflowDefinition", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<WorkflowInstance> instances = new ArrayList<>();

    @OneToMany(mappedBy = "workflowDefinition", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<WorkflowVersion> versions = new ArrayList<>();

    // 构造函数
    public WorkflowDefinition() {}

    public WorkflowDefinition(String id, String name, String version, String dslContent, String createdBy) {
        this.id = id;
        this.name = name;
        this.version = version;
        this.dslContent = dslContent;
        this.createdBy = createdBy;
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDslContent() {
        return dslContent;
    }

    public void setDslContent(String dslContent) {
        this.dslContent = dslContent;
    }

    public String getCompiledDefinition() {
        return compiledDefinition;
    }

    public void setCompiledDefinition(String compiledDefinition) {
        this.compiledDefinition = compiledDefinition;
    }

    public WorkflowStatus getStatus() {
        return status;
    }

    public void setStatus(WorkflowStatus status) {
        this.status = status;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getGraphData() {
        return graphData;
    }

    public void setGraphData(String graphData) {
        this.graphData = graphData;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public LocalDateTime getPublishedAt() {
        return publishedAt;
    }

    public void setPublishedAt(LocalDateTime publishedAt) {
        this.publishedAt = publishedAt;
    }

    public String getPublishedBy() {
        return publishedBy;
    }

    public void setPublishedBy(String publishedBy) {
        this.publishedBy = publishedBy;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public Long getExecutionCount() {
        return executionCount;
    }

    public void setExecutionCount(Long executionCount) {
        this.executionCount = executionCount;
    }

    public Long getSuccessCount() {
        return successCount;
    }

    public void setSuccessCount(Long successCount) {
        this.successCount = successCount;
    }

    public Long getFailureCount() {
        return failureCount;
    }

    public void setFailureCount(Long failureCount) {
        this.failureCount = failureCount;
    }

    public Long getAvgExecutionTime() {
        return avgExecutionTime;
    }

    public void setAvgExecutionTime(Long avgExecutionTime) {
        this.avgExecutionTime = avgExecutionTime;
    }

    public List<WorkflowInstance> getInstances() {
        return instances;
    }

    public void setInstances(List<WorkflowInstance> instances) {
        this.instances = instances;
    }

    public List<WorkflowVersion> getVersions() {
        return versions;
    }

    public void setVersions(List<WorkflowVersion> versions) {
        this.versions = versions;
    }

    // 业务方法
    public void incrementExecutionCount() {
        this.executionCount++;
    }

    public void incrementSuccessCount() {
        this.successCount++;
    }

    public void incrementFailureCount() {
        this.failureCount++;
    }

    public void updateAvgExecutionTime(long executionTime) {
        if (this.executionCount > 0) {
            this.avgExecutionTime = (this.avgExecutionTime * (this.executionCount - 1) + executionTime) / this.executionCount;
        } else {
            this.avgExecutionTime = executionTime;
        }
    }

    public double getSuccessRate() {
        if (executionCount == 0) {
            return 0.0;
        }
        return (double) successCount / executionCount * 100;
    }

    public boolean isPublished() {
        return status == WorkflowStatus.PUBLISHED;
    }

    public boolean isDraft() {
        return status == WorkflowStatus.DRAFT;
    }

    public boolean isDeprecated() {
        return status == WorkflowStatus.DEPRECATED;
    }

    // equals, hashCode, toString
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        WorkflowDefinition that = (WorkflowDefinition) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "WorkflowDefinition{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", version='" + version + '\'' +
                ", status=" + status +
                ", category='" + category + '\'' +
                ", createdBy='" + createdBy + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
}
