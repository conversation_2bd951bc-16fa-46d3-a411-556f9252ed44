package com.company.workflow.model.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 工作流版本实体类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Entity
@Table(name = "workflow_versions",
       uniqueConstraints = @UniqueConstraint(columnNames = {"workflowName", "version"}),
       indexes = {
           @Index(name = "idx_workflow_name", columnList = "workflowName"),
           @Index(name = "idx_is_active", columnList = "isActive"),
           @Index(name = "idx_published_at", columnList = "publishedAt")
       })
public class WorkflowVersion {

    @Id
    @Column(length = 64)
    private String id;

    @NotBlank(message = "工作流名称不能为空")
    @Size(max = 255, message = "工作流名称长度不能超过255个字符")
    @Column(name = "workflow_name", nullable = false)
    private String workflowName;

    @NotBlank(message = "版本号不能为空")
    @Size(max = 32, message = "版本号长度不能超过32个字符")
    @Column(nullable = false, length = 32)
    private String version;

    @NotBlank(message = "定义ID不能为空")
    @Column(name = "definition_id", nullable = false, length = 64)
    private String definitionId;

    @Size(max = 1000, message = "变更日志长度不能超过1000个字符")
    @Column(name = "change_log", columnDefinition = "TEXT")
    private String changeLog;

    @Column(name = "is_active")
    private Boolean isActive = false;

    @Size(max = 64, message = "发布者ID长度不能超过64个字符")
    @Column(name = "published_by", length = 64)
    private String publishedBy;

    @Column(name = "published_at")
    private LocalDateTime publishedAt;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "deprecated_at")
    private LocalDateTime deprecatedAt;

    @Size(max = 64, message = "弃用者ID长度不能超过64个字符")
    @Column(name = "deprecated_by", length = 64)
    private String deprecatedBy;

    @Column(name = "deprecation_reason", columnDefinition = "TEXT")
    private String deprecationReason;

    // 关联关系
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "definition_id", insertable = false, updatable = false)
    private WorkflowDefinition workflowDefinition;

    // 构造函数
    public WorkflowVersion() {}

    public WorkflowVersion(String id, String workflowName, String version, String definitionId) {
        this.id = id;
        this.workflowName = workflowName;
        this.version = version;
        this.definitionId = definitionId;
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getWorkflowName() {
        return workflowName;
    }

    public void setWorkflowName(String workflowName) {
        this.workflowName = workflowName;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getDefinitionId() {
        return definitionId;
    }

    public void setDefinitionId(String definitionId) {
        this.definitionId = definitionId;
    }

    public String getChangeLog() {
        return changeLog;
    }

    public void setChangeLog(String changeLog) {
        this.changeLog = changeLog;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public String getPublishedBy() {
        return publishedBy;
    }

    public void setPublishedBy(String publishedBy) {
        this.publishedBy = publishedBy;
    }

    public LocalDateTime getPublishedAt() {
        return publishedAt;
    }

    public void setPublishedAt(LocalDateTime publishedAt) {
        this.publishedAt = publishedAt;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getDeprecatedAt() {
        return deprecatedAt;
    }

    public void setDeprecatedAt(LocalDateTime deprecatedAt) {
        this.deprecatedAt = deprecatedAt;
    }

    public String getDeprecatedBy() {
        return deprecatedBy;
    }

    public void setDeprecatedBy(String deprecatedBy) {
        this.deprecatedBy = deprecatedBy;
    }

    public String getDeprecationReason() {
        return deprecationReason;
    }

    public void setDeprecationReason(String deprecationReason) {
        this.deprecationReason = deprecationReason;
    }

    public WorkflowDefinition getWorkflowDefinition() {
        return workflowDefinition;
    }

    public void setWorkflowDefinition(WorkflowDefinition workflowDefinition) {
        this.workflowDefinition = workflowDefinition;
    }

    // 业务方法
    public void activate(String publishedBy) {
        this.isActive = true;
        this.publishedBy = publishedBy;
        this.publishedAt = LocalDateTime.now();
    }

    public void deactivate() {
        this.isActive = false;
    }

    public void deprecate(String deprecatedBy, String reason) {
        this.isActive = false;
        this.deprecatedBy = deprecatedBy;
        this.deprecatedAt = LocalDateTime.now();
        this.deprecationReason = reason;
    }

    public boolean isPublished() {
        return this.publishedAt != null;
    }

    public boolean isDeprecated() {
        return this.deprecatedAt != null;
    }

    // equals, hashCode, toString
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        WorkflowVersion that = (WorkflowVersion) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "WorkflowVersion{" +
                "id='" + id + '\'' +
                ", workflowName='" + workflowName + '\'' +
                ", version='" + version + '\'' +
                ", definitionId='" + definitionId + '\'' +
                ", isActive=" + isActive +
                ", publishedAt=" + publishedAt +
                ", createdAt=" + createdAt +
                '}';
    }
}
