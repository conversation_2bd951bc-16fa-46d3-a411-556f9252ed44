package com.company.workflow.controller;

import com.company.workflow.model.entity.InstanceStatus;
import com.company.workflow.service.WorkflowExecutionService;
import com.company.workflow.dto.ApiResponse;
import com.company.workflow.dto.PageResult;
import com.company.workflow.dto.WorkflowInstanceDTO;
import com.company.workflow.dto.WorkflowExecutionRequest;
import com.company.workflow.dto.NodeExecutionDTO;
import com.company.workflow.engine.context.ExecutionContext;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.List;
import java.util.Map;

/**
 * 工作流执行控制器
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/v1/workflow-executions")
@Tag(name = "工作流执行控制", description = "工作流实例的启动、暂停、恢复、取消等执行控制操作")
@Validated
public class WorkflowExecutionController {
    
    @Autowired
    private WorkflowExecutionService workflowExecutionService;
    
    /**
     * 启动工作流实例
     */
    @PostMapping("/start")
    @Operation(summary = "启动工作流实例", description = "启动新的工作流实例")
    @PreAuthorize("hasPermission('WORKFLOW', 'EXECUTE')")
    public ResponseEntity<ApiResponse<WorkflowInstanceDTO>> startWorkflowInstance(
            @Valid @RequestBody WorkflowExecutionRequest request) {
        
        WorkflowInstanceDTO result = workflowExecutionService.startWorkflowInstance(request);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 根据工作流名称启动实例
     */
    @PostMapping("/start-by-name/{workflowName}")
    @Operation(summary = "根据名称启动工作流", description = "根据工作流名称启动最新版本的工作流实例")
    @PreAuthorize("hasPermission('WORKFLOW', 'EXECUTE')")
    public ResponseEntity<ApiResponse<WorkflowInstanceDTO>> startWorkflowByName(
            @Parameter(description = "工作流名称") @PathVariable String workflowName,
            @RequestBody(required = false) Map<String, Object> inputData) {
        
        WorkflowInstanceDTO result = workflowExecutionService.startWorkflowByName(workflowName, inputData);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 获取工作流实例详情
     */
    @GetMapping("/{instanceId}")
    @Operation(summary = "获取工作流实例", description = "根据ID获取工作流实例详情")
    @PreAuthorize("hasPermission('INSTANCE', 'READ')")
    public ResponseEntity<ApiResponse<WorkflowInstanceDTO>> getWorkflowInstance(
            @Parameter(description = "工作流实例ID") @PathVariable String instanceId) {
        
        WorkflowInstanceDTO result = workflowExecutionService.getWorkflowInstance(instanceId);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 暂停工作流实例
     */
    @PostMapping("/{instanceId}/suspend")
    @Operation(summary = "暂停工作流实例", description = "暂停正在运行的工作流实例")
    @PreAuthorize("hasPermission('INSTANCE', 'UPDATE')")
    public ResponseEntity<ApiResponse<WorkflowInstanceDTO>> suspendWorkflowInstance(
            @Parameter(description = "工作流实例ID") @PathVariable String instanceId) {
        
        WorkflowInstanceDTO result = workflowExecutionService.suspendWorkflowInstance(instanceId);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 恢复工作流实例
     */
    @PostMapping("/{instanceId}/resume")
    @Operation(summary = "恢复工作流实例", description = "恢复已暂停的工作流实例")
    @PreAuthorize("hasPermission('INSTANCE', 'UPDATE')")
    public ResponseEntity<ApiResponse<WorkflowInstanceDTO>> resumeWorkflowInstance(
            @Parameter(description = "工作流实例ID") @PathVariable String instanceId) {
        
        WorkflowInstanceDTO result = workflowExecutionService.resumeWorkflowInstance(instanceId);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 取消工作流实例
     */
    @PostMapping("/{instanceId}/cancel")
    @Operation(summary = "取消工作流实例", description = "取消正在运行或暂停的工作流实例")
    @PreAuthorize("hasPermission('INSTANCE', 'UPDATE')")
    public ResponseEntity<ApiResponse<WorkflowInstanceDTO>> cancelWorkflowInstance(
            @Parameter(description = "工作流实例ID") @PathVariable String instanceId) {
        
        WorkflowInstanceDTO result = workflowExecutionService.cancelWorkflowInstance(instanceId);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 重试失败的工作流实例
     */
    @PostMapping("/{instanceId}/retry")
    @Operation(summary = "重试工作流实例", description = "重试失败的工作流实例")
    @PreAuthorize("hasPermission('INSTANCE', 'UPDATE')")
    public ResponseEntity<ApiResponse<WorkflowInstanceDTO>> retryWorkflowInstance(
            @Parameter(description = "工作流实例ID") @PathVariable String instanceId) {
        
        WorkflowInstanceDTO result = workflowExecutionService.retryWorkflowInstance(instanceId);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 分页查询工作流实例
     */
    @GetMapping
    @Operation(summary = "分页查询工作流实例", description = "分页查询工作流实例列表")
    @PreAuthorize("hasPermission('INSTANCE', 'READ')")
    public ResponseEntity<ApiResponse<PageResult<WorkflowInstanceDTO>>> getWorkflowInstances(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") @Min(0) Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "createdAt") String sort,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "desc") String direction,
            @Parameter(description = "工作流定义ID") @RequestParam(required = false) String workflowDefinitionId,
            @Parameter(description = "实例状态") @RequestParam(required = false) InstanceStatus status,
            @Parameter(description = "创建者") @RequestParam(required = false) String createdBy,
            @Parameter(description = "业务键") @RequestParam(required = false) String businessKey,
            @Parameter(description = "关键词搜索") @RequestParam(required = false) String keyword) {
        
        Sort.Direction sortDirection = "desc".equalsIgnoreCase(direction) ? Sort.Direction.DESC : Sort.Direction.ASC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));
        
        PageResult<WorkflowInstanceDTO> result = workflowExecutionService.getWorkflowInstances(
                pageable, workflowDefinitionId, status, createdBy, businessKey, keyword);
        
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 获取工作流实例的节点执行记录
     */
    @GetMapping("/{instanceId}/node-executions")
    @Operation(summary = "获取节点执行记录", description = "获取工作流实例的所有节点执行记录")
    @PreAuthorize("hasPermission('INSTANCE', 'READ')")
    public ResponseEntity<ApiResponse<List<NodeExecutionDTO>>> getNodeExecutions(
            @Parameter(description = "工作流实例ID") @PathVariable String instanceId) {
        
        List<NodeExecutionDTO> result = workflowExecutionService.getNodeExecutions(instanceId);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 获取工作流实例的执行上下文
     */
    @GetMapping("/{instanceId}/context")
    @Operation(summary = "获取执行上下文", description = "获取工作流实例的执行上下文信息")
    @PreAuthorize("hasPermission('INSTANCE', 'READ')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getExecutionContext(
            @Parameter(description = "工作流实例ID") @PathVariable String instanceId) {
        
        Map<String, Object> result = workflowExecutionService.getExecutionContext(instanceId);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 获取工作流实例的变量
     */
    @GetMapping("/{instanceId}/variables")
    @Operation(summary = "获取实例变量", description = "获取工作流实例的所有变量")
    @PreAuthorize("hasPermission('INSTANCE', 'READ')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getInstanceVariables(
            @Parameter(description = "工作流实例ID") @PathVariable String instanceId) {
        
        Map<String, Object> result = workflowExecutionService.getInstanceVariables(instanceId);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 更新工作流实例的变量
     */
    @PutMapping("/{instanceId}/variables")
    @Operation(summary = "更新实例变量", description = "更新工作流实例的变量")
    @PreAuthorize("hasPermission('INSTANCE', 'UPDATE')")
    public ResponseEntity<ApiResponse<Void>> updateInstanceVariables(
            @Parameter(description = "工作流实例ID") @PathVariable String instanceId,
            @RequestBody Map<String, Object> variables) {
        
        workflowExecutionService.updateInstanceVariables(instanceId, variables);
        return ResponseEntity.ok(ApiResponse.success());
    }
    
    /**
     * 获取运行中的工作流实例
     */
    @GetMapping("/active")
    @Operation(summary = "获取运行中的实例", description = "获取所有运行中的工作流实例")
    @PreAuthorize("hasPermission('INSTANCE', 'READ')")
    public ResponseEntity<ApiResponse<PageResult<WorkflowInstanceDTO>>> getActiveInstances(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") @Min(0) Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        PageResult<WorkflowInstanceDTO> result = workflowExecutionService.getActiveInstances(pageable);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 获取已完成的工作流实例
     */
    @GetMapping("/completed")
    @Operation(summary = "获取已完成的实例", description = "获取所有已完成的工作流实例")
    @PreAuthorize("hasPermission('INSTANCE', 'READ')")
    public ResponseEntity<ApiResponse<PageResult<WorkflowInstanceDTO>>> getCompletedInstances(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") @Min(0) Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "endTime"));
        PageResult<WorkflowInstanceDTO> result = workflowExecutionService.getCompletedInstances(pageable);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 获取工作流实例统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取实例统计信息", description = "获取工作流实例的统计信息")
    @PreAuthorize("hasPermission('INSTANCE', 'READ')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getInstanceStatistics() {
        
        Map<String, Object> result = workflowExecutionService.getInstanceStatistics();
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 获取工作流实例的执行日志
     */
    @GetMapping("/{instanceId}/logs")
    @Operation(summary = "获取执行日志", description = "获取工作流实例的执行日志")
    @PreAuthorize("hasPermission('INSTANCE', 'READ')")
    public ResponseEntity<ApiResponse<List<Map<String, Object>>>> getExecutionLogs(
            @Parameter(description = "工作流实例ID") @PathVariable String instanceId,
            @Parameter(description = "日志级别") @RequestParam(required = false) String level,
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "100") @Min(1) @Max(1000) Integer limit) {
        
        List<Map<String, Object>> result = workflowExecutionService.getExecutionLogs(instanceId, level, limit);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 批量操作工作流实例
     */
    @PostMapping("/batch/{action}")
    @Operation(summary = "批量操作实例", description = "批量执行工作流实例操作（暂停、恢复、取消）")
    @PreAuthorize("hasPermission('INSTANCE', 'UPDATE')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> batchOperateInstances(
            @Parameter(description = "操作类型：suspend、resume、cancel") @PathVariable String action,
            @RequestBody List<String> instanceIds) {
        
        Map<String, Object> result = workflowExecutionService.batchOperateInstances(action, instanceIds);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 删除工作流实例
     */
    @DeleteMapping("/{instanceId}")
    @Operation(summary = "删除工作流实例", description = "删除指定的工作流实例（仅限已完成或已取消的实例）")
    @PreAuthorize("hasPermission('INSTANCE', 'DELETE')")
    public ResponseEntity<ApiResponse<Void>> deleteWorkflowInstance(
            @Parameter(description = "工作流实例ID") @PathVariable String instanceId) {
        
        workflowExecutionService.deleteWorkflowInstance(instanceId);
        return ResponseEntity.ok(ApiResponse.success());
    }
    
    /**
     * 清理历史实例
     */
    @PostMapping("/cleanup")
    @Operation(summary = "清理历史实例", description = "清理指定时间之前的已完成实例")
    @PreAuthorize("hasPermission('INSTANCE', 'DELETE')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> cleanupHistoryInstances(
            @Parameter(description = "保留天数") @RequestParam @Min(1) Integer retentionDays) {
        
        Map<String, Object> result = workflowExecutionService.cleanupHistoryInstances(retentionDays);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
}
