package com.company.workflow.controller;

import com.company.workflow.model.entity.WorkflowDefinition;
import com.company.workflow.model.entity.WorkflowStatus;
import com.company.workflow.service.WorkflowDefinitionService;
import com.company.workflow.dto.ApiResponse;
import com.company.workflow.dto.PageResult;
import com.company.workflow.dto.WorkflowDefinitionDTO;
import com.company.workflow.dto.WorkflowDefinitionCreateRequest;
import com.company.workflow.dto.WorkflowDefinitionUpdateRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.List;
import java.util.Map;

/**
 * 工作流定义管理控制器
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/v1/workflow-definitions")
@Tag(name = "工作流定义管理", description = "工作流定义的创建、查询、更新、删除等操作")
@Validated
public class WorkflowDefinitionController {
    
    @Autowired
    private WorkflowDefinitionService workflowDefinitionService;
    
    /**
     * 创建工作流定义
     */
    @PostMapping
    @Operation(summary = "创建工作流定义", description = "创建新的工作流定义")
    @PreAuthorize("hasPermission('WORKFLOW', 'CREATE')")
    public ResponseEntity<ApiResponse<WorkflowDefinitionDTO>> createWorkflowDefinition(
            @Valid @RequestBody WorkflowDefinitionCreateRequest request) {
        
        WorkflowDefinitionDTO result = workflowDefinitionService.createWorkflowDefinition(request);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 根据ID获取工作流定义
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取工作流定义", description = "根据ID获取工作流定义详情")
    @PreAuthorize("hasPermission('WORKFLOW', 'READ')")
    public ResponseEntity<ApiResponse<WorkflowDefinitionDTO>> getWorkflowDefinition(
            @Parameter(description = "工作流定义ID") @PathVariable String id) {
        
        WorkflowDefinitionDTO result = workflowDefinitionService.getWorkflowDefinition(id);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 更新工作流定义
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新工作流定义", description = "更新指定的工作流定义")
    @PreAuthorize("hasPermission('WORKFLOW', 'UPDATE')")
    public ResponseEntity<ApiResponse<WorkflowDefinitionDTO>> updateWorkflowDefinition(
            @Parameter(description = "工作流定义ID") @PathVariable String id,
            @Valid @RequestBody WorkflowDefinitionUpdateRequest request) {
        
        WorkflowDefinitionDTO result = workflowDefinitionService.updateWorkflowDefinition(id, request);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 删除工作流定义
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除工作流定义", description = "删除指定的工作流定义")
    @PreAuthorize("hasPermission('WORKFLOW', 'DELETE')")
    public ResponseEntity<ApiResponse<Void>> deleteWorkflowDefinition(
            @Parameter(description = "工作流定义ID") @PathVariable String id) {
        
        workflowDefinitionService.deleteWorkflowDefinition(id);
        return ResponseEntity.ok(ApiResponse.success());
    }
    
    /**
     * 分页查询工作流定义
     */
    @GetMapping
    @Operation(summary = "分页查询工作流定义", description = "分页查询工作流定义列表")
    @PreAuthorize("hasPermission('WORKFLOW', 'READ')")
    public ResponseEntity<ApiResponse<PageResult<WorkflowDefinitionDTO>>> getWorkflowDefinitions(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") @Min(0) Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "createdAt") String sort,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "desc") String direction,
            @Parameter(description = "工作流名称") @RequestParam(required = false) String name,
            @Parameter(description = "工作流状态") @RequestParam(required = false) WorkflowStatus status,
            @Parameter(description = "分类") @RequestParam(required = false) String category,
            @Parameter(description = "创建者") @RequestParam(required = false) String createdBy,
            @Parameter(description = "关键词搜索") @RequestParam(required = false) String keyword) {
        
        Sort.Direction sortDirection = "desc".equalsIgnoreCase(direction) ? Sort.Direction.DESC : Sort.Direction.ASC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));
        
        PageResult<WorkflowDefinitionDTO> result = workflowDefinitionService.getWorkflowDefinitions(
                pageable, name, status, category, createdBy, keyword);
        
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 发布工作流定义
     */
    @PostMapping("/{id}/publish")
    @Operation(summary = "发布工作流定义", description = "发布工作流定义，使其可以被执行")
    @PreAuthorize("hasPermission('WORKFLOW', 'MANAGE')")
    public ResponseEntity<ApiResponse<WorkflowDefinitionDTO>> publishWorkflowDefinition(
            @Parameter(description = "工作流定义ID") @PathVariable String id) {
        
        WorkflowDefinitionDTO result = workflowDefinitionService.publishWorkflowDefinition(id);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 停用工作流定义
     */
    @PostMapping("/{id}/deactivate")
    @Operation(summary = "停用工作流定义", description = "停用工作流定义，使其不能被执行")
    @PreAuthorize("hasPermission('WORKFLOW', 'MANAGE')")
    public ResponseEntity<ApiResponse<WorkflowDefinitionDTO>> deactivateWorkflowDefinition(
            @Parameter(description = "工作流定义ID") @PathVariable String id) {
        
        WorkflowDefinitionDTO result = workflowDefinitionService.deactivateWorkflowDefinition(id);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 弃用工作流定义
     */
    @PostMapping("/{id}/deprecate")
    @Operation(summary = "弃用工作流定义", description = "弃用工作流定义")
    @PreAuthorize("hasPermission('WORKFLOW', 'MANAGE')")
    public ResponseEntity<ApiResponse<WorkflowDefinitionDTO>> deprecateWorkflowDefinition(
            @Parameter(description = "工作流定义ID") @PathVariable String id) {
        
        WorkflowDefinitionDTO result = workflowDefinitionService.deprecateWorkflowDefinition(id);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 验证工作流DSL
     */
    @PostMapping("/validate")
    @Operation(summary = "验证工作流DSL", description = "验证工作流DSL语法和语义")
    @PreAuthorize("hasPermission('WORKFLOW', 'CREATE')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> validateWorkflowDsl(
            @RequestBody Map<String, String> request) {
        
        String dslContent = request.get("dslContent");
        Map<String, Object> result = workflowDefinitionService.validateWorkflowDsl(dslContent);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 复制工作流定义
     */
    @PostMapping("/{id}/copy")
    @Operation(summary = "复制工作流定义", description = "复制现有的工作流定义")
    @PreAuthorize("hasPermission('WORKFLOW', 'CREATE')")
    public ResponseEntity<ApiResponse<WorkflowDefinitionDTO>> copyWorkflowDefinition(
            @Parameter(description = "工作流定义ID") @PathVariable String id,
            @RequestBody Map<String, String> request) {
        
        String newName = request.get("name");
        String newVersion = request.get("version");
        
        WorkflowDefinitionDTO result = workflowDefinitionService.copyWorkflowDefinition(id, newName, newVersion);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 获取工作流定义的所有版本
     */
    @GetMapping("/by-name/{name}/versions")
    @Operation(summary = "获取工作流版本列表", description = "获取指定名称工作流的所有版本")
    @PreAuthorize("hasPermission('WORKFLOW', 'READ')")
    public ResponseEntity<ApiResponse<List<WorkflowDefinitionDTO>>> getWorkflowVersions(
            @Parameter(description = "工作流名称") @PathVariable String name) {
        
        List<WorkflowDefinitionDTO> result = workflowDefinitionService.getWorkflowVersions(name);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 获取工作流定义的最新版本
     */
    @GetMapping("/by-name/{name}/latest")
    @Operation(summary = "获取最新版本", description = "获取指定名称工作流的最新版本")
    @PreAuthorize("hasPermission('WORKFLOW', 'READ')")
    public ResponseEntity<ApiResponse<WorkflowDefinitionDTO>> getLatestWorkflowVersion(
            @Parameter(description = "工作流名称") @PathVariable String name) {
        
        WorkflowDefinitionDTO result = workflowDefinitionService.getLatestWorkflowVersion(name);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 获取工作流定义统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取统计信息", description = "获取工作流定义的统计信息")
    @PreAuthorize("hasPermission('WORKFLOW', 'READ')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getWorkflowStatistics() {
        
        Map<String, Object> result = workflowDefinitionService.getWorkflowStatistics();
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 获取热门工作流定义
     */
    @GetMapping("/popular")
    @Operation(summary = "获取热门工作流", description = "获取执行次数最多的工作流定义")
    @PreAuthorize("hasPermission('WORKFLOW', 'READ')")
    public ResponseEntity<ApiResponse<List<WorkflowDefinitionDTO>>> getPopularWorkflows(
            @Parameter(description = "返回数量") @RequestParam(defaultValue = "10") @Min(1) @Max(50) Integer limit) {
        
        List<WorkflowDefinitionDTO> result = workflowDefinitionService.getPopularWorkflows(limit);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 获取最近创建的工作流定义
     */
    @GetMapping("/recent")
    @Operation(summary = "获取最近创建的工作流", description = "获取最近创建的工作流定义")
    @PreAuthorize("hasPermission('WORKFLOW', 'READ')")
    public ResponseEntity<ApiResponse<List<WorkflowDefinitionDTO>>> getRecentWorkflows(
            @Parameter(description = "返回数量") @RequestParam(defaultValue = "10") @Min(1) @Max(50) Integer limit) {
        
        List<WorkflowDefinitionDTO> result = workflowDefinitionService.getRecentWorkflows(limit);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 搜索工作流定义
     */
    @GetMapping("/search")
    @Operation(summary = "搜索工作流定义", description = "全文搜索工作流定义")
    @PreAuthorize("hasPermission('WORKFLOW', 'READ')")
    public ResponseEntity<ApiResponse<PageResult<WorkflowDefinitionDTO>>> searchWorkflowDefinitions(
            @Parameter(description = "搜索关键词") @RequestParam String keyword,
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") @Min(0) Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size) {
        
        Pageable pageable = PageRequest.of(page, size);
        PageResult<WorkflowDefinitionDTO> result = workflowDefinitionService.searchWorkflowDefinitions(keyword, pageable);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 导出工作流定义
     */
    @GetMapping("/{id}/export")
    @Operation(summary = "导出工作流定义", description = "导出工作流定义为JSON格式")
    @PreAuthorize("hasPermission('WORKFLOW', 'READ')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> exportWorkflowDefinition(
            @Parameter(description = "工作流定义ID") @PathVariable String id) {
        
        Map<String, Object> result = workflowDefinitionService.exportWorkflowDefinition(id);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    /**
     * 导入工作流定义
     */
    @PostMapping("/import")
    @Operation(summary = "导入工作流定义", description = "从JSON格式导入工作流定义")
    @PreAuthorize("hasPermission('WORKFLOW', 'CREATE')")
    public ResponseEntity<ApiResponse<WorkflowDefinitionDTO>> importWorkflowDefinition(
            @RequestBody Map<String, Object> workflowData) {
        
        WorkflowDefinitionDTO result = workflowDefinitionService.importWorkflowDefinition(workflowData);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
}
