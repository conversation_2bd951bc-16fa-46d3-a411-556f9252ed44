package com.company.workflow.controller;

import com.company.workflow.dto.ApiResponse;
import com.company.workflow.dto.LoginRequest;
import com.company.workflow.dto.LoginResponse;
import com.company.workflow.dto.RefreshTokenRequest;
import com.company.workflow.dto.RegisterRequest;
import com.company.workflow.dto.UserDTO;
import com.company.workflow.security.JwtTokenUtil;
import com.company.workflow.security.WorkflowUserPrincipal;
import com.company.workflow.service.AuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 认证控制器
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/v1/auth")
@Tag(name = "认证管理", description = "用户登录、注册、令牌刷新等认证相关操作")
@Validated
public class AuthController {
    
    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);
    
    @Autowired
    private AuthenticationManager authenticationManager;
    
    @Autowired
    private JwtTokenUtil jwtTokenUtil;
    
    @Autowired
    private AuthService authService;
    
    /**
     * 用户登录
     */
    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "用户名密码登录，返回JWT令牌")
    public ResponseEntity<ApiResponse<LoginResponse>> login(@Valid @RequestBody LoginRequest loginRequest,
                                                           HttpServletRequest request) {
        
        logger.info("用户登录请求: {}", loginRequest.getUsername());
        
        try {
            // 认证用户
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(
                            loginRequest.getUsername(),
                            loginRequest.getPassword()
                    )
            );
            
            // 设置安全上下文
            SecurityContextHolder.getContext().setAuthentication(authentication);
            
            // 生成令牌
            String accessToken = jwtTokenUtil.generateAccessToken(authentication);
            String refreshToken = jwtTokenUtil.generateRefreshToken(authentication);
            
            // 获取用户信息
            WorkflowUserPrincipal userPrincipal = (WorkflowUserPrincipal) authentication.getPrincipal();
            
            // 记录登录日志
            authService.recordLoginLog(userPrincipal.getId(), request.getRemoteAddr(), 
                                     request.getHeader("User-Agent"), true, null);
            
            // 构建响应
            LoginResponse response = new LoginResponse();
            response.setAccessToken(accessToken);
            response.setRefreshToken(refreshToken);
            response.setTokenType("Bearer");
            response.setExpiresIn(86400); // 24小时
            response.setUser(authService.convertToUserDTO(userPrincipal));
            
            logger.info("用户 {} 登录成功", loginRequest.getUsername());
            return ResponseEntity.ok(ApiResponse.success("登录成功", response));
            
        } catch (Exception e) {
            logger.error("用户 {} 登录失败: {}", loginRequest.getUsername(), e.getMessage());
            
            // 记录失败日志
            authService.recordLoginLog(null, request.getRemoteAddr(), 
                                     request.getHeader("User-Agent"), false, e.getMessage());
            
            return ResponseEntity.ok(ApiResponse.error("用户名或密码错误"));
        }
    }
    
    /**
     * 用户注册
     */
    @PostMapping("/register")
    @Operation(summary = "用户注册", description = "注册新用户账户")
    public ResponseEntity<ApiResponse<UserDTO>> register(@Valid @RequestBody RegisterRequest registerRequest) {
        
        logger.info("用户注册请求: {}", registerRequest.getUsername());
        
        try {
            UserDTO user = authService.registerUser(registerRequest);
            
            logger.info("用户 {} 注册成功", registerRequest.getUsername());
            return ResponseEntity.ok(ApiResponse.success("注册成功", user));
            
        } catch (Exception e) {
            logger.error("用户 {} 注册失败: {}", registerRequest.getUsername(), e.getMessage());
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * 刷新令牌
     */
    @PostMapping("/refresh")
    @Operation(summary = "刷新令牌", description = "使用刷新令牌获取新的访问令牌")
    public ResponseEntity<ApiResponse<LoginResponse>> refreshToken(@Valid @RequestBody RefreshTokenRequest request) {
        
        logger.debug("刷新令牌请求");
        
        try {
            String refreshToken = request.getRefreshToken();
            
            if (!jwtTokenUtil.validateToken(refreshToken) || !jwtTokenUtil.isRefreshToken(refreshToken)) {
                return ResponseEntity.ok(ApiResponse.error("无效的刷新令牌"));
            }
            
            // 生成新的访问令牌
            String newAccessToken = jwtTokenUtil.refreshToken(refreshToken);
            
            // 获取用户信息
            String username = jwtTokenUtil.getUsernameFromToken(refreshToken);
            UserDTO user = authService.getUserByUsername(username);
            
            // 构建响应
            LoginResponse response = new LoginResponse();
            response.setAccessToken(newAccessToken);
            response.setRefreshToken(refreshToken); // 刷新令牌保持不变
            response.setTokenType("Bearer");
            response.setExpiresIn(86400);
            response.setUser(user);
            
            logger.debug("令牌刷新成功: {}", username);
            return ResponseEntity.ok(ApiResponse.success("令牌刷新成功", response));
            
        } catch (Exception e) {
            logger.error("刷新令牌失败: {}", e.getMessage());
            return ResponseEntity.ok(ApiResponse.error("刷新令牌失败"));
        }
    }
    
    /**
     * 用户登出
     */
    @PostMapping("/logout")
    @Operation(summary = "用户登出", description = "用户登出，清除认证信息")
    public ResponseEntity<ApiResponse<Void>> logout(HttpServletRequest request) {
        
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            
            if (authentication != null && authentication.getPrincipal() instanceof WorkflowUserPrincipal) {
                WorkflowUserPrincipal userPrincipal = (WorkflowUserPrincipal) authentication.getPrincipal();
                
                // 记录登出日志
                authService.recordLogoutLog(userPrincipal.getId(), request.getRemoteAddr());
                
                logger.info("用户 {} 登出", userPrincipal.getUsername());
            }
            
            // 清除安全上下文
            SecurityContextHolder.clearContext();
            
            return ResponseEntity.ok(ApiResponse.success("登出成功"));
            
        } catch (Exception e) {
            logger.error("用户登出失败: {}", e.getMessage());
            return ResponseEntity.ok(ApiResponse.error("登出失败"));
        }
    }
    
    /**
     * 获取当前用户信息
     */
    @GetMapping("/me")
    @Operation(summary = "获取当前用户", description = "获取当前登录用户的详细信息")
    public ResponseEntity<ApiResponse<UserDTO>> getCurrentUser() {
        
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            
            if (authentication != null && authentication.getPrincipal() instanceof WorkflowUserPrincipal) {
                WorkflowUserPrincipal userPrincipal = (WorkflowUserPrincipal) authentication.getPrincipal();
                UserDTO user = authService.convertToUserDTO(userPrincipal);
                
                return ResponseEntity.ok(ApiResponse.success(user));
            }
            
            return ResponseEntity.ok(ApiResponse.error("未找到当前用户信息"));
            
        } catch (Exception e) {
            logger.error("获取当前用户信息失败: {}", e.getMessage());
            return ResponseEntity.ok(ApiResponse.error("获取用户信息失败"));
        }
    }
    
    /**
     * 验证令牌
     */
    @PostMapping("/validate")
    @Operation(summary = "验证令牌", description = "验证JWT令牌的有效性")
    public ResponseEntity<ApiResponse<Boolean>> validateToken(@RequestParam String token) {
        
        try {
            boolean isValid = jwtTokenUtil.validateToken(token);
            
            return ResponseEntity.ok(ApiResponse.success("令牌验证完成", isValid));
            
        } catch (Exception e) {
            logger.error("令牌验证失败: {}", e.getMessage());
            return ResponseEntity.ok(ApiResponse.success("令牌验证完成", false));
        }
    }
    
    /**
     * 修改密码
     */
    @PostMapping("/change-password")
    @Operation(summary = "修改密码", description = "修改当前用户密码")
    public ResponseEntity<ApiResponse<Void>> changePassword(@RequestParam String oldPassword,
                                                           @RequestParam String newPassword) {
        
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            
            if (authentication != null && authentication.getPrincipal() instanceof WorkflowUserPrincipal) {
                WorkflowUserPrincipal userPrincipal = (WorkflowUserPrincipal) authentication.getPrincipal();
                
                authService.changePassword(userPrincipal.getId(), oldPassword, newPassword);
                
                logger.info("用户 {} 修改密码成功", userPrincipal.getUsername());
                return ResponseEntity.ok(ApiResponse.success("密码修改成功"));
            }
            
            return ResponseEntity.ok(ApiResponse.error("未找到当前用户信息"));
            
        } catch (Exception e) {
            logger.error("修改密码失败: {}", e.getMessage());
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * 忘记密码
     */
    @PostMapping("/forgot-password")
    @Operation(summary = "忘记密码", description = "发送密码重置邮件")
    public ResponseEntity<ApiResponse<Void>> forgotPassword(@RequestParam String email) {
        
        try {
            authService.sendPasswordResetEmail(email);
            
            logger.info("密码重置邮件已发送: {}", email);
            return ResponseEntity.ok(ApiResponse.success("密码重置邮件已发送"));
            
        } catch (Exception e) {
            logger.error("发送密码重置邮件失败: {}", e.getMessage());
            return ResponseEntity.ok(ApiResponse.error("发送邮件失败"));
        }
    }
    
    /**
     * 重置密码
     */
    @PostMapping("/reset-password")
    @Operation(summary = "重置密码", description = "使用重置令牌重置密码")
    public ResponseEntity<ApiResponse<Void>> resetPassword(@RequestParam String token,
                                                          @RequestParam String newPassword) {
        
        try {
            authService.resetPassword(token, newPassword);
            
            logger.info("密码重置成功");
            return ResponseEntity.ok(ApiResponse.success("密码重置成功"));
            
        } catch (Exception e) {
            logger.error("密码重置失败: {}", e.getMessage());
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }
}
