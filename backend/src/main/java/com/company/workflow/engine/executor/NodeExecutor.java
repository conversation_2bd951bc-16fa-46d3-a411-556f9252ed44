package com.company.workflow.engine.executor;

import com.company.workflow.dsl.model.NodeAST;
import com.company.workflow.engine.context.ExecutionContext;
import com.company.workflow.model.entity.NodeExecution;

/**
 * 节点执行器接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public interface NodeExecutor {
    
    /**
     * 获取支持的节点类型
     */
    String getSupportedNodeType();
    
    /**
     * 执行节点
     * 
     * @param context 执行上下文
     * @param nodeAST 节点AST
     * @param nodeExecution 节点执行记录
     * @throws Exception 执行异常
     */
    void execute(ExecutionContext context, NodeAST nodeAST, NodeExecution nodeExecution) throws Exception;
    
    /**
     * 检查节点是否可以执行
     * 
     * @param context 执行上下文
     * @param nodeAST 节点AST
     * @return 是否可以执行
     */
    default boolean canExecute(ExecutionContext context, NodeAST nodeAST) {
        return !context.isCancelled() && !context.isSuspended();
    }
    
    /**
     * 获取执行优先级（数字越小优先级越高）
     */
    default int getPriority() {
        return 100;
    }
    
    /**
     * 是否支持异步执行
     */
    default boolean supportsAsync() {
        return false;
    }
    
    /**
     * 是否支持重试
     */
    default boolean supportsRetry() {
        return true;
    }
    
    /**
     * 是否支持超时
     */
    default boolean supportsTimeout() {
        return true;
    }
    
    /**
     * 获取默认超时时间（毫秒）
     */
    default long getDefaultTimeoutMs() {
        return 30000; // 30秒
    }
    
    /**
     * 获取默认重试次数
     */
    default int getDefaultRetryCount() {
        return 3;
    }
    
    /**
     * 预处理节点执行
     * 在实际执行前调用，可以用于参数验证、资源准备等
     * 
     * @param context 执行上下文
     * @param nodeAST 节点AST
     * @param nodeExecution 节点执行记录
     */
    default void preExecute(ExecutionContext context, NodeAST nodeAST, NodeExecution nodeExecution) {
        // 默认实现为空
    }
    
    /**
     * 后处理节点执行
     * 在实际执行后调用，可以用于结果处理、资源清理等
     * 
     * @param context 执行上下文
     * @param nodeAST 节点AST
     * @param nodeExecution 节点执行记录
     * @param success 执行是否成功
     */
    default void postExecute(ExecutionContext context, NodeAST nodeAST, NodeExecution nodeExecution, boolean success) {
        // 默认实现为空
    }
    
    /**
     * 处理执行异常
     * 
     * @param context 执行上下文
     * @param nodeAST 节点AST
     * @param nodeExecution 节点执行记录
     * @param exception 异常
     * @return 是否已处理异常（true表示异常已处理，不需要向上抛出）
     */
    default boolean handleException(ExecutionContext context, NodeAST nodeAST, NodeExecution nodeExecution, Exception exception) {
        return false; // 默认不处理异常
    }
    
    /**
     * 取消节点执行
     * 
     * @param context 执行上下文
     * @param nodeAST 节点AST
     * @param nodeExecution 节点执行记录
     */
    default void cancel(ExecutionContext context, NodeAST nodeAST, NodeExecution nodeExecution) {
        // 默认实现为空
    }
    
    /**
     * 暂停节点执行
     * 
     * @param context 执行上下文
     * @param nodeAST 节点AST
     * @param nodeExecution 节点执行记录
     */
    default void suspend(ExecutionContext context, NodeAST nodeAST, NodeExecution nodeExecution) {
        // 默认实现为空
    }
    
    /**
     * 恢复节点执行
     * 
     * @param context 执行上下文
     * @param nodeAST 节点AST
     * @param nodeExecution 节点执行记录
     */
    default void resume(ExecutionContext context, NodeAST nodeAST, NodeExecution nodeExecution) {
        // 默认实现为空
    }
}
