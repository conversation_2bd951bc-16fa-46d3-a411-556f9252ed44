package com.company.workflow.engine.executor;

import com.company.workflow.dsl.model.NodeAST;
import com.company.workflow.engine.context.ExecutionContext;
import com.company.workflow.model.entity.NodeExecution;
import com.company.workflow.exception.WorkflowException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 抽象节点执行器基类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public abstract class AbstractNodeExecutor implements NodeExecutor {
    
    protected final Logger logger = LoggerFactory.getLogger(getClass());
    
    @Autowired
    protected ObjectMapper objectMapper;
    
    @Override
    public final void execute(ExecutionContext context, NodeAST nodeAST, NodeExecution nodeExecution) throws Exception {
        logger.debug("开始执行节点: {} ({})", nodeAST.getName(), nodeAST.getId());
        
        try {
            // 1. 检查是否可以执行
            if (!canExecute(context, nodeAST)) {
                logger.warn("节点不能执行: {} ({})", nodeAST.getName(), nodeAST.getId());
                throw new WorkflowException("节点不能执行: " + nodeAST.getName());
            }
            
            // 2. 预处理
            preExecute(context, nodeAST, nodeExecution);
            
            // 3. 设置输入数据
            setInputData(context, nodeAST, nodeExecution);
            
            // 4. 执行具体逻辑
            Map<String, Object> outputData = doExecute(context, nodeAST, nodeExecution);
            
            // 5. 设置输出数据
            setOutputData(context, nodeAST, nodeExecution, outputData);
            
            // 6. 后处理
            postExecute(context, nodeAST, nodeExecution, true);
            
            logger.debug("节点执行成功: {} ({})", nodeAST.getName(), nodeAST.getId());
            
        } catch (Exception e) {
            logger.error("节点执行失败: {} ({})", nodeAST.getName(), nodeAST.getId(), e);
            
            // 处理异常
            boolean handled = handleException(context, nodeAST, nodeExecution, e);
            
            // 后处理
            postExecute(context, nodeAST, nodeExecution, false);
            
            if (!handled) {
                throw e;
            }
        }
    }
    
    /**
     * 执行具体的节点逻辑
     * 子类需要实现此方法
     * 
     * @param context 执行上下文
     * @param nodeAST 节点AST
     * @param nodeExecution 节点执行记录
     * @return 输出数据
     * @throws Exception 执行异常
     */
    protected abstract Map<String, Object> doExecute(ExecutionContext context, NodeAST nodeAST, NodeExecution nodeExecution) throws Exception;
    
    /**
     * 设置输入数据
     */
    protected void setInputData(ExecutionContext context, NodeAST nodeAST, NodeExecution nodeExecution) {
        try {
            Map<String, Object> inputData = new HashMap<>();
            
            // 1. 从上下文变量中获取数据
            inputData.putAll(context.getVariables());
            
            // 2. 应用输入映射
            if (nodeAST.getInputMapping() != null && !nodeAST.getInputMapping().isEmpty()) {
                Map<String, Object> mappedData = applyInputMapping(context, nodeAST.getInputMapping());
                inputData.putAll(mappedData);
            }
            
            // 3. 设置到节点执行记录
            if (!inputData.isEmpty()) {
                nodeExecution.setInputData(objectMapper.writeValueAsString(inputData));
            }
            
        } catch (Exception e) {
            logger.warn("设置输入数据失败: {}", nodeAST.getName(), e);
        }
    }
    
    /**
     * 设置输出数据
     */
    protected void setOutputData(ExecutionContext context, NodeAST nodeAST, NodeExecution nodeExecution, Map<String, Object> outputData) {
        try {
            if (outputData == null) {
                outputData = new HashMap<>();
            }
            
            // 1. 设置到节点执行记录
            if (!outputData.isEmpty()) {
                nodeExecution.setOutputData(objectMapper.writeValueAsString(outputData));
            }
            
            // 2. 应用输出映射
            if (nodeAST.getOutputMapping() != null && !nodeAST.getOutputMapping().isEmpty()) {
                Map<String, Object> mappedData = applyOutputMapping(context, nodeAST.getOutputMapping(), outputData);
                outputData.putAll(mappedData);
            }
            
            // 3. 更新上下文变量
            for (Map.Entry<String, Object> entry : outputData.entrySet()) {
                context.setVariable(entry.getKey(), entry.getValue());
            }
            
            // 4. 添加到输出数据
            context.addOutputData(outputData);
            
        } catch (Exception e) {
            logger.warn("设置输出数据失败: {}", nodeAST.getName(), e);
        }
    }
    
    /**
     * 应用输入映射
     */
    protected Map<String, Object> applyInputMapping(ExecutionContext context, Map<String, Object> inputMapping) {
        Map<String, Object> mappedData = new HashMap<>();
        
        for (Map.Entry<String, Object> entry : inputMapping.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            // 如果值是字符串且以$开头，则从上下文变量中获取
            if (value instanceof String && ((String) value).startsWith("$")) {
                String varName = ((String) value).substring(1);
                Object varValue = context.getVariable(varName);
                mappedData.put(key, varValue);
            } else {
                mappedData.put(key, value);
            }
        }
        
        return mappedData;
    }
    
    /**
     * 应用输出映射
     */
    protected Map<String, Object> applyOutputMapping(ExecutionContext context, Map<String, Object> outputMapping, Map<String, Object> outputData) {
        Map<String, Object> mappedData = new HashMap<>();
        
        for (Map.Entry<String, Object> entry : outputMapping.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            // 如果值是字符串且以$开头，则从输出数据中获取
            if (value instanceof String && ((String) value).startsWith("$")) {
                String varName = ((String) value).substring(1);
                Object varValue = outputData.get(varName);
                mappedData.put(key, varValue);
            } else {
                mappedData.put(key, value);
            }
        }
        
        return mappedData;
    }
    
    /**
     * 获取节点属性值
     */
    protected Object getNodeProperty(NodeAST nodeAST, String propertyName, Object defaultValue) {
        Object value = nodeAST.getProperty(propertyName);
        return value != null ? value : defaultValue;
    }
    
    /**
     * 获取字符串类型的节点属性
     */
    protected String getStringProperty(NodeAST nodeAST, String propertyName, String defaultValue) {
        Object value = getNodeProperty(nodeAST, propertyName, defaultValue);
        return value != null ? value.toString() : defaultValue;
    }
    
    /**
     * 获取整数类型的节点属性
     */
    protected Integer getIntegerProperty(NodeAST nodeAST, String propertyName, Integer defaultValue) {
        Object value = getNodeProperty(nodeAST, propertyName, defaultValue);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        } else if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                logger.warn("无法解析整数属性 {}: {}", propertyName, value);
                return defaultValue;
            }
        }
        return defaultValue;
    }
    
    /**
     * 获取长整数类型的节点属性
     */
    protected Long getLongProperty(NodeAST nodeAST, String propertyName, Long defaultValue) {
        Object value = getNodeProperty(nodeAST, propertyName, defaultValue);
        if (value instanceof Number) {
            return ((Number) value).longValue();
        } else if (value instanceof String) {
            try {
                return Long.parseLong((String) value);
            } catch (NumberFormatException e) {
                logger.warn("无法解析长整数属性 {}: {}", propertyName, value);
                return defaultValue;
            }
        }
        return defaultValue;
    }
    
    /**
     * 获取布尔类型的节点属性
     */
    protected Boolean getBooleanProperty(NodeAST nodeAST, String propertyName, Boolean defaultValue) {
        Object value = getNodeProperty(nodeAST, propertyName, defaultValue);
        if (value instanceof Boolean) {
            return (Boolean) value;
        } else if (value instanceof String) {
            return Boolean.parseBoolean((String) value);
        }
        return defaultValue;
    }
    
    /**
     * 解析表达式
     * 简化实现，实际应该使用表达式引擎
     */
    protected Object evaluateExpression(ExecutionContext context, String expression) {
        if (expression == null || expression.trim().isEmpty()) {
            return null;
        }
        
        // 简单的变量替换
        if (expression.startsWith("$")) {
            String varName = expression.substring(1);
            return context.getVariable(varName);
        }
        
        // 其他表达式处理...
        return expression;
    }
    
    /**
     * 等待指定时间
     */
    protected void waitFor(long timeMs) throws InterruptedException {
        if (timeMs > 0) {
            TimeUnit.MILLISECONDS.sleep(timeMs);
        }
    }
    
    /**
     * 检查是否超时
     */
    protected boolean isTimeout(NodeExecution nodeExecution) {
        return nodeExecution.isTimeout();
    }
    
    /**
     * 创建执行异常
     */
    protected WorkflowException createExecutionException(String message, Throwable cause) {
        return new WorkflowException("节点执行失败: " + message, cause);
    }
    
    /**
     * 创建执行异常
     */
    protected WorkflowException createExecutionException(String message) {
        return new WorkflowException("节点执行失败: " + message);
    }
}
