package com.company.workflow.engine.executor.impl;

import com.company.workflow.dsl.model.NodeAST;
import com.company.workflow.engine.context.ExecutionContext;
import com.company.workflow.engine.executor.AbstractNodeExecutor;
import com.company.workflow.model.entity.NodeExecution;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 结束节点执行器
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Component
public class EndNodeExecutor extends AbstractNodeExecutor {
    
    @Override
    public String getSupportedNodeType() {
        return "end";
    }
    
    @Override
    public int getPriority() {
        return 1; // 最高优先级
    }
    
    @Override
    public boolean supportsRetry() {
        return false; // 结束节点不支持重试
    }
    
    @Override
    public boolean supportsTimeout() {
        return false; // 结束节点不支持超时
    }
    
    @Override
    protected Map<String, Object> doExecute(ExecutionContext context, NodeAST nodeAST, NodeExecution nodeExecution) throws Exception {
        logger.info("执行结束节点: {} ({})", nodeAST.getName(), nodeAST.getId());
        
        Map<String, Object> outputData = new HashMap<>();
        
        // 1. 记录工作流结束时间
        LocalDateTime endTime = LocalDateTime.now();
        outputData.put("workflowEndTime", endTime);
        outputData.put("workflowInstanceId", context.getInstance().getId());
        
        // 2. 计算执行时长
        long executionDuration = context.getExecutionDuration();
        outputData.put("executionDuration", executionDuration);
        outputData.put("executionDurationFormatted", formatDuration(executionDuration));
        
        // 3. 统计执行信息
        outputData.put("totalNodes", context.getTotalNodes());
        outputData.put("completedNodes", context.getCompletedNodes());
        outputData.put("failedNodes", context.getFailedNodes());
        outputData.put("progressPercentage", context.getProgressPercentage());
        
        // 4. 收集最终输出数据
        Map<String, Object> finalOutputData = new HashMap<>(context.getOutputData());
        outputData.put("finalOutputData", finalOutputData);
        
        // 5. 收集最终变量状态
        Map<String, Object> finalVariables = new HashMap<>(context.getVariables());
        outputData.put("finalVariables", finalVariables);
        
        // 6. 设置结束状态
        outputData.put("workflowStatus", "COMPLETED");
        outputData.put("endNodeId", nodeAST.getId());
        outputData.put("endNodeName", nodeAST.getName());
        
        // 7. 生成执行摘要
        Map<String, Object> executionSummary = generateExecutionSummary(context, nodeAST, endTime);
        outputData.put("executionSummary", executionSummary);
        
        // 8. 触发结束事件
        triggerEndEvent(context, nodeAST, outputData);
        
        logger.info("结束节点执行完成: {}，工作流总耗时: {}ms", nodeAST.getName(), executionDuration);
        return outputData;
    }
    
    @Override
    public void preExecute(ExecutionContext context, NodeAST nodeAST, NodeExecution nodeExecution) {
        super.preExecute(context, nodeAST, nodeExecution);
        
        // 标记工作流即将结束
        context.setEndTime(LocalDateTime.now());
        
        logger.debug("结束节点预处理完成: {}", nodeAST.getName());
    }
    
    @Override
    public void postExecute(ExecutionContext context, NodeAST nodeAST, NodeExecution nodeExecution, boolean success) {
        super.postExecute(context, nodeAST, nodeExecution, success);
        
        if (success) {
            // 更新实例状态信息
            context.setCurrentNodeId(nodeAST.getId());
            context.setCurrentNodeName(nodeAST.getName());
            
            // 增加完成节点计数
            context.incrementCompletedNodes();
            
            // 记录结束节点执行
            context.addExecutionHistory(nodeAST.getId(), nodeExecution);
            
            logger.info("工作流实例 {} 已完成，结束节点: {}", 
                       context.getInstance().getId(), nodeAST.getName());
        }
    }
    
    /**
     * 生成执行摘要
     */
    private Map<String, Object> generateExecutionSummary(ExecutionContext context, NodeAST nodeAST, LocalDateTime endTime) {
        Map<String, Object> summary = new HashMap<>();
        
        // 基本信息
        summary.put("instanceId", context.getInstance().getId());
        summary.put("workflowName", context.getWorkflowAST().getName());
        summary.put("workflowVersion", context.getWorkflowAST().getVersion());
        summary.put("startTime", context.getStartTime());
        summary.put("endTime", endTime);
        summary.put("duration", context.getExecutionDuration());
        
        // 执行统计
        summary.put("totalNodes", context.getTotalNodes());
        summary.put("completedNodes", context.getCompletedNodes());
        summary.put("failedNodes", context.getFailedNodes());
        summary.put("successRate", calculateSuccessRate(context));
        
        // 性能指标
        summary.put("avgNodeExecutionTime", calculateAvgNodeExecutionTime(context));
        summary.put("executionEfficiency", calculateExecutionEfficiency(context));
        
        // 资源使用
        summary.put("memoryUsage", getMemoryUsage());
        summary.put("threadCount", Thread.activeCount());
        
        return summary;
    }
    
    /**
     * 计算成功率
     */
    private double calculateSuccessRate(ExecutionContext context) {
        int totalNodes = context.getTotalNodes();
        if (totalNodes == 0) {
            return 100.0;
        }
        return (double) context.getCompletedNodes() / totalNodes * 100.0;
    }
    
    /**
     * 计算平均节点执行时间
     */
    private double calculateAvgNodeExecutionTime(ExecutionContext context) {
        int completedNodes = context.getCompletedNodes();
        if (completedNodes == 0) {
            return 0.0;
        }
        return (double) context.getExecutionDuration() / completedNodes;
    }
    
    /**
     * 计算执行效率
     */
    private double calculateExecutionEfficiency(ExecutionContext context) {
        // 简化的效率计算：完成节点数 / 总执行时间（秒）
        long durationSeconds = context.getExecutionDuration() / 1000;
        if (durationSeconds == 0) {
            return 0.0;
        }
        return (double) context.getCompletedNodes() / durationSeconds;
    }
    
    /**
     * 获取内存使用情况
     */
    private Map<String, Object> getMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        Map<String, Object> memoryInfo = new HashMap<>();
        
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();
        
        memoryInfo.put("totalMemory", totalMemory);
        memoryInfo.put("freeMemory", freeMemory);
        memoryInfo.put("usedMemory", usedMemory);
        memoryInfo.put("maxMemory", maxMemory);
        memoryInfo.put("memoryUsagePercentage", (double) usedMemory / maxMemory * 100.0);
        
        return memoryInfo;
    }
    
    /**
     * 格式化持续时间
     */
    private String formatDuration(long durationMs) {
        if (durationMs < 1000) {
            return durationMs + "ms";
        } else if (durationMs < 60000) {
            return String.format("%.2fs", durationMs / 1000.0);
        } else if (durationMs < 3600000) {
            long minutes = durationMs / 60000;
            long seconds = (durationMs % 60000) / 1000;
            return String.format("%dm %ds", minutes, seconds);
        } else {
            long hours = durationMs / 3600000;
            long minutes = (durationMs % 3600000) / 60000;
            long seconds = (durationMs % 60000) / 1000;
            return String.format("%dh %dm %ds", hours, minutes, seconds);
        }
    }
    
    /**
     * 触发结束事件
     */
    private void triggerEndEvent(ExecutionContext context, NodeAST nodeAST, Map<String, Object> outputData) {
        try {
            // 这里可以触发工作流结束事件
            // 例如发送通知、记录审计日志、清理资源等
            
            logger.debug("触发工作流结束事件: {}", context.getInstance().getId());
            
            // 可以在这里添加事件监听器的调用
            // eventPublisher.publishEvent(new WorkflowCompletedEvent(context, nodeAST, outputData));
            
        } catch (Exception e) {
            logger.warn("触发结束事件失败", e);
            // 不抛出异常，避免影响主流程
        }
    }
    
    @Override
    public boolean canExecute(ExecutionContext context, NodeAST nodeAST) {
        // 结束节点总是可以执行
        return true;
    }
    
    @Override
    public boolean handleException(ExecutionContext context, NodeAST nodeAST, NodeExecution nodeExecution, Exception exception) {
        // 结束节点执行失败的处理
        logger.error("结束节点执行失败: {}", nodeAST.getName(), exception);
        
        // 记录失败原因
        Map<String, Object> errorData = new HashMap<>();
        errorData.put("errorType", "END_NODE_FAILURE");
        errorData.put("errorMessage", exception.getMessage());
        errorData.put("errorTime", LocalDateTime.now());
        
        try {
            context.addOutputData(errorData);
        } catch (Exception e) {
            logger.warn("记录结束节点错误信息失败", e);
        }
        
        return false; // 不处理异常，让其向上抛出
    }
}
