package com.company.workflow.engine.executor.impl;

import com.company.workflow.dsl.model.NodeAST;
import com.company.workflow.engine.context.ExecutionContext;
import com.company.workflow.engine.executor.AbstractNodeExecutor;
import com.company.workflow.model.entity.NodeExecution;
import com.company.workflow.service.TaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 任务节点执行器
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Component
public class TaskNodeExecutor extends AbstractNodeExecutor {
    
    @Autowired
    private ApplicationContext applicationContext;
    
    @Override
    public String getSupportedNodeType() {
        return "task";
    }
    
    @Override
    public int getPriority() {
        return 50;
    }
    
    @Override
    public boolean supportsAsync() {
        return true;
    }
    
    @Override
    public boolean supportsRetry() {
        return true;
    }
    
    @Override
    public boolean supportsTimeout() {
        return true;
    }
    
    @Override
    public long getDefaultTimeoutMs() {
        return 300000; // 5分钟
    }
    
    @Override
    public int getDefaultRetryCount() {
        return 2;
    }
    
    @Override
    protected Map<String, Object> doExecute(ExecutionContext context, NodeAST nodeAST, NodeExecution nodeExecution) throws Exception {
        logger.info("执行任务节点: {} ({})", nodeAST.getName(), nodeAST.getId());
        
        // 1. 获取服务名称和方法名称
        String serviceName = nodeAST.getServiceName();
        String methodName = nodeAST.getMethodName();
        
        if (serviceName == null || serviceName.trim().isEmpty()) {
            throw createExecutionException("任务节点未指定服务名称");
        }
        
        if (methodName == null || methodName.trim().isEmpty()) {
            throw createExecutionException("任务节点未指定方法名称");
        }
        
        // 2. 准备服务参数
        Map<String, Object> serviceParameters = prepareServiceParameters(context, nodeAST);
        
        // 3. 执行服务调用
        Map<String, Object> result = executeService(serviceName, methodName, serviceParameters, context, nodeAST);
        
        // 4. 处理执行结果
        Map<String, Object> outputData = processExecutionResult(result, context, nodeAST);
        
        logger.info("任务节点执行完成: {}", nodeAST.getName());
        return outputData;
    }
    
    /**
     * 准备服务参数
     */
    private Map<String, Object> prepareServiceParameters(ExecutionContext context, NodeAST nodeAST) {
        Map<String, Object> parameters = new HashMap<>();
        
        // 1. 添加节点配置的参数
        if (nodeAST.getServiceParameters() != null) {
            parameters.putAll(nodeAST.getServiceParameters());
        }
        
        // 2. 添加上下文变量
        parameters.putAll(context.getVariables());
        
        // 3. 添加系统参数
        parameters.put("instanceId", context.getInstance().getId());
        parameters.put("nodeId", nodeAST.getId());
        parameters.put("nodeName", nodeAST.getName());
        parameters.put("executionId", context.getCurrentExecution().getId());
        
        // 4. 处理参数表达式
        Map<String, Object> processedParameters = new HashMap<>();
        for (Map.Entry<String, Object> entry : parameters.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            if (value instanceof String) {
                Object evaluatedValue = evaluateExpression(context, (String) value);
                processedParameters.put(key, evaluatedValue);
            } else {
                processedParameters.put(key, value);
            }
        }
        
        logger.debug("任务节点参数: {}", processedParameters.keySet());
        return processedParameters;
    }
    
    /**
     * 执行服务调用
     */
    private Map<String, Object> executeService(String serviceName, String methodName, 
                                             Map<String, Object> parameters, 
                                             ExecutionContext context, NodeAST nodeAST) throws Exception {
        
        logger.debug("调用服务: {}.{}", serviceName, methodName);
        
        try {
            // 1. 获取服务Bean
            Object serviceBean = getServiceBean(serviceName);
            
            // 2. 获取方法
            Method method = getServiceMethod(serviceBean, methodName, parameters);
            
            // 3. 准备方法参数
            Object[] methodArgs = prepareMethodArguments(method, parameters, context);
            
            // 4. 执行方法调用
            Object result = method.invoke(serviceBean, methodArgs);
            
            // 5. 处理返回结果
            return processServiceResult(result);
            
        } catch (Exception e) {
            logger.error("服务调用失败: {}.{}", serviceName, methodName, e);
            throw createExecutionException("服务调用失败: " + serviceName + "." + methodName, e);
        }
    }
    
    /**
     * 获取服务Bean
     */
    private Object getServiceBean(String serviceName) throws Exception {
        try {
            // 首先尝试按名称获取Bean
            return applicationContext.getBean(serviceName);
        } catch (Exception e) {
            try {
                // 尝试按类型获取Bean
                Class<?> serviceClass = Class.forName(serviceName);
                return applicationContext.getBean(serviceClass);
            } catch (Exception ex) {
                throw new Exception("无法找到服务: " + serviceName, ex);
            }
        }
    }
    
    /**
     * 获取服务方法
     */
    private Method getServiceMethod(Object serviceBean, String methodName, Map<String, Object> parameters) throws Exception {
        Class<?> serviceClass = serviceBean.getClass();
        Method[] methods = serviceClass.getMethods();
        
        // 查找匹配的方法
        for (Method method : methods) {
            if (method.getName().equals(methodName)) {
                // 简化的方法匹配，实际应该考虑参数类型
                return method;
            }
        }
        
        throw new Exception("无法找到方法: " + serviceClass.getName() + "." + methodName);
    }
    
    /**
     * 准备方法参数
     */
    private Object[] prepareMethodArguments(Method method, Map<String, Object> parameters, ExecutionContext context) {
        Class<?>[] parameterTypes = method.getParameterTypes();
        Object[] args = new Object[parameterTypes.length];
        
        // 简化的参数映射，实际应该根据参数名称和类型进行匹配
        for (int i = 0; i < parameterTypes.length; i++) {
            Class<?> paramType = parameterTypes[i];
            
            if (paramType == ExecutionContext.class) {
                args[i] = context;
            } else if (paramType == Map.class) {
                args[i] = parameters;
            } else if (paramType == String.class && parameters.containsKey("input")) {
                args[i] = parameters.get("input");
            } else {
                // 其他类型的参数处理
                args[i] = null;
            }
        }
        
        return args;
    }
    
    /**
     * 处理服务返回结果
     */
    private Map<String, Object> processServiceResult(Object result) {
        Map<String, Object> resultMap = new HashMap<>();
        
        if (result == null) {
            resultMap.put("result", null);
            resultMap.put("success", true);
        } else if (result instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> mapResult = (Map<String, Object>) result;
            resultMap.putAll(mapResult);
        } else {
            resultMap.put("result", result);
            resultMap.put("success", true);
        }
        
        return resultMap;
    }
    
    /**
     * 处理执行结果
     */
    private Map<String, Object> processExecutionResult(Map<String, Object> result, ExecutionContext context, NodeAST nodeAST) {
        Map<String, Object> outputData = new HashMap<>();
        
        // 1. 添加服务执行结果
        outputData.putAll(result);
        
        // 2. 添加执行信息
        outputData.put("taskExecutionTime", LocalDateTime.now());
        outputData.put("taskNodeId", nodeAST.getId());
        outputData.put("taskNodeName", nodeAST.getName());
        outputData.put("serviceName", nodeAST.getServiceName());
        outputData.put("methodName", nodeAST.getMethodName());
        
        // 3. 检查执行是否成功
        boolean success = (Boolean) result.getOrDefault("success", true);
        outputData.put("taskSuccess", success);
        
        if (!success) {
            String errorMessage = (String) result.get("errorMessage");
            if (errorMessage != null) {
                outputData.put("taskErrorMessage", errorMessage);
            }
        }
        
        return outputData;
    }
    
    @Override
    public void preExecute(ExecutionContext context, NodeAST nodeAST, NodeExecution nodeExecution) {
        super.preExecute(context, nodeAST, nodeExecution);
        
        // 验证任务节点配置
        validateTaskConfiguration(nodeAST);
        
        // 设置执行器信息
        nodeExecution.setExecutorId("task-executor");
        nodeExecution.setExecutorName("TaskNodeExecutor");
        
        logger.debug("任务节点预处理完成: {}", nodeAST.getName());
    }
    
    /**
     * 验证任务节点配置
     */
    private void validateTaskConfiguration(NodeAST nodeAST) {
        if (nodeAST.getServiceName() == null || nodeAST.getServiceName().trim().isEmpty()) {
            throw createExecutionException("任务节点必须指定服务名称");
        }
        
        if (nodeAST.getMethodName() == null || nodeAST.getMethodName().trim().isEmpty()) {
            throw createExecutionException("任务节点必须指定方法名称");
        }
    }
    
    @Override
    public boolean handleException(ExecutionContext context, NodeAST nodeAST, NodeExecution nodeExecution, Exception exception) {
        logger.error("任务节点执行异常: {}", nodeAST.getName(), exception);
        
        // 记录异常信息
        Map<String, Object> errorData = new HashMap<>();
        errorData.put("errorType", "TASK_EXECUTION_ERROR");
        errorData.put("errorMessage", exception.getMessage());
        errorData.put("errorTime", LocalDateTime.now());
        errorData.put("serviceName", nodeAST.getServiceName());
        errorData.put("methodName", nodeAST.getMethodName());
        
        try {
            context.addOutputData(errorData);
        } catch (Exception e) {
            logger.warn("记录任务节点错误信息失败", e);
        }
        
        // 检查是否可以重试
        if (nodeExecution.canRetry()) {
            logger.info("任务节点将进行重试: {} (重试次数: {}/{})", 
                       nodeAST.getName(), 
                       nodeExecution.getRetryCount() + 1, 
                       nodeExecution.getMaxRetryCount());
            return false; // 不处理异常，让重试机制处理
        }
        
        return false; // 不处理异常，让其向上抛出
    }
}
