package com.company.workflow.engine.state;

import com.company.workflow.model.entity.*;
import com.company.workflow.repository.WorkflowInstanceRepository;
import com.company.workflow.repository.NodeExecutionRepository;
import com.company.workflow.exception.WorkflowException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 工作流状态管理器
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
public class StateManager {
    
    private static final Logger logger = LoggerFactory.getLogger(StateManager.class);
    
    @Autowired
    private WorkflowInstanceRepository instanceRepository;
    
    @Autowired
    private NodeExecutionRepository nodeExecutionRepository;
    
    // ==================== 实例状态管理 ====================
    
    /**
     * 启动实例
     */
    @Transactional
    public void startInstance(String instanceId) {
        try {
            logger.debug("启动实例: {}", instanceId);
            
            WorkflowInstance instance = getInstanceById(instanceId);
            
            if (instance.getStatus() != InstanceStatus.PENDING) {
                throw new WorkflowException("实例状态不是等待状态，无法启动: " + instanceId);
            }
            
            instance.start();
            instanceRepository.save(instance);
            
            logger.info("实例启动成功: {}", instanceId);
            
        } catch (Exception e) {
            logger.error("启动实例失败: {}", instanceId, e);
            throw new WorkflowException("启动实例失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 完成实例
     */
    @Transactional
    public void completeInstance(String instanceId) {
        try {
            logger.debug("完成实例: {}", instanceId);
            
            WorkflowInstance instance = getInstanceById(instanceId);
            
            if (!instance.isRunning()) {
                logger.warn("实例状态不是运行中，但仍然标记为完成: {}", instanceId);
            }
            
            instance.complete();
            instanceRepository.save(instance);
            
            logger.info("实例完成: {}", instanceId);
            
        } catch (Exception e) {
            logger.error("完成实例失败: {}", instanceId, e);
            throw new WorkflowException("完成实例失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 失败实例
     */
    @Transactional
    public void failInstance(String instanceId, String errorMessage) {
        try {
            logger.debug("失败实例: {}, 错误: {}", instanceId, errorMessage);
            
            WorkflowInstance instance = getInstanceById(instanceId);
            instance.fail(errorMessage);
            instanceRepository.save(instance);
            
            logger.info("实例失败: {}", instanceId);
            
        } catch (Exception e) {
            logger.error("失败实例操作失败: {}", instanceId, e);
            throw new WorkflowException("失败实例操作失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 取消实例
     */
    @Transactional
    public void cancelInstance(String instanceId) {
        try {
            logger.debug("取消实例: {}", instanceId);
            
            WorkflowInstance instance = getInstanceById(instanceId);
            
            if (!instance.getStatus().canCancel()) {
                throw new WorkflowException("实例当前状态不允许取消: " + instanceId);
            }
            
            instance.cancel();
            instanceRepository.save(instance);
            
            logger.info("实例取消: {}", instanceId);
            
        } catch (Exception e) {
            logger.error("取消实例失败: {}", instanceId, e);
            throw new WorkflowException("取消实例失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 暂停实例
     */
    @Transactional
    public void suspendInstance(String instanceId) {
        try {
            logger.debug("暂停实例: {}", instanceId);
            
            WorkflowInstance instance = getInstanceById(instanceId);
            
            if (!instance.getStatus().canSuspend()) {
                throw new WorkflowException("实例当前状态不允许暂停: " + instanceId);
            }
            
            instance.suspend();
            instanceRepository.save(instance);
            
            logger.info("实例暂停: {}", instanceId);
            
        } catch (Exception e) {
            logger.error("暂停实例失败: {}", instanceId, e);
            throw new WorkflowException("暂停实例失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 恢复实例
     */
    @Transactional
    public void resumeInstance(String instanceId) {
        try {
            logger.debug("恢复实例: {}", instanceId);
            
            WorkflowInstance instance = getInstanceById(instanceId);
            
            if (!instance.getStatus().canResume()) {
                throw new WorkflowException("实例当前状态不允许恢复: " + instanceId);
            }
            
            instance.resume();
            instanceRepository.save(instance);
            
            logger.info("实例恢复: {}", instanceId);
            
        } catch (Exception e) {
            logger.error("恢复实例失败: {}", instanceId, e);
            throw new WorkflowException("恢复实例失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 更新当前节点
     */
    @Transactional
    public void updateCurrentNode(String instanceId, String nodeId, String nodeName) {
        try {
            logger.debug("更新当前节点: {} -> {} ({})", instanceId, nodeId, nodeName);
            
            WorkflowInstance instance = getInstanceById(instanceId);
            instance.setCurrentNodeId(nodeId);
            instance.setCurrentNodeName(nodeName);
            instanceRepository.save(instance);
            
        } catch (Exception e) {
            logger.error("更新当前节点失败: {}", instanceId, e);
            throw new WorkflowException("更新当前节点失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 增加重试次数
     */
    @Transactional
    public void incrementRetryCount(String instanceId) {
        try {
            logger.debug("增加重试次数: {}", instanceId);
            
            WorkflowInstance instance = getInstanceById(instanceId);
            instance.incrementRetryCount();
            instanceRepository.save(instance);
            
        } catch (Exception e) {
            logger.error("增加重试次数失败: {}", instanceId, e);
            throw new WorkflowException("增加重试次数失败: " + e.getMessage(), e);
        }
    }
    
    // ==================== 节点执行状态管理 ====================
    
    /**
     * 开始节点执行
     */
    @Transactional
    public void startNodeExecution(String executionId) {
        try {
            logger.debug("开始节点执行: {}", executionId);
            
            NodeExecution execution = getNodeExecutionById(executionId);
            
            if (execution.getStatus() != NodeStatus.PENDING) {
                throw new WorkflowException("节点执行状态不是等待状态，无法开始: " + executionId);
            }
            
            execution.start();
            nodeExecutionRepository.save(execution);
            
        } catch (Exception e) {
            logger.error("开始节点执行失败: {}", executionId, e);
            throw new WorkflowException("开始节点执行失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 完成节点执行
     */
    @Transactional
    public void completeNodeExecution(String executionId) {
        try {
            logger.debug("完成节点执行: {}", executionId);
            
            NodeExecution execution = getNodeExecutionById(executionId);
            execution.complete();
            nodeExecutionRepository.save(execution);
            
        } catch (Exception e) {
            logger.error("完成节点执行失败: {}", executionId, e);
            throw new WorkflowException("完成节点执行失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 失败节点执行
     */
    @Transactional
    public void failNodeExecution(String executionId, String errorMessage) {
        try {
            logger.debug("失败节点执行: {}, 错误: {}", executionId, errorMessage);
            
            NodeExecution execution = getNodeExecutionById(executionId);
            execution.fail(errorMessage);
            nodeExecutionRepository.save(execution);
            
        } catch (Exception e) {
            logger.error("失败节点执行操作失败: {}", executionId, e);
            throw new WorkflowException("失败节点执行操作失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 失败节点执行（带堆栈信息）
     */
    @Transactional
    public void failNodeExecution(String executionId, String errorMessage, String stackTrace) {
        try {
            logger.debug("失败节点执行: {}, 错误: {}", executionId, errorMessage);
            
            NodeExecution execution = getNodeExecutionById(executionId);
            execution.fail(errorMessage, stackTrace);
            nodeExecutionRepository.save(execution);
            
        } catch (Exception e) {
            logger.error("失败节点执行操作失败: {}", executionId, e);
            throw new WorkflowException("失败节点执行操作失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 跳过节点执行
     */
    @Transactional
    public void skipNodeExecution(String executionId) {
        try {
            logger.debug("跳过节点执行: {}", executionId);
            
            NodeExecution execution = getNodeExecutionById(executionId);
            execution.skip();
            nodeExecutionRepository.save(execution);
            
        } catch (Exception e) {
            logger.error("跳过节点执行失败: {}", executionId, e);
            throw new WorkflowException("跳过节点执行失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 取消节点执行
     */
    @Transactional
    public void cancelNodeExecution(String executionId) {
        try {
            logger.debug("取消节点执行: {}", executionId);
            
            NodeExecution execution = getNodeExecutionById(executionId);
            execution.cancel();
            nodeExecutionRepository.save(execution);
            
        } catch (Exception e) {
            logger.error("取消节点执行失败: {}", executionId, e);
            throw new WorkflowException("取消节点执行失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 设置节点执行为等待状态
     */
    @Transactional
    public void setNodeExecutionWaiting(String executionId) {
        try {
            logger.debug("设置节点执行为等待状态: {}", executionId);
            
            NodeExecution execution = getNodeExecutionById(executionId);
            execution.setStatus(NodeStatus.WAITING);
            nodeExecutionRepository.save(execution);
            
        } catch (Exception e) {
            logger.error("设置节点执行等待状态失败: {}", executionId, e);
            throw new WorkflowException("设置节点执行等待状态失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 设置节点执行超时
     */
    @Transactional
    public void timeoutNodeExecution(String executionId) {
        try {
            logger.debug("设置节点执行超时: {}", executionId);
            
            NodeExecution execution = getNodeExecutionById(executionId);
            execution.setStatus(NodeStatus.TIMEOUT);
            execution.setEndTime(LocalDateTime.now());
            if (execution.getStartTime() != null) {
                execution.setDurationMs(java.time.Duration.between(execution.getStartTime(), execution.getEndTime()).toMillis());
            }
            nodeExecutionRepository.save(execution);
            
        } catch (Exception e) {
            logger.error("设置节点执行超时失败: {}", executionId, e);
            throw new WorkflowException("设置节点执行超时失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 增加节点重试次数
     */
    @Transactional
    public void incrementNodeRetryCount(String executionId) {
        try {
            logger.debug("增加节点重试次数: {}", executionId);
            
            NodeExecution execution = getNodeExecutionById(executionId);
            execution.incrementRetryCount();
            nodeExecutionRepository.save(execution);
            
        } catch (Exception e) {
            logger.error("增加节点重试次数失败: {}", executionId, e);
            throw new WorkflowException("增加节点重试次数失败: " + e.getMessage(), e);
        }
    }
    
    // ==================== 状态查询方法 ====================
    
    /**
     * 检查实例是否可以执行
     */
    public boolean canExecuteInstance(String instanceId) {
        try {
            WorkflowInstance instance = getInstanceById(instanceId);
            return instance.getStatus().canSuspend() || instance.getStatus().canResume();
        } catch (Exception e) {
            logger.error("检查实例执行状态失败: {}", instanceId, e);
            return false;
        }
    }
    
    /**
     * 检查节点是否可以执行
     */
    public boolean canExecuteNode(String executionId) {
        try {
            NodeExecution execution = getNodeExecutionById(executionId);
            return execution.getStatus().canCancel();
        } catch (Exception e) {
            logger.error("检查节点执行状态失败: {}", executionId, e);
            return false;
        }
    }
    
    /**
     * 获取实例状态
     */
    public InstanceStatus getInstanceStatus(String instanceId) {
        try {
            WorkflowInstance instance = getInstanceById(instanceId);
            return instance.getStatus();
        } catch (Exception e) {
            logger.error("获取实例状态失败: {}", instanceId, e);
            return null;
        }
    }
    
    /**
     * 获取节点执行状态
     */
    public NodeStatus getNodeExecutionStatus(String executionId) {
        try {
            NodeExecution execution = getNodeExecutionById(executionId);
            return execution.getStatus();
        } catch (Exception e) {
            logger.error("获取节点执行状态失败: {}", executionId, e);
            return null;
        }
    }
    
    // ==================== 辅助方法 ====================
    
    /**
     * 根据ID获取实例
     */
    private WorkflowInstance getInstanceById(String instanceId) {
        Optional<WorkflowInstance> optional = instanceRepository.findById(instanceId);
        if (optional.isEmpty()) {
            throw new WorkflowException("工作流实例不存在: " + instanceId);
        }
        return optional.get();
    }
    
    /**
     * 根据ID获取节点执行
     */
    private NodeExecution getNodeExecutionById(String executionId) {
        Optional<NodeExecution> optional = nodeExecutionRepository.findById(executionId);
        if (optional.isEmpty()) {
            throw new WorkflowException("节点执行记录不存在: " + executionId);
        }
        return optional.get();
    }
    
    /**
     * 批量更新实例状态
     */
    @Transactional
    public void batchUpdateInstanceStatus(java.util.List<String> instanceIds, InstanceStatus status) {
        try {
            logger.debug("批量更新实例状态: {} -> {}", instanceIds.size(), status);
            
            for (String instanceId : instanceIds) {
                WorkflowInstance instance = getInstanceById(instanceId);
                instance.setStatus(status);
                instanceRepository.save(instance);
            }
            
            logger.info("批量更新实例状态完成: {} 个实例", instanceIds.size());
            
        } catch (Exception e) {
            logger.error("批量更新实例状态失败", e);
            throw new WorkflowException("批量更新实例状态失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 批量更新节点执行状态
     */
    @Transactional
    public void batchUpdateNodeExecutionStatus(java.util.List<String> executionIds, NodeStatus status) {
        try {
            logger.debug("批量更新节点执行状态: {} -> {}", executionIds.size(), status);
            
            for (String executionId : executionIds) {
                NodeExecution execution = getNodeExecutionById(executionId);
                execution.setStatus(status);
                nodeExecutionRepository.save(execution);
            }
            
            logger.info("批量更新节点执行状态完成: {} 个执行", executionIds.size());
            
        } catch (Exception e) {
            logger.error("批量更新节点执行状态失败", e);
            throw new WorkflowException("批量更新节点执行状态失败: " + e.getMessage(), e);
        }
    }
}
