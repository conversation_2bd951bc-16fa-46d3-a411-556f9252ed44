package com.company.workflow.engine.executor;

import com.company.workflow.exception.WorkflowException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 节点执行器工厂
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Component
public class NodeExecutorFactory {
    
    private static final Logger logger = LoggerFactory.getLogger(NodeExecutorFactory.class);
    
    @Autowired
    private List<NodeExecutor> nodeExecutors;
    
    private final Map<String, NodeExecutor> executorMap = new ConcurrentHashMap<>();
    
    /**
     * 初始化执行器映射
     */
    @PostConstruct
    public void init() {
        logger.info("初始化节点执行器工厂");
        
        for (NodeExecutor executor : nodeExecutors) {
            String nodeType = executor.getSupportedNodeType();
            if (nodeType == null || nodeType.trim().isEmpty()) {
                logger.warn("节点执行器 {} 未指定支持的节点类型", executor.getClass().getSimpleName());
                continue;
            }
            
            if (executorMap.containsKey(nodeType)) {
                logger.warn("节点类型 {} 已有执行器 {}，将被 {} 覆盖", 
                           nodeType, 
                           executorMap.get(nodeType).getClass().getSimpleName(),
                           executor.getClass().getSimpleName());
            }
            
            executorMap.put(nodeType.toLowerCase(), executor);
            logger.info("注册节点执行器: {} -> {}", nodeType, executor.getClass().getSimpleName());
        }
        
        logger.info("节点执行器工厂初始化完成，共注册 {} 个执行器", executorMap.size());
    }
    
    /**
     * 获取节点执行器
     * 
     * @param nodeType 节点类型
     * @return 节点执行器
     */
    public NodeExecutor getExecutor(String nodeType) {
        if (nodeType == null || nodeType.trim().isEmpty()) {
            throw new WorkflowException("节点类型不能为空");
        }
        
        NodeExecutor executor = executorMap.get(nodeType.toLowerCase());
        if (executor == null) {
            throw new WorkflowException("不支持的节点类型: " + nodeType);
        }
        
        return executor;
    }
    
    /**
     * 检查是否支持指定节点类型
     * 
     * @param nodeType 节点类型
     * @return 是否支持
     */
    public boolean isSupported(String nodeType) {
        if (nodeType == null || nodeType.trim().isEmpty()) {
            return false;
        }
        return executorMap.containsKey(nodeType.toLowerCase());
    }
    
    /**
     * 获取所有支持的节点类型
     * 
     * @return 支持的节点类型列表
     */
    public java.util.Set<String> getSupportedNodeTypes() {
        return executorMap.keySet();
    }
    
    /**
     * 获取执行器数量
     * 
     * @return 执行器数量
     */
    public int getExecutorCount() {
        return executorMap.size();
    }
    
    /**
     * 注册新的执行器
     * 
     * @param executor 执行器
     */
    public void registerExecutor(NodeExecutor executor) {
        if (executor == null) {
            throw new IllegalArgumentException("执行器不能为空");
        }
        
        String nodeType = executor.getSupportedNodeType();
        if (nodeType == null || nodeType.trim().isEmpty()) {
            throw new IllegalArgumentException("执行器必须指定支持的节点类型");
        }
        
        executorMap.put(nodeType.toLowerCase(), executor);
        logger.info("动态注册节点执行器: {} -> {}", nodeType, executor.getClass().getSimpleName());
    }
    
    /**
     * 注销执行器
     * 
     * @param nodeType 节点类型
     */
    public void unregisterExecutor(String nodeType) {
        if (nodeType == null || nodeType.trim().isEmpty()) {
            return;
        }
        
        NodeExecutor removed = executorMap.remove(nodeType.toLowerCase());
        if (removed != null) {
            logger.info("注销节点执行器: {} -> {}", nodeType, removed.getClass().getSimpleName());
        }
    }
    
    /**
     * 获取执行器信息
     * 
     * @return 执行器信息映射
     */
    public Map<String, ExecutorInfo> getExecutorInfo() {
        Map<String, ExecutorInfo> infoMap = new ConcurrentHashMap<>();
        
        for (Map.Entry<String, NodeExecutor> entry : executorMap.entrySet()) {
            String nodeType = entry.getKey();
            NodeExecutor executor = entry.getValue();
            
            ExecutorInfo info = new ExecutorInfo(
                nodeType,
                executor.getClass().getSimpleName(),
                executor.getClass().getName(),
                executor.getPriority(),
                executor.supportsAsync(),
                executor.supportsRetry(),
                executor.supportsTimeout(),
                executor.getDefaultTimeoutMs(),
                executor.getDefaultRetryCount()
            );
            
            infoMap.put(nodeType, info);
        }
        
        return infoMap;
    }
    
    /**
     * 执行器信息类
     */
    public static class ExecutorInfo {
        private final String nodeType;
        private final String className;
        private final String fullClassName;
        private final int priority;
        private final boolean supportsAsync;
        private final boolean supportsRetry;
        private final boolean supportsTimeout;
        private final long defaultTimeoutMs;
        private final int defaultRetryCount;
        
        public ExecutorInfo(String nodeType, String className, String fullClassName, 
                           int priority, boolean supportsAsync, boolean supportsRetry, 
                           boolean supportsTimeout, long defaultTimeoutMs, int defaultRetryCount) {
            this.nodeType = nodeType;
            this.className = className;
            this.fullClassName = fullClassName;
            this.priority = priority;
            this.supportsAsync = supportsAsync;
            this.supportsRetry = supportsRetry;
            this.supportsTimeout = supportsTimeout;
            this.defaultTimeoutMs = defaultTimeoutMs;
            this.defaultRetryCount = defaultRetryCount;
        }
        
        // Getters
        public String getNodeType() { return nodeType; }
        public String getClassName() { return className; }
        public String getFullClassName() { return fullClassName; }
        public int getPriority() { return priority; }
        public boolean isSupportsAsync() { return supportsAsync; }
        public boolean isSupportsRetry() { return supportsRetry; }
        public boolean isSupportsTimeout() { return supportsTimeout; }
        public long getDefaultTimeoutMs() { return defaultTimeoutMs; }
        public int getDefaultRetryCount() { return defaultRetryCount; }
        
        @Override
        public String toString() {
            return "ExecutorInfo{" +
                    "nodeType='" + nodeType + '\'' +
                    ", className='" + className + '\'' +
                    ", priority=" + priority +
                    ", supportsAsync=" + supportsAsync +
                    ", supportsRetry=" + supportsRetry +
                    ", supportsTimeout=" + supportsTimeout +
                    ", defaultTimeoutMs=" + defaultTimeoutMs +
                    ", defaultRetryCount=" + defaultRetryCount +
                    '}';
        }
    }
}
