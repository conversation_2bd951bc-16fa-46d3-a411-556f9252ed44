package com.company.workflow.engine.context;

import com.company.workflow.dsl.model.WorkflowAST;
import com.company.workflow.model.entity.WorkflowDefinition;
import com.company.workflow.model.entity.WorkflowInstance;
import com.company.workflow.model.entity.NodeExecution;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 工作流执行上下文
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class ExecutionContext {
    
    private final WorkflowInstance instance;
    private final WorkflowAST workflowAST;
    private final WorkflowDefinition definition;
    
    // 执行状态
    private volatile boolean suspended = false;
    private volatile boolean cancelled = false;
    private final LocalDateTime startTime;
    private LocalDateTime endTime;
    
    // 变量存储
    private final Map<String, Object> variables = new ConcurrentHashMap<>();
    private final Map<String, Object> outputData = new ConcurrentHashMap<>();
    
    // 执行统计
    private final AtomicInteger completedNodes = new AtomicInteger(0);
    private final AtomicInteger failedNodes = new AtomicInteger(0);
    private final AtomicInteger totalNodes = new AtomicInteger(0);
    
    // 当前执行信息
    private volatile String currentNodeId;
    private volatile String currentNodeName;
    private volatile NodeExecution currentExecution;
    
    // 执行历史
    private final Map<String, NodeExecution> executionHistory = new ConcurrentHashMap<>();
    
    // 线程安全的执行锁
    private final Object executionLock = new Object();
    
    /**
     * 构造函数
     */
    public ExecutionContext(WorkflowInstance instance, WorkflowAST workflowAST, WorkflowDefinition definition) {
        this.instance = instance;
        this.workflowAST = workflowAST;
        this.definition = definition;
        this.startTime = LocalDateTime.now();
        this.totalNodes.set(workflowAST.getNodes().size());
        
        // 初始化变量
        if (workflowAST.getVariables() != null) {
            this.variables.putAll(workflowAST.getVariables());
        }
    }
    
    // Getters
    public WorkflowInstance getInstance() {
        return instance;
    }
    
    public WorkflowAST getWorkflowAST() {
        return workflowAST;
    }
    
    public WorkflowDefinition getDefinition() {
        return definition;
    }
    
    public boolean isSuspended() {
        return suspended;
    }
    
    public boolean isCancelled() {
        return cancelled;
    }
    
    public LocalDateTime getStartTime() {
        return startTime;
    }
    
    public LocalDateTime getEndTime() {
        return endTime;
    }
    
    public String getCurrentNodeId() {
        return currentNodeId;
    }
    
    public String getCurrentNodeName() {
        return currentNodeName;
    }
    
    public NodeExecution getCurrentExecution() {
        return currentExecution;
    }
    
    public Map<String, Object> getVariables() {
        return variables;
    }
    
    public Map<String, Object> getOutputData() {
        return outputData;
    }
    
    public int getCompletedNodes() {
        return completedNodes.get();
    }
    
    public int getFailedNodes() {
        return failedNodes.get();
    }
    
    public int getTotalNodes() {
        return totalNodes.get();
    }
    
    public Map<String, NodeExecution> getExecutionHistory() {
        return executionHistory;
    }
    
    public Object getExecutionLock() {
        return executionLock;
    }
    
    // Setters
    public void setSuspended(boolean suspended) {
        this.suspended = suspended;
    }
    
    public void setCancelled(boolean cancelled) {
        this.cancelled = cancelled;
    }
    
    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
    
    public void setCurrentNodeId(String currentNodeId) {
        this.currentNodeId = currentNodeId;
    }
    
    public void setCurrentNodeName(String currentNodeName) {
        this.currentNodeName = currentNodeName;
    }
    
    public void setCurrentExecution(NodeExecution currentExecution) {
        this.currentExecution = currentExecution;
    }
    
    public void setVariables(Map<String, Object> variables) {
        this.variables.clear();
        if (variables != null) {
            this.variables.putAll(variables);
        }
    }
    
    // 业务方法
    
    /**
     * 获取变量值
     */
    public Object getVariable(String name) {
        return variables.get(name);
    }
    
    /**
     * 设置变量值
     */
    public void setVariable(String name, Object value) {
        variables.put(name, value);
    }
    
    /**
     * 移除变量
     */
    public Object removeVariable(String name) {
        return variables.remove(name);
    }
    
    /**
     * 检查变量是否存在
     */
    public boolean hasVariable(String name) {
        return variables.containsKey(name);
    }
    
    /**
     * 获取输出数据
     */
    public Object getOutputValue(String name) {
        return outputData.get(name);
    }
    
    /**
     * 设置输出数据
     */
    public void setOutputValue(String name, Object value) {
        outputData.put(name, value);
    }
    
    /**
     * 添加输出数据
     */
    public void addOutputData(Map<String, Object> data) {
        if (data != null) {
            outputData.putAll(data);
        }
    }
    
    /**
     * 增加完成节点数
     */
    public int incrementCompletedNodes() {
        return completedNodes.incrementAndGet();
    }
    
    /**
     * 增加失败节点数
     */
    public int incrementFailedNodes() {
        return failedNodes.incrementAndGet();
    }
    
    /**
     * 添加执行历史
     */
    public void addExecutionHistory(String nodeId, NodeExecution execution) {
        executionHistory.put(nodeId, execution);
    }
    
    /**
     * 获取执行历史
     */
    public NodeExecution getExecutionHistory(String nodeId) {
        return executionHistory.get(nodeId);
    }
    
    /**
     * 检查节点是否已执行
     */
    public boolean hasExecuted(String nodeId) {
        return executionHistory.containsKey(nodeId);
    }
    
    /**
     * 计算执行进度百分比
     */
    public double getProgressPercentage() {
        if (totalNodes.get() == 0) {
            return 0.0;
        }
        return (double) completedNodes.get() / totalNodes.get() * 100.0;
    }
    
    /**
     * 计算执行时长（毫秒）
     */
    public long getExecutionDuration() {
        LocalDateTime end = endTime != null ? endTime : LocalDateTime.now();
        return java.time.Duration.between(startTime, end).toMillis();
    }
    
    /**
     * 检查是否可以继续执行
     */
    public boolean canContinue() {
        return !suspended && !cancelled;
    }
    
    /**
     * 检查是否已完成
     */
    public boolean isCompleted() {
        return endTime != null;
    }
    
    /**
     * 获取执行状态摘要
     */
    public ExecutionSummary getExecutionSummary() {
        return new ExecutionSummary(
                instance.getId(),
                instance.getWorkflowDefinitionId(),
                workflowAST.getName(),
                instance.getStatus(),
                currentNodeId,
                currentNodeName,
                completedNodes.get(),
                failedNodes.get(),
                totalNodes.get(),
                getProgressPercentage(),
                getExecutionDuration(),
                startTime,
                endTime,
                suspended,
                cancelled
        );
    }
    
    /**
     * 创建子上下文（用于子流程）
     */
    public ExecutionContext createChildContext(WorkflowInstance childInstance, WorkflowAST childWorkflowAST, WorkflowDefinition childDefinition) {
        ExecutionContext childContext = new ExecutionContext(childInstance, childWorkflowAST, childDefinition);
        
        // 继承父上下文的部分变量
        childContext.variables.putAll(this.variables);
        
        return childContext;
    }
    
    /**
     * 合并子上下文的结果
     */
    public void mergeChildContext(ExecutionContext childContext) {
        // 合并输出数据
        this.outputData.putAll(childContext.outputData);
        
        // 可以选择性地合并变量
        // this.variables.putAll(childContext.variables);
    }
    
    /**
     * 清理上下文资源
     */
    public void cleanup() {
        variables.clear();
        outputData.clear();
        executionHistory.clear();
        currentExecution = null;
    }
    
    @Override
    public String toString() {
        return "ExecutionContext{" +
                "instanceId='" + instance.getId() + '\'' +
                ", workflowName='" + workflowAST.getName() + '\'' +
                ", currentNodeId='" + currentNodeId + '\'' +
                ", currentNodeName='" + currentNodeName + '\'' +
                ", completedNodes=" + completedNodes.get() +
                ", failedNodes=" + failedNodes.get() +
                ", totalNodes=" + totalNodes.get() +
                ", progress=" + String.format("%.1f%%", getProgressPercentage()) +
                ", duration=" + getExecutionDuration() + "ms" +
                ", suspended=" + suspended +
                ", cancelled=" + cancelled +
                '}';
    }
    
    /**
     * 执行摘要内部类
     */
    public static class ExecutionSummary {
        private final String instanceId;
        private final String workflowDefinitionId;
        private final String workflowName;
        private final com.company.workflow.model.entity.InstanceStatus status;
        private final String currentNodeId;
        private final String currentNodeName;
        private final int completedNodes;
        private final int failedNodes;
        private final int totalNodes;
        private final double progressPercentage;
        private final long executionDuration;
        private final LocalDateTime startTime;
        private final LocalDateTime endTime;
        private final boolean suspended;
        private final boolean cancelled;
        
        public ExecutionSummary(String instanceId, String workflowDefinitionId, String workflowName,
                               com.company.workflow.model.entity.InstanceStatus status, String currentNodeId, String currentNodeName,
                               int completedNodes, int failedNodes, int totalNodes, double progressPercentage,
                               long executionDuration, LocalDateTime startTime, LocalDateTime endTime,
                               boolean suspended, boolean cancelled) {
            this.instanceId = instanceId;
            this.workflowDefinitionId = workflowDefinitionId;
            this.workflowName = workflowName;
            this.status = status;
            this.currentNodeId = currentNodeId;
            this.currentNodeName = currentNodeName;
            this.completedNodes = completedNodes;
            this.failedNodes = failedNodes;
            this.totalNodes = totalNodes;
            this.progressPercentage = progressPercentage;
            this.executionDuration = executionDuration;
            this.startTime = startTime;
            this.endTime = endTime;
            this.suspended = suspended;
            this.cancelled = cancelled;
        }
        
        // Getters
        public String getInstanceId() { return instanceId; }
        public String getWorkflowDefinitionId() { return workflowDefinitionId; }
        public String getWorkflowName() { return workflowName; }
        public com.company.workflow.model.entity.InstanceStatus getStatus() { return status; }
        public String getCurrentNodeId() { return currentNodeId; }
        public String getCurrentNodeName() { return currentNodeName; }
        public int getCompletedNodes() { return completedNodes; }
        public int getFailedNodes() { return failedNodes; }
        public int getTotalNodes() { return totalNodes; }
        public double getProgressPercentage() { return progressPercentage; }
        public long getExecutionDuration() { return executionDuration; }
        public LocalDateTime getStartTime() { return startTime; }
        public LocalDateTime getEndTime() { return endTime; }
        public boolean isSuspended() { return suspended; }
        public boolean isCancelled() { return cancelled; }
    }
}
