package com.company.workflow.engine;

import com.company.workflow.dsl.WorkflowDslParser;
import com.company.workflow.dsl.ValidationEngine;
import com.company.workflow.dsl.model.WorkflowAST;
import com.company.workflow.dsl.model.NodeAST;
import com.company.workflow.dsl.model.ASTModels.*;
import com.company.workflow.model.entity.*;
import com.company.workflow.repository.WorkflowDefinitionRepository;
import com.company.workflow.repository.WorkflowInstanceRepository;
import com.company.workflow.repository.NodeExecutionRepository;
import com.company.workflow.engine.executor.NodeExecutorFactory;
import com.company.workflow.engine.executor.NodeExecutor;
import com.company.workflow.engine.context.ExecutionContext;
import com.company.workflow.engine.state.StateManager;
import com.company.workflow.exception.WorkflowException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 工作流执行引擎
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
public class WorkflowEngine {
    
    private static final Logger logger = LoggerFactory.getLogger(WorkflowEngine.class);
    
    @Autowired
    private WorkflowDefinitionRepository workflowDefinitionRepository;
    
    @Autowired
    private WorkflowInstanceRepository workflowInstanceRepository;
    
    @Autowired
    private NodeExecutionRepository nodeExecutionRepository;
    
    @Autowired
    private WorkflowDslParser dslParser;
    
    @Autowired
    private ValidationEngine validationEngine;
    
    @Autowired
    private NodeExecutorFactory nodeExecutorFactory;
    
    @Autowired
    private StateManager stateManager;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    // 执行线程池
    private final ExecutorService executorService = Executors.newFixedThreadPool(20);
    
    // 运行中的实例缓存
    private final Map<String, ExecutionContext> runningInstances = new ConcurrentHashMap<>();
    
    /**
     * 启动工作流实例
     */
    @Transactional
    public String startWorkflow(String workflowDefinitionId, Map<String, Object> inputData, String createdBy) {
        try {
            logger.info("启动工作流实例，定义ID: {}, 创建者: {}", workflowDefinitionId, createdBy);
            
            // 1. 获取工作流定义
            WorkflowDefinition definition = workflowDefinitionRepository.findById(workflowDefinitionId)
                    .orElseThrow(() -> new WorkflowException("工作流定义不存在: " + workflowDefinitionId));
            
            if (!definition.isPublished()) {
                throw new WorkflowException("工作流定义未发布，无法执行: " + workflowDefinitionId);
            }
            
            // 2. 解析 DSL
            WorkflowAST workflowAST = dslParser.parse(definition.getDslContent());
            
            // 3. 验证工作流
            ValidationResult validationResult = validationEngine.validate(workflowAST);
            if (!validationResult.isValid()) {
                throw new WorkflowException("工作流定义验证失败: " + validationResult.getErrors());
            }
            
            // 4. 创建工作流实例
            String instanceId = generateInstanceId();
            WorkflowInstance instance = new WorkflowInstance();
            instance.setId(instanceId);
            instance.setWorkflowDefinitionId(workflowDefinitionId);
            instance.setInstanceName(workflowAST.getName() + " - " + LocalDateTime.now());
            instance.setStatus(InstanceStatus.PENDING);
            instance.setCreatedBy(createdBy);
            
            // 设置输入数据和变量
            if (inputData != null && !inputData.isEmpty()) {
                instance.setInputData(objectMapper.writeValueAsString(inputData));
            }
            
            // 初始化变量
            Map<String, Object> variables = new HashMap<>(workflowAST.getVariables());
            if (inputData != null) {
                variables.putAll(inputData);
            }
            instance.setVariables(objectMapper.writeValueAsString(variables));
            
            // 保存实例
            workflowInstanceRepository.save(instance);
            
            // 5. 创建执行上下文
            ExecutionContext context = new ExecutionContext(instance, workflowAST, definition);
            context.setVariables(variables);
            runningInstances.put(instanceId, context);
            
            // 6. 异步执行工作流
            CompletableFuture.runAsync(() -> executeWorkflow(context), executorService);
            
            logger.info("工作流实例启动成功，实例ID: {}", instanceId);
            return instanceId;
            
        } catch (Exception e) {
            logger.error("启动工作流实例失败", e);
            throw new WorkflowException("启动工作流实例失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 执行工作流
     */
    private void executeWorkflow(ExecutionContext context) {
        String instanceId = context.getInstance().getId();
        
        try {
            logger.info("开始执行工作流实例: {}", instanceId);
            
            // 1. 更新实例状态为运行中
            stateManager.startInstance(instanceId);
            
            // 2. 查找开始节点
            NodeAST startNode = context.getWorkflowAST().getStartNode();
            if (startNode == null) {
                throw new WorkflowException("工作流没有开始节点");
            }
            
            // 3. 从开始节点开始执行
            executeNode(context, startNode);
            
        } catch (Exception e) {
            logger.error("执行工作流实例失败: {}", instanceId, e);
            handleWorkflowError(context, e);
        } finally {
            // 清理运行中的实例缓存
            runningInstances.remove(instanceId);
        }
    }
    
    /**
     * 执行节点
     */
    private void executeNode(ExecutionContext context, NodeAST node) {
        String instanceId = context.getInstance().getId();
        String nodeId = node.getId();
        
        try {
            logger.debug("执行节点: {} ({})", node.getName(), nodeId);
            
            // 1. 创建节点执行记录
            NodeExecution nodeExecution = createNodeExecution(context, node);
            nodeExecutionRepository.save(nodeExecution);
            
            // 2. 更新实例当前节点
            stateManager.updateCurrentNode(instanceId, nodeId, node.getName());
            
            // 3. 获取节点执行器
            NodeExecutor executor = nodeExecutorFactory.getExecutor(node.getType());
            if (executor == null) {
                throw new WorkflowException("不支持的节点类型: " + node.getType());
            }
            
            // 4. 执行节点
            stateManager.startNodeExecution(nodeExecution.getId());
            
            try {
                executor.execute(context, node, nodeExecution);
                
                // 5. 处理执行成功
                handleNodeSuccess(context, node, nodeExecution);
                
            } catch (Exception e) {
                // 6. 处理执行失败
                handleNodeFailure(context, node, nodeExecution, e);
            }
            
        } catch (Exception e) {
            logger.error("执行节点失败: {} ({})", node.getName(), nodeId, e);
            handleWorkflowError(context, e);
        }
    }
    
    /**
     * 处理节点执行成功
     */
    private void handleNodeSuccess(ExecutionContext context, NodeAST node, NodeExecution nodeExecution) {
        try {
            // 1. 更新节点执行状态
            stateManager.completeNodeExecution(nodeExecution.getId());
            
            // 2. 检查是否为结束节点
            if ("end".equals(node.getType())) {
                handleWorkflowComplete(context);
                return;
            }
            
            // 3. 查找下一个节点
            List<NodeAST> nextNodes = findNextNodes(context, node);
            
            if (nextNodes.isEmpty()) {
                // 没有下一个节点，工作流完成
                handleWorkflowComplete(context);
            } else if (nextNodes.size() == 1) {
                // 单个下一节点，继续执行
                executeNode(context, nextNodes.get(0));
            } else {
                // 多个下一节点，并行执行
                executeParallelNodes(context, nextNodes);
            }
            
        } catch (Exception e) {
            logger.error("处理节点成功后续操作失败", e);
            handleWorkflowError(context, e);
        }
    }
    
    /**
     * 处理节点执行失败
     */
    private void handleNodeFailure(ExecutionContext context, NodeAST node, NodeExecution nodeExecution, Exception error) {
        try {
            // 1. 更新节点执行状态
            stateManager.failNodeExecution(nodeExecution.getId(), error.getMessage());
            
            // 2. 检查是否可以重试
            if (canRetryNode(node, nodeExecution)) {
                logger.info("节点执行失败，准备重试: {} ({})", node.getName(), node.getId());
                retryNode(context, node, nodeExecution);
                return;
            }
            
            // 3. 查找失败处理节点
            String failureNode = node.getOnFailure();
            if (failureNode != null) {
                NodeAST nextNode = context.getWorkflowAST().findNodeById(failureNode);
                if (nextNode != null) {
                    executeNode(context, nextNode);
                    return;
                }
            }
            
            // 4. 没有失败处理，工作流失败
            handleWorkflowFailure(context, error);
            
        } catch (Exception e) {
            logger.error("处理节点失败后续操作失败", e);
            handleWorkflowError(context, e);
        }
    }
    
    /**
     * 并行执行节点
     */
    private void executeParallelNodes(ExecutionContext context, List<NodeAST> nodes) {
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        
        for (NodeAST node : nodes) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                executeNode(context, node);
            }, executorService);
            futures.add(future);
        }
        
        // 等待所有并行节点完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenRun(() -> {
                    logger.debug("所有并行节点执行完成");
                })
                .exceptionally(throwable -> {
                    logger.error("并行节点执行失败", throwable);
                    handleWorkflowError(context, (Exception) throwable);
                    return null;
                });
    }
    
    /**
     * 查找下一个节点
     */
    private List<NodeAST> findNextNodes(ExecutionContext context, NodeAST currentNode) {
        List<NodeAST> nextNodes = new ArrayList<>();
        
        // 根据节点类型和配置查找下一个节点
        if ("decision".equals(currentNode.getType())) {
            // 决策节点：根据条件选择分支
            nextNodes.addAll(evaluateDecisionBranches(context, currentNode));
        } else if ("parallel".equals(currentNode.getType())) {
            // 并行节点：返回所有并行分支
            nextNodes.addAll(getParallelBranches(context, currentNode));
        } else {
            // 普通节点：查找成功分支
            String successNode = currentNode.getOnSuccess();
            if (successNode != null) {
                NodeAST nextNode = context.getWorkflowAST().findNodeById(successNode);
                if (nextNode != null) {
                    nextNodes.add(nextNode);
                }
            }
        }
        
        return nextNodes;
    }
    
    /**
     * 评估决策分支
     */
    private List<NodeAST> evaluateDecisionBranches(ExecutionContext context, NodeAST decisionNode) {
        List<NodeAST> selectedNodes = new ArrayList<>();
        
        // 简化实现：这里应该根据条件表达式评估结果选择分支
        // 实际实现需要表达式引擎来评估条件
        
        return selectedNodes;
    }
    
    /**
     * 获取并行分支
     */
    private List<NodeAST> getParallelBranches(ExecutionContext context, NodeAST parallelNode) {
        List<NodeAST> branchNodes = new ArrayList<>();
        
        // 简化实现：这里应该返回并行分支中的所有节点
        
        return branchNodes;
    }
    
    /**
     * 检查节点是否可以重试
     */
    private boolean canRetryNode(NodeAST node, NodeExecution nodeExecution) {
        return nodeExecution.getRetryCount() < nodeExecution.getMaxRetryCount();
    }
    
    /**
     * 重试节点
     */
    private void retryNode(ExecutionContext context, NodeAST node, NodeExecution nodeExecution) {
        // 增加重试次数
        nodeExecution.incrementRetryCount();
        nodeExecutionRepository.save(nodeExecution);
        
        // 延迟后重新执行
        CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(calculateRetryDelay(nodeExecution.getRetryCount()));
                executeNode(context, node);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.warn("重试节点被中断: {}", node.getName());
            }
        }, executorService);
    }
    
    /**
     * 计算重试延迟时间
     */
    private long calculateRetryDelay(int retryCount) {
        // 指数退避策略
        return Math.min(1000L * (1L << retryCount), 30000L);
    }
    
    /**
     * 处理工作流完成
     */
    private void handleWorkflowComplete(ExecutionContext context) {
        String instanceId = context.getInstance().getId();
        
        try {
            logger.info("工作流实例执行完成: {}", instanceId);
            
            // 更新实例状态
            stateManager.completeInstance(instanceId);
            
            // 更新工作流定义统计信息
            workflowDefinitionRepository.incrementSuccessCount(context.getInstance().getWorkflowDefinitionId());
            
        } catch (Exception e) {
            logger.error("处理工作流完成失败", e);
        }
    }
    
    /**
     * 处理工作流失败
     */
    private void handleWorkflowFailure(ExecutionContext context, Exception error) {
        String instanceId = context.getInstance().getId();
        
        try {
            logger.error("工作流实例执行失败: {}", instanceId, error);
            
            // 更新实例状态
            stateManager.failInstance(instanceId, error.getMessage());
            
            // 更新工作流定义统计信息
            workflowDefinitionRepository.incrementFailureCount(context.getInstance().getWorkflowDefinitionId());
            
        } catch (Exception e) {
            logger.error("处理工作流失败失败", e);
        }
    }
    
    /**
     * 处理工作流错误
     */
    private void handleWorkflowError(ExecutionContext context, Exception error) {
        handleWorkflowFailure(context, error);
    }
    
    /**
     * 创建节点执行记录
     */
    private NodeExecution createNodeExecution(ExecutionContext context, NodeAST node) {
        NodeExecution execution = new NodeExecution();
        execution.setId(generateExecutionId());
        execution.setInstanceId(context.getInstance().getId());
        execution.setNodeId(node.getId());
        execution.setNodeName(node.getName());
        execution.setNodeType(NodeType.fromCode(node.getType()));
        execution.setStatus(NodeStatus.PENDING);
        execution.setMaxRetryCount(node.getMaxRetryCount() != null ? node.getMaxRetryCount() : 3);
        execution.setTimeoutMs(node.getTimeoutMs());
        execution.setPriority(node.getPriority() != null ? node.getPriority() : 0);
        
        return execution;
    }
    
    /**
     * 生成实例ID
     */
    private String generateInstanceId() {
        return "inst_" + UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 生成执行ID
     */
    private String generateExecutionId() {
        return "exec_" + UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 暂停工作流实例
     */
    @Transactional
    public void suspendInstance(String instanceId) {
        try {
            logger.info("暂停工作流实例: {}", instanceId);
            stateManager.suspendInstance(instanceId);
            
            // 从运行缓存中移除
            runningInstances.remove(instanceId);
            
        } catch (Exception e) {
            logger.error("暂停工作流实例失败: {}", instanceId, e);
            throw new WorkflowException("暂停工作流实例失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 恢复工作流实例
     */
    @Transactional
    public void resumeInstance(String instanceId) {
        try {
            logger.info("恢复工作流实例: {}", instanceId);
            
            // 获取实例信息
            WorkflowInstance instance = workflowInstanceRepository.findById(instanceId)
                    .orElseThrow(() -> new WorkflowException("工作流实例不存在: " + instanceId));
            
            if (instance.getStatus() != InstanceStatus.SUSPENDED) {
                throw new WorkflowException("工作流实例状态不是暂停状态，无法恢复: " + instanceId);
            }
            
            // 重新创建执行上下文并继续执行
            // 这里需要根据当前节点状态恢复执行
            stateManager.resumeInstance(instanceId);
            
        } catch (Exception e) {
            logger.error("恢复工作流实例失败: {}", instanceId, e);
            throw new WorkflowException("恢复工作流实例失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 取消工作流实例
     */
    @Transactional
    public void cancelInstance(String instanceId) {
        try {
            logger.info("取消工作流实例: {}", instanceId);
            stateManager.cancelInstance(instanceId);
            
            // 从运行缓存中移除
            runningInstances.remove(instanceId);
            
        } catch (Exception e) {
            logger.error("取消工作流实例失败: {}", instanceId, e);
            throw new WorkflowException("取消工作流实例失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取运行中的实例数量
     */
    public int getRunningInstanceCount() {
        return runningInstances.size();
    }
    
    /**
     * 获取执行上下文
     */
    public ExecutionContext getExecutionContext(String instanceId) {
        return runningInstances.get(instanceId);
    }
}
