package com.company.workflow.engine.executor.impl;

import com.company.workflow.dsl.model.NodeAST;
import com.company.workflow.dsl.model.ASTModels.NotificationConfigAST;
import com.company.workflow.engine.context.ExecutionContext;
import com.company.workflow.engine.executor.AbstractNodeExecutor;
import com.company.workflow.model.entity.NodeExecution;
import com.company.workflow.service.NotificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 通知节点执行器
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Component
public class NotificationNodeExecutor extends AbstractNodeExecutor {
    
    @Autowired(required = false)
    private NotificationService notificationService;
    
    @Override
    public String getSupportedNodeType() {
        return "notification";
    }
    
    @Override
    public int getPriority() {
        return 60;
    }
    
    @Override
    public boolean supportsAsync() {
        return true;
    }
    
    @Override
    public boolean supportsRetry() {
        return true;
    }
    
    @Override
    public boolean supportsTimeout() {
        return true;
    }
    
    @Override
    public long getDefaultTimeoutMs() {
        return 30000; // 30秒
    }
    
    @Override
    public int getDefaultRetryCount() {
        return 3;
    }
    
    @Override
    protected Map<String, Object> doExecute(ExecutionContext context, NodeAST nodeAST, NodeExecution nodeExecution) throws Exception {
        logger.info("执行通知节点: {} ({})", nodeAST.getName(), nodeAST.getId());
        
        Map<String, Object> outputData = new HashMap<>();
        List<NotificationResult> results = new ArrayList<>();
        
        // 1. 获取通知配置
        List<NotificationConfigAST> notifications = nodeAST.getNotifications();
        if (notifications == null || notifications.isEmpty()) {
            throw createExecutionException("通知节点没有配置通知方式");
        }
        
        // 2. 准备通知内容
        NotificationContent content = prepareNotificationContent(context, nodeAST);
        
        // 3. 执行通知发送
        for (NotificationConfigAST notification : notifications) {
            try {
                NotificationResult result = sendNotification(notification, content, context, nodeAST);
                results.add(result);
                
                logger.debug("通知发送完成: {} -> {}", notification.getType(), result.isSuccess());
                
            } catch (Exception e) {
                logger.error("通知发送失败: {}", notification.getType(), e);
                
                NotificationResult result = new NotificationResult(
                    notification.getType(),
                    false,
                    e.getMessage(),
                    LocalDateTime.now()
                );
                results.add(result);
            }
        }
        
        // 4. 汇总结果
        outputData.put("notificationResults", results);
        outputData.put("totalNotifications", results.size());
        outputData.put("successfulNotifications", results.stream().mapToInt(r -> r.isSuccess() ? 1 : 0).sum());
        outputData.put("failedNotifications", results.stream().mapToInt(r -> r.isSuccess() ? 0 : 1).sum());
        outputData.put("notificationTime", LocalDateTime.now());
        outputData.put("notificationNodeId", nodeAST.getId());
        outputData.put("notificationNodeName", nodeAST.getName());
        
        // 5. 检查是否有失败的通知
        boolean hasFailures = results.stream().anyMatch(r -> !r.isSuccess());
        outputData.put("allNotificationsSuccessful", !hasFailures);
        
        if (hasFailures) {
            List<String> failureReasons = results.stream()
                .filter(r -> !r.isSuccess())
                .map(NotificationResult::getErrorMessage)
                .toList();
            outputData.put("failureReasons", failureReasons);
        }
        
        logger.info("通知节点执行完成: {}，成功: {}, 失败: {}", 
                   nodeAST.getName(), 
                   outputData.get("successfulNotifications"),
                   outputData.get("failedNotifications"));
        
        return outputData;
    }
    
    /**
     * 准备通知内容
     */
    private NotificationContent prepareNotificationContent(ExecutionContext context, NodeAST nodeAST) {
        NotificationContent content = new NotificationContent();
        
        // 1. 设置基本信息
        content.setTitle(getStringProperty(nodeAST, "title", "工作流通知"));
        content.setMessage(getStringProperty(nodeAST, "message", "工作流执行通知"));
        content.setTemplate(getStringProperty(nodeAST, "template", null));
        
        // 2. 设置收件人信息
        content.setRecipients(getRecipients(nodeAST, context));
        
        // 3. 设置模板变量
        Map<String, Object> templateVariables = new HashMap<>();
        templateVariables.put("instanceId", context.getInstance().getId());
        templateVariables.put("workflowName", context.getWorkflowAST().getName());
        templateVariables.put("workflowVersion", context.getWorkflowAST().getVersion());
        templateVariables.put("currentNodeName", nodeAST.getName());
        templateVariables.put("executionTime", LocalDateTime.now());
        templateVariables.put("progress", context.getProgressPercentage());
        templateVariables.putAll(context.getVariables());
        
        content.setTemplateVariables(templateVariables);
        
        // 4. 处理内容模板
        content.setTitle(processTemplate(content.getTitle(), templateVariables));
        content.setMessage(processTemplate(content.getMessage(), templateVariables));
        
        return content;
    }
    
    /**
     * 获取收件人列表
     */
    private List<String> getRecipients(NodeAST nodeAST, ExecutionContext context) {
        List<String> recipients = new ArrayList<>();
        
        // 从节点配置中获取收件人
        Object recipientsConfig = nodeAST.getProperty("recipients");
        if (recipientsConfig instanceof List) {
            @SuppressWarnings("unchecked")
            List<String> configRecipients = (List<String>) recipientsConfig;
            recipients.addAll(configRecipients);
        } else if (recipientsConfig instanceof String) {
            recipients.add((String) recipientsConfig);
        }
        
        // 从上下文变量中获取收件人
        Object contextRecipients = context.getVariable("recipients");
        if (contextRecipients instanceof List) {
            @SuppressWarnings("unchecked")
            List<String> contextRecipientsList = (List<String>) contextRecipients;
            recipients.addAll(contextRecipientsList);
        } else if (contextRecipients instanceof String) {
            recipients.add((String) contextRecipients);
        }
        
        // 如果没有指定收件人，使用创建者
        if (recipients.isEmpty() && context.getInstance().getCreatedBy() != null) {
            recipients.add(context.getInstance().getCreatedBy());
        }
        
        return recipients;
    }
    
    /**
     * 发送通知
     */
    private NotificationResult sendNotification(NotificationConfigAST notification, 
                                              NotificationContent content, 
                                              ExecutionContext context, 
                                              NodeAST nodeAST) throws Exception {
        
        String notificationType = notification.getType();
        Map<String, Object> config = notification.getConfig();
        
        logger.debug("发送通知: {} -> {}", notificationType, content.getRecipients());
        
        try {
            boolean success = false;
            String message = null;
            
            if (notificationService != null) {
                // 使用注入的通知服务
                success = notificationService.sendNotification(notificationType, content, config);
            } else {
                // 使用内置的通知发送逻辑
                success = sendNotificationInternal(notificationType, content, config);
            }
            
            return new NotificationResult(
                notificationType,
                success,
                message,
                LocalDateTime.now()
            );
            
        } catch (Exception e) {
            logger.error("发送通知异常: {}", notificationType, e);
            throw e;
        }
    }
    
    /**
     * 内置通知发送逻辑
     */
    private boolean sendNotificationInternal(String notificationType, 
                                           NotificationContent content, 
                                           Map<String, Object> config) throws Exception {
        
        switch (notificationType.toLowerCase()) {
            case "email":
                return sendEmailNotification(content, config);
            case "sms":
                return sendSmsNotification(content, config);
            case "wechat":
                return sendWechatNotification(content, config);
            case "dingtalk":
                return sendDingtalkNotification(content, config);
            case "webhook":
                return sendWebhookNotification(content, config);
            default:
                logger.warn("不支持的通知类型: {}", notificationType);
                return false;
        }
    }
    
    /**
     * 发送邮件通知
     */
    private boolean sendEmailNotification(NotificationContent content, Map<String, Object> config) {
        logger.info("发送邮件通知: {} -> {}", content.getTitle(), content.getRecipients());
        // 这里应该实现实际的邮件发送逻辑
        // 例如使用 Spring Mail 或其他邮件服务
        return true; // 简化实现
    }
    
    /**
     * 发送短信通知
     */
    private boolean sendSmsNotification(NotificationContent content, Map<String, Object> config) {
        logger.info("发送短信通知: {} -> {}", content.getMessage(), content.getRecipients());
        // 这里应该实现实际的短信发送逻辑
        return true; // 简化实现
    }
    
    /**
     * 发送企业微信通知
     */
    private boolean sendWechatNotification(NotificationContent content, Map<String, Object> config) {
        logger.info("发送企业微信通知: {} -> {}", content.getTitle(), content.getRecipients());
        // 这里应该实现实际的企业微信发送逻辑
        return true; // 简化实现
    }
    
    /**
     * 发送钉钉通知
     */
    private boolean sendDingtalkNotification(NotificationContent content, Map<String, Object> config) {
        logger.info("发送钉钉通知: {} -> {}", content.getTitle(), content.getRecipients());
        // 这里应该实现实际的钉钉发送逻辑
        return true; // 简化实现
    }
    
    /**
     * 发送Webhook通知
     */
    private boolean sendWebhookNotification(NotificationContent content, Map<String, Object> config) {
        logger.info("发送Webhook通知: {} -> {}", content.getTitle(), config.get("url"));
        // 这里应该实现实际的Webhook发送逻辑
        return true; // 简化实现
    }
    
    /**
     * 处理模板内容
     */
    private String processTemplate(String template, Map<String, Object> variables) {
        if (template == null) {
            return null;
        }
        
        String result = template;
        for (Map.Entry<String, Object> entry : variables.entrySet()) {
            String placeholder = "${" + entry.getKey() + "}";
            String value = entry.getValue() != null ? entry.getValue().toString() : "";
            result = result.replace(placeholder, value);
        }
        
        return result;
    }
    
    @Override
    public void preExecute(ExecutionContext context, NodeAST nodeAST, NodeExecution nodeExecution) {
        super.preExecute(context, nodeAST, nodeExecution);
        
        // 验证通知节点配置
        validateNotificationConfiguration(nodeAST);
        
        logger.debug("通知节点预处理完成: {}", nodeAST.getName());
    }
    
    /**
     * 验证通知节点配置
     */
    private void validateNotificationConfiguration(NodeAST nodeAST) {
        if (nodeAST.getNotifications() == null || nodeAST.getNotifications().isEmpty()) {
            throw createExecutionException("通知节点必须配置至少一种通知方式");
        }
    }
    
    /**
     * 通知内容类
     */
    private static class NotificationContent {
        private String title;
        private String message;
        private String template;
        private List<String> recipients = new ArrayList<>();
        private Map<String, Object> templateVariables = new HashMap<>();
        
        // Getters and Setters
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public String getTemplate() { return template; }
        public void setTemplate(String template) { this.template = template; }
        public List<String> getRecipients() { return recipients; }
        public void setRecipients(List<String> recipients) { this.recipients = recipients; }
        public Map<String, Object> getTemplateVariables() { return templateVariables; }
        public void setTemplateVariables(Map<String, Object> templateVariables) { this.templateVariables = templateVariables; }
    }
    
    /**
     * 通知结果类
     */
    private static class NotificationResult {
        private final String type;
        private final boolean success;
        private final String errorMessage;
        private final LocalDateTime sentTime;
        
        public NotificationResult(String type, boolean success, String errorMessage, LocalDateTime sentTime) {
            this.type = type;
            this.success = success;
            this.errorMessage = errorMessage;
            this.sentTime = sentTime;
        }
        
        // Getters
        public String getType() { return type; }
        public boolean isSuccess() { return success; }
        public String getErrorMessage() { return errorMessage; }
        public LocalDateTime getSentTime() { return sentTime; }
    }
}
