package com.company.workflow.engine.executor.impl;

import com.company.workflow.dsl.model.NodeAST;
import com.company.workflow.engine.context.ExecutionContext;
import com.company.workflow.engine.executor.AbstractNodeExecutor;
import com.company.workflow.model.entity.NodeExecution;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 开始节点执行器
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Component
public class StartNodeExecutor extends AbstractNodeExecutor {
    
    @Override
    public String getSupportedNodeType() {
        return "start";
    }
    
    @Override
    public int getPriority() {
        return 1; // 最高优先级
    }
    
    @Override
    public boolean supportsRetry() {
        return false; // 开始节点不支持重试
    }
    
    @Override
    public boolean supportsTimeout() {
        return false; // 开始节点不支持超时
    }
    
    @Override
    protected Map<String, Object> doExecute(ExecutionContext context, NodeAST nodeAST, NodeExecution nodeExecution) throws Exception {
        logger.info("执行开始节点: {} ({})", nodeAST.getName(), nodeAST.getId());
        
        Map<String, Object> outputData = new HashMap<>();
        
        // 1. 记录工作流开始时间
        outputData.put("workflowStartTime", LocalDateTime.now());
        outputData.put("workflowInstanceId", context.getInstance().getId());
        outputData.put("workflowDefinitionId", context.getInstance().getWorkflowDefinitionId());
        outputData.put("workflowName", context.getWorkflowAST().getName());
        outputData.put("workflowVersion", context.getWorkflowAST().getVersion());
        
        // 2. 设置初始变量
        Map<String, Object> initialVariables = context.getWorkflowAST().getVariables();
        if (initialVariables != null && !initialVariables.isEmpty()) {
            outputData.put("initialVariables", initialVariables);
            logger.debug("设置初始变量: {}", initialVariables.keySet());
        }
        
        // 3. 记录输入数据
        if (context.getInstance().getInputData() != null) {
            try {
                Map<String, Object> inputData = objectMapper.readValue(
                    context.getInstance().getInputData(), 
                    Map.class
                );
                outputData.put("inputData", inputData);
                logger.debug("记录输入数据: {}", inputData.keySet());
            } catch (Exception e) {
                logger.warn("解析输入数据失败", e);
            }
        }
        
        // 4. 设置执行上下文信息
        outputData.put("executorId", getExecutorId());
        outputData.put("executorName", getExecutorName());
        outputData.put("startNodeId", nodeAST.getId());
        outputData.put("startNodeName", nodeAST.getName());
        
        // 5. 触发开始事件
        triggerStartEvent(context, nodeAST, outputData);
        
        logger.info("开始节点执行完成: {}", nodeAST.getName());
        return outputData;
    }
    
    @Override
    public void preExecute(ExecutionContext context, NodeAST nodeAST, NodeExecution nodeExecution) {
        super.preExecute(context, nodeAST, nodeExecution);
        
        // 初始化执行统计
        context.incrementCompletedNodes();
        
        // 记录开始节点执行
        context.addExecutionHistory(nodeAST.getId(), nodeExecution);
        
        logger.debug("开始节点预处理完成: {}", nodeAST.getName());
    }
    
    @Override
    public void postExecute(ExecutionContext context, NodeAST nodeAST, NodeExecution nodeExecution, boolean success) {
        super.postExecute(context, nodeAST, nodeExecution, success);
        
        if (success) {
            // 更新实例状态信息
            context.setCurrentNodeId(nodeAST.getId());
            context.setCurrentNodeName(nodeAST.getName());
            
            logger.info("工作流实例 {} 已启动，当前节点: {}", 
                       context.getInstance().getId(), nodeAST.getName());
        }
    }
    
    /**
     * 触发开始事件
     */
    private void triggerStartEvent(ExecutionContext context, NodeAST nodeAST, Map<String, Object> outputData) {
        try {
            // 这里可以触发工作流开始事件
            // 例如发送通知、记录审计日志等
            
            logger.debug("触发工作流开始事件: {}", context.getInstance().getId());
            
            // 可以在这里添加事件监听器的调用
            // eventPublisher.publishEvent(new WorkflowStartedEvent(context, nodeAST, outputData));
            
        } catch (Exception e) {
            logger.warn("触发开始事件失败", e);
            // 不抛出异常，避免影响主流程
        }
    }
    
    /**
     * 获取执行器ID
     */
    private String getExecutorId() {
        // 这里可以返回实际的执行器ID，例如服务器ID、线程ID等
        return "executor-" + Thread.currentThread().getId();
    }
    
    /**
     * 获取执行器名称
     */
    private String getExecutorName() {
        // 这里可以返回实际的执行器名称，例如服务器名称等
        return "workflow-engine-" + java.net.InetAddress.getLoopbackAddress().getHostName();
    }
    
    @Override
    public boolean canExecute(ExecutionContext context, NodeAST nodeAST) {
        // 开始节点总是可以执行
        return true;
    }
    
    @Override
    public boolean handleException(ExecutionContext context, NodeAST nodeAST, NodeExecution nodeExecution, Exception exception) {
        // 开始节点执行失败通常意味着工作流无法启动
        logger.error("开始节点执行失败，工作流无法启动: {}", nodeAST.getName(), exception);
        
        // 记录失败原因
        Map<String, Object> errorData = new HashMap<>();
        errorData.put("errorType", "START_NODE_FAILURE");
        errorData.put("errorMessage", exception.getMessage());
        errorData.put("errorTime", LocalDateTime.now());
        
        try {
            context.addOutputData(errorData);
        } catch (Exception e) {
            logger.warn("记录开始节点错误信息失败", e);
        }
        
        return false; // 不处理异常，让其向上抛出
    }
}
