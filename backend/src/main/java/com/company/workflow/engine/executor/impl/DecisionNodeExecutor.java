package com.company.workflow.engine.executor.impl;

import com.company.workflow.dsl.model.NodeAST;
import com.company.workflow.dsl.model.ASTModels.ConditionBranchAST;
import com.company.workflow.engine.context.ExecutionContext;
import com.company.workflow.engine.executor.AbstractNodeExecutor;
import com.company.workflow.model.entity.NodeExecution;
import org.springframework.stereotype.Component;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 决策节点执行器
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Component
public class DecisionNodeExecutor extends AbstractNodeExecutor {
    
    private final ScriptEngineManager scriptEngineManager = new ScriptEngineManager();
    
    @Override
    public String getSupportedNodeType() {
        return "decision";
    }
    
    @Override
    public int getPriority() {
        return 30;
    }
    
    @Override
    public boolean supportsRetry() {
        return false; // 决策节点通常不需要重试
    }
    
    @Override
    public boolean supportsTimeout() {
        return true;
    }
    
    @Override
    public long getDefaultTimeoutMs() {
        return 10000; // 10秒
    }
    
    @Override
    protected Map<String, Object> doExecute(ExecutionContext context, NodeAST nodeAST, NodeExecution nodeExecution) throws Exception {
        logger.info("执行决策节点: {} ({})", nodeAST.getName(), nodeAST.getId());
        
        Map<String, Object> outputData = new HashMap<>();
        
        // 1. 评估决策条件
        DecisionResult decisionResult = evaluateDecision(context, nodeAST);
        
        // 2. 记录决策结果
        outputData.put("decisionResult", decisionResult.getResult());
        outputData.put("selectedBranch", decisionResult.getSelectedBranch());
        outputData.put("evaluatedConditions", decisionResult.getEvaluatedConditions());
        outputData.put("decisionTime", LocalDateTime.now());
        outputData.put("decisionNodeId", nodeAST.getId());
        outputData.put("decisionNodeName", nodeAST.getName());
        
        // 3. 设置下一个节点信息
        if (decisionResult.getSelectedBranch() != null) {
            outputData.put("nextNodeId", decisionResult.getSelectedBranch().getTargetNode());
            outputData.put("nextNodeCondition", decisionResult.getSelectedBranch().getCondition());
            outputData.put("nextNodeLabel", decisionResult.getSelectedBranch().getLabel());
        }
        
        logger.info("决策节点执行完成: {}，选择分支: {}", 
                   nodeAST.getName(), 
                   decisionResult.getSelectedBranch() != null ? 
                   decisionResult.getSelectedBranch().getLabel() : "无");
        
        return outputData;
    }
    
    /**
     * 评估决策条件
     */
    private DecisionResult evaluateDecision(ExecutionContext context, NodeAST nodeAST) throws Exception {
        DecisionResult result = new DecisionResult();
        
        // 1. 检查是否有条件分支
        if (nodeAST.getConditionBranches() != null && !nodeAST.getConditionBranches().isEmpty()) {
            result = evaluateConditionBranches(context, nodeAST.getConditionBranches());
        } 
        // 2. 检查是否有单一条件
        else if (nodeAST.getCondition() != null && !nodeAST.getCondition().trim().isEmpty()) {
            result = evaluateSingleCondition(context, nodeAST.getCondition());
        } 
        // 3. 没有条件配置
        else {
            throw createExecutionException("决策节点没有配置条件或条件分支");
        }
        
        return result;
    }
    
    /**
     * 评估条件分支
     */
    private DecisionResult evaluateConditionBranches(ExecutionContext context, List<ConditionBranchAST> branches) throws Exception {
        DecisionResult result = new DecisionResult();
        List<ConditionEvaluation> evaluations = new ArrayList<>();
        
        for (ConditionBranchAST branch : branches) {
            try {
                // 评估条件
                boolean conditionResult = evaluateConditionExpression(context, branch.getCondition());
                
                ConditionEvaluation evaluation = new ConditionEvaluation(
                    branch.getCondition(),
                    conditionResult,
                    branch.getTargetNode(),
                    branch.getLabel()
                );
                evaluations.add(evaluation);
                
                // 如果条件为真，选择这个分支
                if (conditionResult && result.getSelectedBranch() == null) {
                    result.setSelectedBranch(branch);
                    result.setResult(true);
                }
                
            } catch (Exception e) {
                logger.warn("评估条件分支失败: {}", branch.getCondition(), e);
                
                ConditionEvaluation evaluation = new ConditionEvaluation(
                    branch.getCondition(),
                    false,
                    branch.getTargetNode(),
                    branch.getLabel()
                );
                evaluation.setError(e.getMessage());
                evaluations.add(evaluation);
            }
        }
        
        result.setEvaluatedConditions(evaluations);
        
        // 如果没有匹配的分支，查找默认分支
        if (result.getSelectedBranch() == null) {
            ConditionBranchAST defaultBranch = findDefaultBranch(branches);
            if (defaultBranch != null) {
                result.setSelectedBranch(defaultBranch);
                result.setResult(true);
            } else {
                result.setResult(false);
                logger.warn("决策节点没有匹配的条件分支，也没有默认分支");
            }
        }
        
        return result;
    }
    
    /**
     * 评估单一条件
     */
    private DecisionResult evaluateSingleCondition(ExecutionContext context, String condition) throws Exception {
        DecisionResult result = new DecisionResult();
        
        try {
            boolean conditionResult = evaluateConditionExpression(context, condition);
            result.setResult(conditionResult);
            
            ConditionEvaluation evaluation = new ConditionEvaluation(
                condition,
                conditionResult,
                null,
                "主条件"
            );
            result.setEvaluatedConditions(List.of(evaluation));
            
        } catch (Exception e) {
            logger.error("评估决策条件失败: {}", condition, e);
            throw createExecutionException("评估决策条件失败: " + condition, e);
        }
        
        return result;
    }
    
    /**
     * 评估条件表达式
     */
    private boolean evaluateConditionExpression(ExecutionContext context, String condition) throws ScriptException {
        if (condition == null || condition.trim().isEmpty()) {
            return false;
        }
        
        // 创建JavaScript引擎
        ScriptEngine engine = scriptEngineManager.getEngineByName("javascript");
        
        // 设置上下文变量
        for (Map.Entry<String, Object> entry : context.getVariables().entrySet()) {
            engine.put(entry.getKey(), entry.getValue());
        }
        
        // 添加一些内置函数
        addBuiltinFunctions(engine);
        
        try {
            // 执行条件表达式
            Object result = engine.eval(condition);
            
            // 转换为布尔值
            if (result instanceof Boolean) {
                return (Boolean) result;
            } else if (result instanceof Number) {
                return ((Number) result).doubleValue() != 0.0;
            } else if (result instanceof String) {
                return !((String) result).isEmpty();
            } else {
                return result != null;
            }
            
        } catch (ScriptException e) {
            logger.error("执行条件表达式失败: {}", condition, e);
            throw e;
        }
    }
    
    /**
     * 添加内置函数到脚本引擎
     */
    private void addBuiltinFunctions(ScriptEngine engine) {
        // 添加一些常用的函数
        engine.put("isEmpty", (java.util.function.Function<String, Boolean>) str -> 
            str == null || str.trim().isEmpty());
        
        engine.put("isNotEmpty", (java.util.function.Function<String, Boolean>) str -> 
            str != null && !str.trim().isEmpty());
        
        engine.put("contains", (java.util.function.BiFunction<String, String, Boolean>) (str, substring) -> 
            str != null && substring != null && str.contains(substring));
        
        engine.put("startsWith", (java.util.function.BiFunction<String, String, Boolean>) (str, prefix) -> 
            str != null && prefix != null && str.startsWith(prefix));
        
        engine.put("endsWith", (java.util.function.BiFunction<String, String, Boolean>) (str, suffix) -> 
            str != null && suffix != null && str.endsWith(suffix));
    }
    
    /**
     * 查找默认分支
     */
    private ConditionBranchAST findDefaultBranch(List<ConditionBranchAST> branches) {
        return branches.stream()
                .filter(branch -> "default".equalsIgnoreCase(branch.getCondition()) || 
                                 "else".equalsIgnoreCase(branch.getCondition()) ||
                                 "true".equalsIgnoreCase(branch.getCondition()))
                .findFirst()
                .orElse(null);
    }
    
    @Override
    public void preExecute(ExecutionContext context, NodeAST nodeAST, NodeExecution nodeExecution) {
        super.preExecute(context, nodeAST, nodeExecution);
        
        // 验证决策节点配置
        validateDecisionConfiguration(nodeAST);
        
        logger.debug("决策节点预处理完成: {}", nodeAST.getName());
    }
    
    /**
     * 验证决策节点配置
     */
    private void validateDecisionConfiguration(NodeAST nodeAST) {
        boolean hasCondition = nodeAST.getCondition() != null && !nodeAST.getCondition().trim().isEmpty();
        boolean hasBranches = nodeAST.getConditionBranches() != null && !nodeAST.getConditionBranches().isEmpty();
        
        if (!hasCondition && !hasBranches) {
            throw createExecutionException("决策节点必须配置条件或条件分支");
        }
    }
    
    /**
     * 决策结果类
     */
    private static class DecisionResult {
        private boolean result;
        private ConditionBranchAST selectedBranch;
        private List<ConditionEvaluation> evaluatedConditions = new ArrayList<>();
        
        // Getters and Setters
        public boolean getResult() { return result; }
        public void setResult(boolean result) { this.result = result; }
        public ConditionBranchAST getSelectedBranch() { return selectedBranch; }
        public void setSelectedBranch(ConditionBranchAST selectedBranch) { this.selectedBranch = selectedBranch; }
        public List<ConditionEvaluation> getEvaluatedConditions() { return evaluatedConditions; }
        public void setEvaluatedConditions(List<ConditionEvaluation> evaluatedConditions) { this.evaluatedConditions = evaluatedConditions; }
    }
    
    /**
     * 条件评估结果类
     */
    private static class ConditionEvaluation {
        private final String condition;
        private final boolean result;
        private final String targetNode;
        private final String label;
        private String error;
        
        public ConditionEvaluation(String condition, boolean result, String targetNode, String label) {
            this.condition = condition;
            this.result = result;
            this.targetNode = targetNode;
            this.label = label;
        }
        
        // Getters and Setters
        public String getCondition() { return condition; }
        public boolean getResult() { return result; }
        public String getTargetNode() { return targetNode; }
        public String getLabel() { return label; }
        public String getError() { return error; }
        public void setError(String error) { this.error = error; }
    }
}
