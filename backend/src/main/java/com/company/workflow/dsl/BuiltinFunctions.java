package com.company.workflow.dsl;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * DSL 内置函数库
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class BuiltinFunctions {
    
    // ==================== 时间处理函数 ====================
    
    /**
     * 获取当前时间戳
     */
    public static long now() {
        return System.currentTimeMillis();
    }
    
    /**
     * 获取今天的日期
     */
    public static LocalDate today() {
        return LocalDate.now();
    }
    
    /**
     * 获取明天的日期
     */
    public static LocalDate tomorrow() {
        return LocalDate.now().plusDays(1);
    }
    
    /**
     * 日期加天数
     */
    public static LocalDate addDays(LocalDate date, int days) {
        return date.plusDays(days);
    }
    
    /**
     * 日期加小时
     */
    public static LocalDateTime addHours(LocalDateTime dateTime, int hours) {
        return dateTime.plusHours(hours);
    }
    
    /**
     * 格式化日期
     */
    public static String formatDate(LocalDateTime dateTime, String pattern) {
        return dateTime.format(DateTimeFormatter.ofPattern(pattern));
    }
    
    /**
     * 解析日期字符串
     */
    public static LocalDateTime parseDate(String dateString, String pattern) {
        return LocalDateTime.parse(dateString, DateTimeFormatter.ofPattern(pattern));
    }
    
    /**
     * 秒转毫秒
     */
    public static long seconds(int value) {
        return value * 1000L;
    }
    
    /**
     * 分钟转毫秒
     */
    public static long minutes(int value) {
        return value * 60 * 1000L;
    }
    
    /**
     * 小时转毫秒
     */
    public static long hours(int value) {
        return value * 60 * 60 * 1000L;
    }
    
    /**
     * 天转毫秒
     */
    public static long days(int value) {
        return value * 24 * 60 * 60 * 1000L;
    }
    
    // ==================== 字符串处理函数 ====================
    
    /**
     * 判断字符串是否为空
     */
    public static boolean isEmpty(String str) {
        return StringUtils.isEmpty(str);
    }
    
    /**
     * 判断字符串是否不为空
     */
    public static boolean isNotEmpty(String str) {
        return StringUtils.isNotEmpty(str);
    }
    
    /**
     * 判断字符串是否包含子串
     */
    public static boolean contains(String str, String substring) {
        return StringUtils.contains(str, substring);
    }
    
    /**
     * 字符串格式化
     */
    public static String format(String template, Object... args) {
        return String.format(template, args);
    }
    
    /**
     * 字符串替换
     */
    public static String replace(String str, String target, String replacement) {
        return StringUtils.replace(str, target, replacement);
    }
    
    /**
     * 字符串转大写
     */
    public static String upper(String str) {
        return StringUtils.upperCase(str);
    }
    
    /**
     * 字符串转小写
     */
    public static String lower(String str) {
        return StringUtils.lowerCase(str);
    }
    
    /**
     * 去除字符串首尾空格
     */
    public static String trim(String str) {
        return StringUtils.trim(str);
    }
    
    /**
     * 字符串分割
     */
    public static String[] split(String str, String separator) {
        return StringUtils.split(str, separator);
    }
    
    /**
     * 字符串连接
     */
    public static String join(String[] array, String separator) {
        return StringUtils.join(array, separator);
    }
    
    // ==================== 数据处理函数 ====================
    
    /**
     * JSON 解析
     */
    public static Map<String, Object> parseJson(String jsonString) {
        // 这里应该使用实际的 JSON 解析库，如 Jackson
        // 为了简化，这里返回空 Map
        return new HashMap<>();
    }
    
    /**
     * JSON 序列化
     */
    public static String toJson(Object object) {
        // 这里应该使用实际的 JSON 序列化库
        return object.toString();
    }
    
    /**
     * 列表过滤
     */
    public static <T> List<T> filter(List<T> list, java.util.function.Predicate<T> predicate) {
        return list.stream().filter(predicate).toList();
    }
    
    /**
     * 列表映射
     */
    public static <T, R> List<R> map(List<T> list, java.util.function.Function<T, R> mapper) {
        return list.stream().map(mapper).toList();
    }
    
    /**
     * 列表归约
     */
    public static <T> Optional<T> reduce(List<T> list, java.util.function.BinaryOperator<T> accumulator) {
        return list.stream().reduce(accumulator);
    }
    
    /**
     * 获取列表大小
     */
    public static int size(Collection<?> collection) {
        return collection != null ? collection.size() : 0;
    }
    
    /**
     * 判断列表是否为空
     */
    public static boolean isEmpty(Collection<?> collection) {
        return collection == null || collection.isEmpty();
    }
    
    // ==================== 数学函数 ====================
    
    /**
     * 最大值
     */
    public static int max(int a, int b) {
        return Math.max(a, b);
    }
    
    /**
     * 最小值
     */
    public static int min(int a, int b) {
        return Math.min(a, b);
    }
    
    /**
     * 绝对值
     */
    public static int abs(int value) {
        return Math.abs(value);
    }
    
    /**
     * 四舍五入
     */
    public static long round(double value) {
        return Math.round(value);
    }
    
    /**
     * 向上取整
     */
    public static double ceil(double value) {
        return Math.ceil(value);
    }
    
    /**
     * 向下取整
     */
    public static double floor(double value) {
        return Math.floor(value);
    }
    
    // ==================== 加密函数 ====================
    
    /**
     * MD5 哈希
     */
    public static String md5(String data) {
        return DigestUtils.md5Hex(data);
    }
    
    /**
     * SHA256 哈希
     */
    public static String sha256(String data) {
        return DigestUtils.sha256Hex(data);
    }
    
    /**
     * Base64 编码
     */
    public static String base64Encode(String data) {
        return Base64.getEncoder().encodeToString(data.getBytes(StandardCharsets.UTF_8));
    }
    
    /**
     * Base64 解码
     */
    public static String base64Decode(String encodedData) {
        return new String(Base64.getDecoder().decode(encodedData), StandardCharsets.UTF_8);
    }
    
    /**
     * AES 加密（简化版本）
     */
    public static String aesEncrypt(String data, String key) {
        try {
            SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), "AES");
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            byte[] encrypted = cipher.doFinal(data.getBytes());
            return Base64.getEncoder().encodeToString(encrypted);
        } catch (Exception e) {
            throw new RuntimeException("AES 加密失败", e);
        }
    }
    
    /**
     * AES 解密（简化版本）
     */
    public static String aesDecrypt(String encryptedData, String key) {
        try {
            SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), "AES");
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            byte[] decrypted = cipher.doFinal(Base64.getDecoder().decode(encryptedData));
            return new String(decrypted);
        } catch (Exception e) {
            throw new RuntimeException("AES 解密失败", e);
        }
    }
    
    // ==================== 随机函数 ====================
    
    /**
     * 生成 UUID
     */
    public static String uuid() {
        return UUID.randomUUID().toString();
    }
    
    /**
     * 生成随机字符串
     */
    public static String randomString(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder sb = new StringBuilder();
        ThreadLocalRandom random = ThreadLocalRandom.current();
        
        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        
        return sb.toString();
    }
    
    /**
     * 生成随机数字
     */
    public static int randomNumber(int min, int max) {
        return ThreadLocalRandom.current().nextInt(min, max + 1);
    }
    
    /**
     * 生成随机布尔值
     */
    public static boolean randomBoolean() {
        return ThreadLocalRandom.current().nextBoolean();
    }
    
    // ==================== 验证函数 ====================
    
    /**
     * 验证邮箱格式
     */
    public static boolean isEmail(String email) {
        return email != null && email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");
    }
    
    /**
     * 验证手机号格式
     */
    public static boolean isPhone(String phone) {
        return phone != null && phone.matches("^1[3-9]\\d{9}$");
    }
    
    /**
     * 验证身份证号格式
     */
    public static boolean isIdCard(String idCard) {
        return idCard != null && idCard.matches("^\\d{17}[\\dXx]$");
    }
    
    /**
     * 验证 URL 格式
     */
    public static boolean isUrl(String url) {
        return url != null && url.matches("^https?://[\\w\\-]+(\\.[\\w\\-]+)+([\\w\\-\\.,@?^=%&:/~\\+#]*[\\w\\-\\@?^=%&/~\\+#])?$");
    }
}
