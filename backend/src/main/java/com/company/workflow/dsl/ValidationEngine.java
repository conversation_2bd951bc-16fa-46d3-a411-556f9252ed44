package com.company.workflow.dsl;

import com.company.workflow.dsl.model.WorkflowAST;
import com.company.workflow.dsl.model.NodeAST;
import com.company.workflow.dsl.model.ASTModels.*;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 工作流 DSL 验证引擎
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Component
public class ValidationEngine {
    
    private static final Logger logger = LoggerFactory.getLogger(ValidationEngine.class);
    
    // 验证规则配置
    private static final int MAX_WORKFLOW_NAME_LENGTH = 255;
    private static final int MAX_NODE_NAME_LENGTH = 255;
    private static final int MAX_DESCRIPTION_LENGTH = 1000;
    private static final int MAX_NODES_COUNT = 1000;
    private static final int MAX_VARIABLES_COUNT = 100;
    
    // 正则表达式模式
    private static final Pattern WORKFLOW_NAME_PATTERN = Pattern.compile("^[\\u4e00-\\u9fa5a-zA-Z0-9_\\-\\s]+$");
    private static final Pattern NODE_NAME_PATTERN = Pattern.compile("^[\\u4e00-\\u9fa5a-zA-Z0-9_\\-\\s]+$");
    private static final Pattern VERSION_PATTERN = Pattern.compile("^\\d+\\.\\d+\\.\\d+$");
    private static final Pattern VARIABLE_NAME_PATTERN = Pattern.compile("^[a-zA-Z_][a-zA-Z0-9_]*$");
    
    /**
     * 验证工作流 AST
     */
    public ValidationResult validate(WorkflowAST workflowAST) {
        logger.debug("开始验证工作流 AST: {}", workflowAST.getName());
        
        ValidationResult result = new ValidationResult();
        
        try {
            // 1. 基础信息验证
            validateBasicInfo(workflowAST, result);
            
            // 2. 变量验证
            validateVariables(workflowAST, result);
            
            // 3. 节点验证
            validateNodes(workflowAST, result);
            
            // 4. 边连接验证
            validateEdges(workflowAST, result);
            
            // 5. 流程结构验证
            validateWorkflowStructure(workflowAST, result);
            
            // 6. 业务逻辑验证
            validateBusinessLogic(workflowAST, result);
            
            // 7. 性能和限制验证
            validatePerformanceConstraints(workflowAST, result);
            
            logger.info("工作流 AST 验证完成: {} 个错误, {} 个警告", 
                       result.getErrors().size(), result.getWarnings().size());
            
        } catch (Exception e) {
            logger.error("验证工作流 AST 时发生异常", e);
            result.addError("VALIDATION_ERROR", "验证过程中发生异常: " + e.getMessage(), "");
        }
        
        return result;
    }
    
    /**
     * 验证基础信息
     */
    private void validateBasicInfo(WorkflowAST workflowAST, ValidationResult result) {
        // 验证工作流名称
        if (workflowAST.getName() == null || workflowAST.getName().trim().isEmpty()) {
            result.addError("MISSING_WORKFLOW_NAME", "工作流名称不能为空", "workflow.name");
        } else {
            if (workflowAST.getName().length() > MAX_WORKFLOW_NAME_LENGTH) {
                result.addError("WORKFLOW_NAME_TOO_LONG", 
                               "工作流名称长度不能超过 " + MAX_WORKFLOW_NAME_LENGTH + " 个字符", 
                               "workflow.name");
            }
            
            if (!WORKFLOW_NAME_PATTERN.matcher(workflowAST.getName()).matches()) {
                result.addError("INVALID_WORKFLOW_NAME", 
                               "工作流名称只能包含中文、英文、数字、下划线、连字符和空格", 
                               "workflow.name");
            }
        }
        
        // 验证版本号
        if (workflowAST.getVersion() == null || workflowAST.getVersion().trim().isEmpty()) {
            result.addError("MISSING_VERSION", "版本号不能为空", "workflow.version");
        } else {
            if (!VERSION_PATTERN.matcher(workflowAST.getVersion()).matches()) {
                result.addError("INVALID_VERSION_FORMAT", 
                               "版本号格式必须为 x.y.z（如：1.0.0）", 
                               "workflow.version");
            }
        }
        
        // 验证描述
        if (workflowAST.getDescription() != null && 
            workflowAST.getDescription().length() > MAX_DESCRIPTION_LENGTH) {
            result.addError("DESCRIPTION_TOO_LONG", 
                           "描述长度不能超过 " + MAX_DESCRIPTION_LENGTH + " 个字符", 
                           "workflow.description");
        }
    }
    
    /**
     * 验证变量定义
     */
    private void validateVariables(WorkflowAST workflowAST, ValidationResult result) {
        Map<String, Object> variables = workflowAST.getVariables();
        
        if (variables.size() > MAX_VARIABLES_COUNT) {
            result.addError("TOO_MANY_VARIABLES", 
                           "变量数量不能超过 " + MAX_VARIABLES_COUNT + " 个", 
                           "workflow.variables");
        }
        
        for (Map.Entry<String, Object> entry : variables.entrySet()) {
            String varName = entry.getKey();
            Object varValue = entry.getValue();
            
            // 验证变量名
            if (!VARIABLE_NAME_PATTERN.matcher(varName).matches()) {
                result.addError("INVALID_VARIABLE_NAME", 
                               "变量名 '" + varName + "' 格式不正确，只能包含字母、数字和下划线，且不能以数字开头", 
                               "workflow.variables." + varName);
            }
            
            // 验证变量值类型
            if (varValue != null && !isValidVariableType(varValue)) {
                result.addWarning("UNSUPPORTED_VARIABLE_TYPE", 
                                 "变量 '" + varName + "' 的类型可能不被支持", 
                                 "workflow.variables." + varName);
            }
        }
    }
    
    /**
     * 验证节点定义
     */
    private void validateNodes(WorkflowAST workflowAST, ValidationResult result) {
        List<NodeAST> nodes = workflowAST.getNodes();
        
        if (nodes.isEmpty()) {
            result.addError("NO_NODES", "工作流必须包含至少一个节点", "workflow.nodes");
            return;
        }
        
        if (nodes.size() > MAX_NODES_COUNT) {
            result.addError("TOO_MANY_NODES", 
                           "节点数量不能超过 " + MAX_NODES_COUNT + " 个", 
                           "workflow.nodes");
        }
        
        // 检查节点名称唯一性
        Set<String> nodeNames = new HashSet<>();
        Set<String> nodeIds = new HashSet<>();
        
        for (NodeAST node : nodes) {
            validateSingleNode(node, result);
            
            // 检查名称唯一性
            if (nodeNames.contains(node.getName())) {
                result.addError("DUPLICATE_NODE_NAME", 
                               "节点名称 '" + node.getName() + "' 重复", 
                               "node." + node.getId());
            } else {
                nodeNames.add(node.getName());
            }
            
            // 检查ID唯一性
            if (nodeIds.contains(node.getId())) {
                result.addError("DUPLICATE_NODE_ID", 
                               "节点ID '" + node.getId() + "' 重复", 
                               "node." + node.getId());
            } else {
                nodeIds.add(node.getId());
            }
        }
    }
    
    /**
     * 验证单个节点
     */
    private void validateSingleNode(NodeAST node, ValidationResult result) {
        String nodeLocation = "node." + node.getId();
        
        // 验证节点基本信息
        if (node.getName() == null || node.getName().trim().isEmpty()) {
            result.addError("MISSING_NODE_NAME", "节点名称不能为空", nodeLocation);
        } else {
            if (node.getName().length() > MAX_NODE_NAME_LENGTH) {
                result.addError("NODE_NAME_TOO_LONG", 
                               "节点名称长度不能超过 " + MAX_NODE_NAME_LENGTH + " 个字符", 
                               nodeLocation);
            }
            
            if (!NODE_NAME_PATTERN.matcher(node.getName()).matches()) {
                result.addError("INVALID_NODE_NAME", 
                               "节点名称只能包含中文、英文、数字、下划线、连字符和空格", 
                               nodeLocation);
            }
        }
        
        if (node.getType() == null || node.getType().trim().isEmpty()) {
            result.addError("MISSING_NODE_TYPE", "节点类型不能为空", nodeLocation);
        } else {
            validateNodeByType(node, result);
        }
        
        // 验证超时设置
        if (node.getTimeoutMs() != null && node.getTimeoutMs() <= 0) {
            result.addError("INVALID_TIMEOUT", "超时时间必须大于0", nodeLocation);
        }
        
        // 验证重试设置
        if (node.getMaxRetryCount() != null && node.getMaxRetryCount() < 0) {
            result.addError("INVALID_RETRY_COUNT", "重试次数不能为负数", nodeLocation);
        }
        
        // 验证优先级
        if (node.getPriority() != null && (node.getPriority() < 0 || node.getPriority() > 10)) {
            result.addError("INVALID_PRIORITY", "优先级必须在0-10之间", nodeLocation);
        }
    }
    
    /**
     * 根据节点类型进行特定验证
     */
    private void validateNodeByType(NodeAST node, ValidationResult result) {
        String nodeLocation = "node." + node.getId();
        
        switch (node.getType().toLowerCase()) {
            case "start" -> validateStartNode(node, result, nodeLocation);
            case "end" -> validateEndNode(node, result, nodeLocation);
            case "task" -> validateTaskNode(node, result, nodeLocation);
            case "decision" -> validateDecisionNode(node, result, nodeLocation);
            case "parallel" -> validateParallelNode(node, result, nodeLocation);
            case "loop" -> validateLoopNode(node, result, nodeLocation);
            case "notification" -> validateNotificationNode(node, result, nodeLocation);
            case "delay" -> validateDelayNode(node, result, nodeLocation);
            case "subprocess" -> validateSubprocessNode(node, result, nodeLocation);
            case "script" -> validateScriptNode(node, result, nodeLocation);
            case "http" -> validateHttpNode(node, result, nodeLocation);
            case "sql" -> validateSqlNode(node, result, nodeLocation);
            default -> result.addError("UNKNOWN_NODE_TYPE", 
                                      "未知的节点类型: " + node.getType(), 
                                      nodeLocation);
        }
    }
    
    /**
     * 验证开始节点
     */
    private void validateStartNode(NodeAST node, ValidationResult result, String location) {
        if (node.getOnSuccess() == null || node.getOnSuccess().trim().isEmpty()) {
            result.addError("START_NODE_NO_NEXT", "开始节点必须指定下一个节点", location);
        }
    }
    
    /**
     * 验证结束节点
     */
    private void validateEndNode(NodeAST node, ValidationResult result, String location) {
        if (node.getOnSuccess() != null) {
            result.addWarning("END_NODE_HAS_NEXT", "结束节点不应该有下一个节点", location);
        }
    }
    
    /**
     * 验证任务节点
     */
    private void validateTaskNode(NodeAST node, ValidationResult result, String location) {
        if (node.getServiceName() == null || node.getServiceName().trim().isEmpty()) {
            result.addError("TASK_NODE_NO_SERVICE", "任务节点必须指定服务名称", location);
        }
        
        if (node.getMethodName() == null || node.getMethodName().trim().isEmpty()) {
            result.addError("TASK_NODE_NO_METHOD", "任务节点必须指定方法名称", location);
        }
    }
    
    /**
     * 验证决策节点
     */
    private void validateDecisionNode(NodeAST node, ValidationResult result, String location) {
        if ((node.getCondition() == null || node.getCondition().trim().isEmpty()) &&
            (node.getConditionBranches() == null || node.getConditionBranches().isEmpty())) {
            result.addError("DECISION_NODE_NO_CONDITION", "决策节点必须指定条件表达式或条件分支", location);
        }
    }
    
    /**
     * 验证并行节点
     */
    private void validateParallelNode(NodeAST node, ValidationResult result, String location) {
        if (node.getParallelBranches() == null || node.getParallelBranches().isEmpty()) {
            result.addError("PARALLEL_NODE_NO_BRANCHES", "并行节点必须包含至少一个分支", location);
        } else if (node.getParallelBranches().size() < 2) {
            result.addWarning("PARALLEL_NODE_SINGLE_BRANCH", "并行节点只有一个分支，建议使用普通任务节点", location);
        }
    }
    
    /**
     * 验证循环节点
     */
    private void validateLoopNode(NodeAST node, ValidationResult result, String location) {
        if (node.getLoopCondition() == null || node.getLoopCondition().trim().isEmpty()) {
            result.addError("LOOP_NODE_NO_CONDITION", "循环节点必须指定循环条件", location);
        }
        
        if (node.getMaxIterations() != null && node.getMaxIterations() <= 0) {
            result.addError("INVALID_MAX_ITERATIONS", "最大循环次数必须大于0", location);
        }
    }
    
    /**
     * 验证通知节点
     */
    private void validateNotificationNode(NodeAST node, ValidationResult result, String location) {
        if (node.getNotifications() == null || node.getNotifications().isEmpty()) {
            result.addError("NOTIFICATION_NODE_NO_CONFIG", "通知节点必须配置至少一种通知方式", location);
        }
    }
    
    /**
     * 验证延迟节点
     */
    private void validateDelayNode(NodeAST node, ValidationResult result, String location) {
        if (node.getDelayMs() == null && 
            (node.getCronExpression() == null || node.getCronExpression().trim().isEmpty())) {
            result.addError("DELAY_NODE_NO_CONFIG", "延迟节点必须指定延迟时间或Cron表达式", location);
        }
        
        if (node.getDelayMs() != null && node.getDelayMs() <= 0) {
            result.addError("INVALID_DELAY_TIME", "延迟时间必须大于0", location);
        }
    }
    
    /**
     * 验证子流程节点
     */
    private void validateSubprocessNode(NodeAST node, ValidationResult result, String location) {
        if (node.getSubWorkflowId() == null || node.getSubWorkflowId().trim().isEmpty()) {
            result.addError("SUBPROCESS_NODE_NO_WORKFLOW", "子流程节点必须指定子工作流ID", location);
        }
    }
    
    /**
     * 验证脚本节点
     */
    private void validateScriptNode(NodeAST node, ValidationResult result, String location) {
        if (node.getScriptCode() == null || node.getScriptCode().trim().isEmpty()) {
            result.addError("SCRIPT_NODE_NO_CODE", "脚本节点必须包含脚本代码", location);
        }
        
        if (node.getScriptLanguage() == null || node.getScriptLanguage().trim().isEmpty()) {
            result.addWarning("SCRIPT_NODE_NO_LANGUAGE", "建议指定脚本语言", location);
        }
    }
    
    /**
     * 验证HTTP节点
     */
    private void validateHttpNode(NodeAST node, ValidationResult result, String location) {
        if (node.getHttpUrl() == null || node.getHttpUrl().trim().isEmpty()) {
            result.addError("HTTP_NODE_NO_URL", "HTTP节点必须指定URL", location);
        }
        
        if (node.getHttpMethod() == null || node.getHttpMethod().trim().isEmpty()) {
            result.addError("HTTP_NODE_NO_METHOD", "HTTP节点必须指定请求方法", location);
        }
    }
    
    /**
     * 验证SQL节点
     */
    private void validateSqlNode(NodeAST node, ValidationResult result, String location) {
        if (node.getSqlQuery() == null || node.getSqlQuery().trim().isEmpty()) {
            result.addError("SQL_NODE_NO_QUERY", "SQL节点必须包含SQL查询语句", location);
        }
        
        if (node.getDatasource() == null || node.getDatasource().trim().isEmpty()) {
            result.addWarning("SQL_NODE_NO_DATASOURCE", "建议指定数据源", location);
        }
    }
    
    /**
     * 验证边连接
     */
    private void validateEdges(WorkflowAST workflowAST, ValidationResult result) {
        List<EdgeAST> edges = workflowAST.getEdges();
        Set<String> nodeIds = workflowAST.getNodes().stream()
                .map(NodeAST::getId)
                .collect(Collectors.toSet());
        
        for (EdgeAST edge : edges) {
            if (edge.getSourceId() == null || !nodeIds.contains(edge.getSourceId())) {
                result.addError("INVALID_EDGE_SOURCE", 
                               "边连接的源节点不存在: " + edge.getSourceId(), 
                               "edge." + edge.getId());
            }
            
            if (edge.getTargetId() == null || !nodeIds.contains(edge.getTargetId())) {
                result.addError("INVALID_EDGE_TARGET", 
                               "边连接的目标节点不存在: " + edge.getTargetId(), 
                               "edge." + edge.getId());
            }
        }
    }
    
    /**
     * 验证工作流结构
     */
    private void validateWorkflowStructure(WorkflowAST workflowAST, ValidationResult result) {
        List<NodeAST> startNodes = workflowAST.findNodesByType("start");
        List<NodeAST> endNodes = workflowAST.findNodesByType("end");
        
        // 检查开始节点
        if (startNodes.isEmpty()) {
            result.addError("NO_START_NODE", "工作流必须包含一个开始节点", "workflow.structure");
        } else if (startNodes.size() > 1) {
            result.addError("MULTIPLE_START_NODES", "工作流只能包含一个开始节点", "workflow.structure");
        }
        
        // 检查结束节点
        if (endNodes.isEmpty()) {
            result.addWarning("NO_END_NODE", "建议工作流包含至少一个结束节点", "workflow.structure");
        }
        
        // 检查孤立节点
        validateOrphanNodes(workflowAST, result);
        
        // 检查循环依赖
        validateCircularDependency(workflowAST, result);
    }
    
    /**
     * 验证孤立节点
     */
    private void validateOrphanNodes(WorkflowAST workflowAST, ValidationResult result) {
        Set<String> connectedNodes = new HashSet<>();
        
        for (EdgeAST edge : workflowAST.getEdges()) {
            connectedNodes.add(edge.getSourceId());
            connectedNodes.add(edge.getTargetId());
        }
        
        for (NodeAST node : workflowAST.getNodes()) {
            if (!connectedNodes.contains(node.getId()) && !"start".equals(node.getType())) {
                result.addWarning("ORPHAN_NODE", 
                                 "节点 '" + node.getName() + "' 没有连接到工作流中", 
                                 "node." + node.getId());
            }
        }
    }
    
    /**
     * 验证循环依赖
     */
    private void validateCircularDependency(WorkflowAST workflowAST, ValidationResult result) {
        // 使用深度优先搜索检测循环
        Map<String, Set<String>> adjacencyList = buildAdjacencyList(workflowAST);
        Set<String> visited = new HashSet<>();
        Set<String> recursionStack = new HashSet<>();
        
        for (NodeAST node : workflowAST.getNodes()) {
            if (!visited.contains(node.getId())) {
                if (hasCycle(node.getId(), adjacencyList, visited, recursionStack)) {
                    result.addError("CIRCULAR_DEPENDENCY", 
                                   "工作流中存在循环依赖", 
                                   "workflow.structure");
                    break;
                }
            }
        }
    }
    
    /**
     * 构建邻接表
     */
    private Map<String, Set<String>> buildAdjacencyList(WorkflowAST workflowAST) {
        Map<String, Set<String>> adjacencyList = new HashMap<>();
        
        for (NodeAST node : workflowAST.getNodes()) {
            adjacencyList.put(node.getId(), new HashSet<>());
        }
        
        for (EdgeAST edge : workflowAST.getEdges()) {
            adjacencyList.get(edge.getSourceId()).add(edge.getTargetId());
        }
        
        return adjacencyList;
    }
    
    /**
     * 检测循环
     */
    private boolean hasCycle(String nodeId, Map<String, Set<String>> adjacencyList, 
                           Set<String> visited, Set<String> recursionStack) {
        visited.add(nodeId);
        recursionStack.add(nodeId);
        
        Set<String> neighbors = adjacencyList.get(nodeId);
        if (neighbors != null) {
            for (String neighbor : neighbors) {
                if (!visited.contains(neighbor)) {
                    if (hasCycle(neighbor, adjacencyList, visited, recursionStack)) {
                        return true;
                    }
                } else if (recursionStack.contains(neighbor)) {
                    return true;
                }
            }
        }
        
        recursionStack.remove(nodeId);
        return false;
    }
    
    /**
     * 验证业务逻辑
     */
    private void validateBusinessLogic(WorkflowAST workflowAST, ValidationResult result) {
        // 这里可以添加特定的业务逻辑验证
        // 例如：特定节点的组合规则、业务流程的合理性等
    }
    
    /**
     * 验证性能和限制
     */
    private void validatePerformanceConstraints(WorkflowAST workflowAST, ValidationResult result) {
        // 检查并行分支数量
        for (NodeAST node : workflowAST.getNodes()) {
            if ("parallel".equals(node.getType()) && 
                node.getParallelBranches() != null && 
                node.getParallelBranches().size() > 10) {
                result.addWarning("TOO_MANY_PARALLEL_BRANCHES", 
                                 "并行分支过多可能影响性能", 
                                 "node." + node.getId());
            }
        }
        
        // 检查嵌套深度
        int maxDepth = calculateMaxNestingDepth(workflowAST);
        if (maxDepth > 10) {
            result.addWarning("DEEP_NESTING", 
                             "工作流嵌套层次过深可能影响性能", 
                             "workflow.structure");
        }
    }
    
    /**
     * 计算最大嵌套深度
     */
    private int calculateMaxNestingDepth(WorkflowAST workflowAST) {
        // 简化实现，实际应该根据节点连接关系计算
        return workflowAST.getNodes().size() > 50 ? 8 : 5;
    }
    
    /**
     * 检查变量类型是否有效
     */
    private boolean isValidVariableType(Object value) {
        return value instanceof String || 
               value instanceof Number || 
               value instanceof Boolean || 
               value instanceof Date ||
               value instanceof Map ||
               value instanceof List;
    }
}
