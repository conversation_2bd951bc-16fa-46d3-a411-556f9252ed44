package com.company.workflow.dsl;

import com.company.workflow.dsl.model.WorkflowAST;
import com.company.workflow.dsl.model.NodeAST;
import com.company.workflow.dsl.model.ASTModels.*;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 工作流 DSL 解析上下文
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class WorkflowDslContext {
    
    private String workflowName;
    private String version;
    private String description;
    private String category;
    private List<String> tags = new ArrayList<>();
    
    // 变量定义
    private Map<String, Object> variables = new ConcurrentHashMap<>();
    
    // 节点和边
    private List<NodeAST> nodes = new ArrayList<>();
    private List<EdgeAST> edges = new ArrayList<>();
    
    // 错误处理和监听器
    private List<ErrorHandlerAST> errorHandlers = new ArrayList<>();
    private List<ListenerAST> listeners = new ArrayList<>();
    
    // 节点名称到ID的映射
    private Map<String, String> nodeNameToIdMap = new ConcurrentHashMap<>();
    
    // 构造函数
    public WorkflowDslContext() {}
    
    /**
     * 构建工作流 AST
     */
    public WorkflowAST buildWorkflowAST() {
        WorkflowAST workflowAST = new WorkflowAST();
        
        // 设置基本信息
        workflowAST.setName(workflowName);
        workflowAST.setVersion(version);
        workflowAST.setDescription(description);
        workflowAST.setCategory(category);
        workflowAST.setTags(tags);
        workflowAST.setVariables(variables);
        
        // 解析节点名称引用
        resolveNodeReferences();
        
        // 设置节点和边
        workflowAST.setNodes(nodes);
        workflowAST.setEdges(edges);
        workflowAST.setErrorHandlers(errorHandlers);
        workflowAST.setListeners(listeners);
        
        return workflowAST;
    }
    
    /**
     * 解析节点名称引用为节点ID
     */
    private void resolveNodeReferences() {
        // 首先建立节点名称到ID的映射
        for (NodeAST node : nodes) {
            nodeNameToIdMap.put(node.getName(), node.getId());
        }
        
        // 解析节点中的引用
        for (NodeAST node : nodes) {
            resolveNodeReference(node, "onSuccess");
            resolveNodeReference(node, "onFailure");
            resolveNodeReference(node, "onTimeout");
            resolveNodeReference(node, "onCancel");
        }
        
        // 解析边中的引用
        for (EdgeAST edge : edges) {
            if (edge.getTargetId() != null && !edge.getTargetId().startsWith("node_")) {
                String targetId = nodeNameToIdMap.get(edge.getTargetId());
                if (targetId != null) {
                    edge.setTargetId(targetId);
                }
            }
        }
    }
    
    /**
     * 解析单个节点的引用
     */
    private void resolveNodeReference(NodeAST node, String property) {
        String reference = null;
        switch (property) {
            case "onSuccess" -> reference = node.getOnSuccess();
            case "onFailure" -> reference = node.getOnFailure();
            case "onTimeout" -> reference = node.getOnTimeout();
            case "onCancel" -> reference = node.getOnCancel();
        }
        
        if (reference != null && !reference.startsWith("node_")) {
            String nodeId = nodeNameToIdMap.get(reference);
            if (nodeId != null) {
                switch (property) {
                    case "onSuccess" -> node.setOnSuccess(nodeId);
                    case "onFailure" -> node.setOnFailure(nodeId);
                    case "onTimeout" -> node.setOnTimeout(nodeId);
                    case "onCancel" -> node.setOnCancel(nodeId);
                }
            }
        }
    }
    
    // Getters and Setters
    public String getWorkflowName() {
        return workflowName;
    }
    
    public void setWorkflowName(String workflowName) {
        this.workflowName = workflowName;
    }
    
    public String getVersion() {
        return version;
    }
    
    public void setVersion(String version) {
        this.version = version;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public List<String> getTags() {
        return tags;
    }
    
    public void setTags(List<String> tags) {
        this.tags = tags;
    }
    
    public Map<String, Object> getVariables() {
        return variables;
    }
    
    public void setVariables(Map<String, Object> variables) {
        this.variables = variables;
    }
    
    public List<NodeAST> getNodes() {
        return nodes;
    }
    
    public void setNodes(List<NodeAST> nodes) {
        this.nodes = nodes;
    }
    
    public List<EdgeAST> getEdges() {
        return edges;
    }
    
    public void setEdges(List<EdgeAST> edges) {
        this.edges = edges;
    }
    
    public List<ErrorHandlerAST> getErrorHandlers() {
        return errorHandlers;
    }
    
    public void setErrorHandlers(List<ErrorHandlerAST> errorHandlers) {
        this.errorHandlers = errorHandlers;
    }
    
    public List<ListenerAST> getListeners() {
        return listeners;
    }
    
    public void setListeners(List<ListenerAST> listeners) {
        this.listeners = listeners;
    }
    
    // 业务方法
    public void addVariable(String name, Object value) {
        this.variables.put(name, value);
    }
    
    public void addNode(NodeAST node) {
        this.nodes.add(node);
        this.nodeNameToIdMap.put(node.getName(), node.getId());
    }
    
    public void addEdge(EdgeAST edge) {
        this.edges.add(edge);
    }
    
    public void addErrorHandler(ErrorHandlerAST errorHandler) {
        this.errorHandlers.add(errorHandler);
    }
    
    public void addListener(ListenerAST listener) {
        this.listeners.add(listener);
    }
    
    public void addTag(String tag) {
        if (!this.tags.contains(tag)) {
            this.tags.add(tag);
        }
    }
    
    public NodeAST findNodeByName(String name) {
        return nodes.stream()
                .filter(node -> name.equals(node.getName()))
                .findFirst()
                .orElse(null);
    }
    
    public NodeAST findNodeById(String id) {
        return nodes.stream()
                .filter(node -> id.equals(node.getId()))
                .findFirst()
                .orElse(null);
    }
    
    public String getNodeIdByName(String name) {
        return nodeNameToIdMap.get(name);
    }
    
    public boolean hasNode(String name) {
        return nodeNameToIdMap.containsKey(name);
    }
    
    public int getNodeCount() {
        return nodes.size();
    }
    
    public int getEdgeCount() {
        return edges.size();
    }
    
    public boolean isEmpty() {
        return nodes.isEmpty();
    }
    
    public void clear() {
        workflowName = null;
        version = null;
        description = null;
        category = null;
        tags.clear();
        variables.clear();
        nodes.clear();
        edges.clear();
        errorHandlers.clear();
        listeners.clear();
        nodeNameToIdMap.clear();
    }
    
    @Override
    public String toString() {
        return "WorkflowDslContext{" +
                "workflowName='" + workflowName + '\'' +
                ", version='" + version + '\'' +
                ", description='" + description + '\'' +
                ", category='" + category + '\'' +
                ", tags=" + tags +
                ", nodesCount=" + nodes.size() +
                ", edgesCount=" + edges.size() +
                ", errorHandlersCount=" + errorHandlers.size() +
                ", listenersCount=" + listeners.size() +
                '}';
    }
}
