package com.company.workflow.dsl;

import com.company.workflow.dsl.model.WorkflowAST;
import com.company.workflow.dsl.model.NodeAST;
import com.company.workflow.dsl.model.ASTModels.*;
import com.company.workflow.exception.WorkflowDslParseException;
import groovy.lang.Binding;
import groovy.lang.GroovyShell;
import groovy.lang.Script;
import org.codehaus.groovy.ast.*;
import org.codehaus.groovy.ast.expr.*;
import org.codehaus.groovy.ast.stmt.*;
import org.codehaus.groovy.control.CompilerConfiguration;
import org.codehaus.groovy.control.customizers.ImportCustomizer;
import org.codehaus.groovy.control.customizers.SecureASTCustomizer;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 工作流 DSL 解析器
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Component
public class WorkflowDslParser {
    
    private static final Logger logger = LoggerFactory.getLogger(WorkflowDslParser.class);
    
    private final GroovyShell groovyShell;
    private final ASTTransformer astTransformer;
    private final Map<String, Object> builtinFunctions;
    
    public WorkflowDslParser() {
        this.groovyShell = createSecureGroovyShell();
        this.astTransformer = new ASTTransformer();
        this.builtinFunctions = initializeBuiltinFunctions();
    }
    
    /**
     * 解析工作流 DSL 内容
     */
    public WorkflowAST parse(String dslContent) {
        try {
            logger.debug("开始解析工作流 DSL，内容长度: {}", dslContent.length());
            
            // 1. 预处理 DSL 内容
            String processedContent = preprocessDsl(dslContent);
            
            // 2. 解析 Groovy 脚本
            Script script = groovyShell.parse(processedContent);
            
            // 3. 创建执行上下文
            WorkflowDslContext context = new WorkflowDslContext();
            Binding binding = new Binding();
            binding.setVariable("workflow", new WorkflowBuilder(context));
            script.setBinding(binding);
            
            // 4. 执行脚本构建 AST
            script.run();
            
            // 5. 转换为工作流 AST
            WorkflowAST workflowAST = context.buildWorkflowAST();
            
            logger.info("成功解析工作流 DSL: {}", workflowAST.getName());
            return workflowAST;
            
        } catch (Exception e) {
            logger.error("解析工作流 DSL 失败", e);
            throw new WorkflowDslParseException("解析工作流 DSL 失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建安全的 Groovy Shell
     */
    private GroovyShell createSecureGroovyShell() {
        CompilerConfiguration config = new CompilerConfiguration();
        
        // 导入自定义类
        ImportCustomizer importCustomizer = new ImportCustomizer();
        importCustomizer.addStaticStars("com.company.workflow.dsl.BuiltinFunctions");
        config.addCompilationCustomizers(importCustomizer);
        
        // 安全限制
        SecureASTCustomizer secureCustomizer = new SecureASTCustomizer();
        secureCustomizer.setClosuresAllowed(true);
        secureCustomizer.setMethodDefinitionAllowed(false);
        secureCustomizer.setPackageAllowed(false);
        secureCustomizer.setImportsWhitelist(Arrays.asList(
            "java.lang.*",
            "java.util.*",
            "java.time.*",
            "groovy.lang.*",
            "com.company.workflow.dsl.*"
        ));
        config.addCompilationCustomizers(secureCustomizer);
        
        return new GroovyShell(config);
    }
    
    /**
     * 预处理 DSL 内容
     */
    private String preprocessDsl(String dslContent) {
        // 添加内置函数导入
        StringBuilder processed = new StringBuilder();
        processed.append("import static com.company.workflow.dsl.BuiltinFunctions.*\n");
        processed.append(dslContent);
        
        return processed.toString();
    }
    
    /**
     * 初始化内置函数
     */
    private Map<String, Object> initializeBuiltinFunctions() {
        Map<String, Object> functions = new ConcurrentHashMap<>();
        
        // 时间函数
        functions.put("now", System::currentTimeMillis);
        functions.put("today", () -> new Date());
        functions.put("seconds", (Integer value) -> value * 1000L);
        functions.put("minutes", (Integer value) -> value * 60 * 1000L);
        functions.put("hours", (Integer value) -> value * 60 * 60 * 1000L);
        functions.put("days", (Integer value) -> value * 24 * 60 * 60 * 1000L);
        
        return functions;
    }
    
    /**
     * 工作流构建器
     */
    public static class WorkflowBuilder {
        private final WorkflowDslContext context;
        
        public WorkflowBuilder(WorkflowDslContext context) {
            this.context = context;
        }
        
        public Object call(String name, Closure<?> closure) {
            context.setWorkflowName(name);
            
            // 设置委托对象
            closure.setDelegate(new WorkflowDelegate(context));
            closure.setResolveStrategy(Closure.DELEGATE_FIRST);
            closure.call();
            
            return null;
        }
    }
    
    /**
     * 工作流委托对象
     */
    public static class WorkflowDelegate {
        private final WorkflowDslContext context;
        
        public WorkflowDelegate(WorkflowDslContext context) {
            this.context = context;
        }
        
        public void version(String version) {
            context.setVersion(version);
        }
        
        public void description(String description) {
            context.setDescription(description);
        }
        
        public void category(String category) {
            context.setCategory(category);
        }
        
        public void tags(List<String> tags) {
            context.setTags(tags);
        }
        
        public void variables(Closure<?> closure) {
            VariablesDelegate delegate = new VariablesDelegate(context);
            closure.setDelegate(delegate);
            closure.setResolveStrategy(Closure.DELEGATE_FIRST);
            closure.call();
        }
        
        public void process(Closure<?> closure) {
            ProcessDelegate delegate = new ProcessDelegate(context);
            closure.setDelegate(delegate);
            closure.setResolveStrategy(Closure.DELEGATE_FIRST);
            closure.call();
        }
        
        public void errorHandling(Closure<?> closure) {
            ErrorHandlingDelegate delegate = new ErrorHandlingDelegate(context);
            closure.setDelegate(delegate);
            closure.setResolveStrategy(Closure.DELEGATE_FIRST);
            closure.call();
        }
        
        public void listeners(Closure<?> closure) {
            ListenersDelegate delegate = new ListenersDelegate(context);
            closure.setDelegate(delegate);
            closure.setResolveStrategy(Closure.DELEGATE_FIRST);
            closure.call();
        }
    }
    
    /**
     * 变量委托对象
     */
    public static class VariablesDelegate {
        private final WorkflowDslContext context;
        
        public VariablesDelegate(WorkflowDslContext context) {
            this.context = context;
        }
        
        public Object propertyMissing(String name, Object value) {
            context.addVariable(name, value);
            return value;
        }
    }
    
    /**
     * 流程委托对象
     */
    public static class ProcessDelegate {
        private final WorkflowDslContext context;
        
        public ProcessDelegate(WorkflowDslContext context) {
            this.context = context;
        }
        
        public void start(String name, Closure<?> closure) {
            NodeAST node = new NodeAST(generateNodeId(), name, "start");
            configureNode(node, closure);
            context.addNode(node);
        }
        
        public void end(String name, Closure<?> closure) {
            NodeAST node = new NodeAST(generateNodeId(), name, "end");
            configureNode(node, closure);
            context.addNode(node);
        }
        
        public void task(String name, Closure<?> closure) {
            NodeAST node = new NodeAST(generateNodeId(), name, "task");
            configureNode(node, closure);
            context.addNode(node);
        }
        
        public void decision(String name, Closure<?> closure) {
            NodeAST node = new NodeAST(generateNodeId(), name, "decision");
            configureNode(node, closure);
            context.addNode(node);
        }
        
        public void parallel(String name, Closure<?> closure) {
            NodeAST node = new NodeAST(generateNodeId(), name, "parallel");
            configureNode(node, closure);
            context.addNode(node);
        }
        
        public void loop(String name, Closure<?> closure) {
            NodeAST node = new NodeAST(generateNodeId(), name, "loop");
            configureNode(node, closure);
            context.addNode(node);
        }
        
        public void notification(String name, Closure<?> closure) {
            NodeAST node = new NodeAST(generateNodeId(), name, "notification");
            configureNode(node, closure);
            context.addNode(node);
        }
        
        public void delay(String name, Closure<?> closure) {
            NodeAST node = new NodeAST(generateNodeId(), name, "delay");
            configureNode(node, closure);
            context.addNode(node);
        }
        
        public void subprocess(String name, Closure<?> closure) {
            NodeAST node = new NodeAST(generateNodeId(), name, "subprocess");
            configureNode(node, closure);
            context.addNode(node);
        }
        
        private void configureNode(NodeAST node, Closure<?> closure) {
            if (closure != null) {
                NodeDelegate delegate = new NodeDelegate(node, context);
                closure.setDelegate(delegate);
                closure.setResolveStrategy(Closure.DELEGATE_FIRST);
                closure.call();
            }
        }
        
        private String generateNodeId() {
            return "node_" + UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        }
    }
    
    /**
     * 节点委托对象
     */
    public static class NodeDelegate {
        private final NodeAST node;
        private final WorkflowDslContext context;
        
        public NodeDelegate(NodeAST node, WorkflowDslContext context) {
            this.node = node;
            this.context = context;
        }
        
        public void description(String description) {
            node.setDescription(description);
        }
        
        public void type(String type) {
            node.addProperty("type", type);
        }
        
        public void service(String service) {
            String[] parts = service.split("\\.");
            if (parts.length == 2) {
                node.setServiceName(parts[0]);
                node.setMethodName(parts[1]);
            } else {
                node.setServiceName(service);
            }
        }
        
        public void timeout(Object timeout) {
            if (timeout instanceof Number) {
                node.setTimeoutMs(((Number) timeout).longValue());
            }
        }
        
        public void async(Boolean async) {
            node.setAsync(async);
        }
        
        public void priority(Integer priority) {
            node.setPriority(priority);
        }
        
        public void condition(Closure<?> condition) {
            // 这里需要将 Closure 转换为字符串表达式
            node.setCondition(condition.toString());
        }
        
        public void next(String nextNode) {
            node.setOnSuccess(nextNode);
            // 创建边连接
            EdgeAST edge = new EdgeAST();
            edge.setSourceId(node.getId());
            edge.setTargetId(nextNode);
            context.addEdge(edge);
        }
        
        public void onSuccess(String nextNode) {
            node.setOnSuccess(nextNode);
            createEdge(node.getId(), nextNode, "success");
        }
        
        public void onFailure(String nextNode) {
            node.setOnFailure(nextNode);
            createEdge(node.getId(), nextNode, "failure");
        }
        
        public void onTimeout(String nextNode) {
            node.setOnTimeout(nextNode);
            createEdge(node.getId(), nextNode, "timeout");
        }
        
        public void input(Closure<?> closure) {
            Map<String, Object> inputMapping = new HashMap<>();
            closure.setDelegate(inputMapping);
            closure.setResolveStrategy(Closure.DELEGATE_FIRST);
            closure.call();
            node.setInputMapping(inputMapping);
        }
        
        public void output(Closure<?> closure) {
            Map<String, Object> outputMapping = new HashMap<>();
            closure.setDelegate(outputMapping);
            closure.setResolveStrategy(Closure.DELEGATE_FIRST);
            closure.call();
            node.setOutputMapping(outputMapping);
        }
        
        public void retry(Closure<?> closure) {
            RetryDelegate delegate = new RetryDelegate(node);
            closure.setDelegate(delegate);
            closure.setResolveStrategy(Closure.DELEGATE_FIRST);
            closure.call();
        }
        
        private void createEdge(String sourceId, String targetId, String condition) {
            EdgeAST edge = new EdgeAST();
            edge.setSourceId(sourceId);
            edge.setTargetId(targetId);
            edge.setCondition(condition);
            context.addEdge(edge);
        }
    }
    
    /**
     * 重试委托对象
     */
    public static class RetryDelegate {
        private final NodeAST node;
        
        public RetryDelegate(NodeAST node) {
            this.node = node;
        }
        
        public void maxAttempts(Integer maxAttempts) {
            node.setMaxRetryCount(maxAttempts);
        }
        
        public void backoff(String strategy) {
            node.setRetryStrategy(strategy);
        }
    }
    
    /**
     * 错误处理委托对象
     */
    public static class ErrorHandlingDelegate {
        private final WorkflowDslContext context;
        
        public ErrorHandlingDelegate(WorkflowDslContext context) {
            this.context = context;
        }
        
        public void catchException(String exceptionType, Closure<?> closure) {
            ErrorHandlerAST handler = new ErrorHandlerAST();
            handler.setExceptionType(exceptionType);
            
            // 配置错误处理器
            closure.setDelegate(handler);
            closure.setResolveStrategy(Closure.DELEGATE_FIRST);
            closure.call();
            
            context.addErrorHandler(handler);
        }
    }
    
    /**
     * 监听器委托对象
     */
    public static class ListenersDelegate {
        private final WorkflowDslContext context;
        
        public ListenersDelegate(WorkflowDslContext context) {
            this.context = context;
        }
        
        public void onStart(Closure<?> closure) {
            ListenerAST listener = new ListenerAST();
            listener.setEventType("onStart");
            listener.setHandlerType("script");
            listener.setHandler(closure.toString());
            context.addListener(listener);
        }
        
        public void onComplete(Closure<?> closure) {
            ListenerAST listener = new ListenerAST();
            listener.setEventType("onComplete");
            listener.setHandlerType("script");
            listener.setHandler(closure.toString());
            context.addListener(listener);
        }
        
        public void onError(Closure<?> closure) {
            ListenerAST listener = new ListenerAST();
            listener.setEventType("onError");
            listener.setHandlerType("script");
            listener.setHandler(closure.toString());
            context.addListener(listener);
        }
    }
}
