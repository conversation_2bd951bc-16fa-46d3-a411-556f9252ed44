package com.company.workflow.dsl.model;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 节点抽象语法树模型
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class NodeAST {
    
    private String id;
    private String name;
    private String type;
    private String description;
    
    // 节点属性
    private Map<String, Object> properties = new HashMap<>();
    
    // 输入输出映射
    private Map<String, Object> inputMapping = new HashMap<>();
    private Map<String, Object> outputMapping = new HashMap<>();
    
    // 执行配置
    private Long timeoutMs;
    private Integer maxRetryCount;
    private String retryStrategy;
    private Boolean async = false;
    private Integer priority = 0;
    
    // 条件表达式（用于决策节点）
    private String condition;
    private List<ConditionBranchAST> conditionBranches = new ArrayList<>();
    
    // 并行分支（用于并行节点）
    private List<ParallelBranchAST> parallelBranches = new ArrayList<>();
    
    // 循环配置（用于循环节点）
    private String loopCondition;
    private Integer maxIterations;
    private Long intervalMs;
    
    // 服务调用配置（用于任务节点）
    private String serviceName;
    private String methodName;
    private Map<String, Object> serviceParameters = new HashMap<>();
    
    // 脚本配置（用于脚本节点）
    private String scriptLanguage;
    private String scriptCode;
    
    // HTTP 配置（用于 HTTP 节点）
    private String httpMethod;
    private String httpUrl;
    private Map<String, String> httpHeaders = new HashMap<>();
    private Object httpBody;
    
    // SQL 配置（用于 SQL 节点）
    private String datasource;
    private String sqlQuery;
    private List<Object> sqlParameters = new ArrayList<>();
    
    // 通知配置（用于通知节点）
    private List<NotificationConfigAST> notifications = new ArrayList<>();
    
    // 延迟配置（用于延迟节点）
    private Long delayMs;
    private String cronExpression;
    
    // 子流程配置（用于子流程节点）
    private String subWorkflowId;
    private String subWorkflowVersion;
    
    // 人工任务配置（用于人工任务节点）
    private String assignee;
    private String candidateGroup;
    private List<FormFieldAST> formFields = new ArrayList<>();
    
    // 流转配置
    private String onSuccess;
    private String onFailure;
    private String onTimeout;
    private String onCancel;
    
    // 构造函数
    public NodeAST() {}
    
    public NodeAST(String id, String name, String type) {
        this.id = id;
        this.name = name;
        this.type = type;
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Map<String, Object> getProperties() {
        return properties;
    }
    
    public void setProperties(Map<String, Object> properties) {
        this.properties = properties;
    }
    
    public Map<String, Object> getInputMapping() {
        return inputMapping;
    }
    
    public void setInputMapping(Map<String, Object> inputMapping) {
        this.inputMapping = inputMapping;
    }
    
    public Map<String, Object> getOutputMapping() {
        return outputMapping;
    }
    
    public void setOutputMapping(Map<String, Object> outputMapping) {
        this.outputMapping = outputMapping;
    }
    
    public Long getTimeoutMs() {
        return timeoutMs;
    }
    
    public void setTimeoutMs(Long timeoutMs) {
        this.timeoutMs = timeoutMs;
    }
    
    public Integer getMaxRetryCount() {
        return maxRetryCount;
    }
    
    public void setMaxRetryCount(Integer maxRetryCount) {
        this.maxRetryCount = maxRetryCount;
    }
    
    public String getRetryStrategy() {
        return retryStrategy;
    }
    
    public void setRetryStrategy(String retryStrategy) {
        this.retryStrategy = retryStrategy;
    }
    
    public Boolean getAsync() {
        return async;
    }
    
    public void setAsync(Boolean async) {
        this.async = async;
    }
    
    public Integer getPriority() {
        return priority;
    }
    
    public void setPriority(Integer priority) {
        this.priority = priority;
    }
    
    public String getCondition() {
        return condition;
    }
    
    public void setCondition(String condition) {
        this.condition = condition;
    }
    
    public List<ConditionBranchAST> getConditionBranches() {
        return conditionBranches;
    }
    
    public void setConditionBranches(List<ConditionBranchAST> conditionBranches) {
        this.conditionBranches = conditionBranches;
    }
    
    public List<ParallelBranchAST> getParallelBranches() {
        return parallelBranches;
    }
    
    public void setParallelBranches(List<ParallelBranchAST> parallelBranches) {
        this.parallelBranches = parallelBranches;
    }
    
    public String getLoopCondition() {
        return loopCondition;
    }
    
    public void setLoopCondition(String loopCondition) {
        this.loopCondition = loopCondition;
    }
    
    public Integer getMaxIterations() {
        return maxIterations;
    }
    
    public void setMaxIterations(Integer maxIterations) {
        this.maxIterations = maxIterations;
    }
    
    public Long getIntervalMs() {
        return intervalMs;
    }
    
    public void setIntervalMs(Long intervalMs) {
        this.intervalMs = intervalMs;
    }
    
    public String getServiceName() {
        return serviceName;
    }
    
    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }
    
    public String getMethodName() {
        return methodName;
    }
    
    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }
    
    public Map<String, Object> getServiceParameters() {
        return serviceParameters;
    }
    
    public void setServiceParameters(Map<String, Object> serviceParameters) {
        this.serviceParameters = serviceParameters;
    }
    
    public String getScriptLanguage() {
        return scriptLanguage;
    }
    
    public void setScriptLanguage(String scriptLanguage) {
        this.scriptLanguage = scriptLanguage;
    }
    
    public String getScriptCode() {
        return scriptCode;
    }
    
    public void setScriptCode(String scriptCode) {
        this.scriptCode = scriptCode;
    }
    
    public String getHttpMethod() {
        return httpMethod;
    }
    
    public void setHttpMethod(String httpMethod) {
        this.httpMethod = httpMethod;
    }
    
    public String getHttpUrl() {
        return httpUrl;
    }
    
    public void setHttpUrl(String httpUrl) {
        this.httpUrl = httpUrl;
    }
    
    public Map<String, String> getHttpHeaders() {
        return httpHeaders;
    }
    
    public void setHttpHeaders(Map<String, String> httpHeaders) {
        this.httpHeaders = httpHeaders;
    }
    
    public Object getHttpBody() {
        return httpBody;
    }
    
    public void setHttpBody(Object httpBody) {
        this.httpBody = httpBody;
    }
    
    public String getDatasource() {
        return datasource;
    }
    
    public void setDatasource(String datasource) {
        this.datasource = datasource;
    }
    
    public String getSqlQuery() {
        return sqlQuery;
    }
    
    public void setSqlQuery(String sqlQuery) {
        this.sqlQuery = sqlQuery;
    }
    
    public List<Object> getSqlParameters() {
        return sqlParameters;
    }
    
    public void setSqlParameters(List<Object> sqlParameters) {
        this.sqlParameters = sqlParameters;
    }
    
    public List<NotificationConfigAST> getNotifications() {
        return notifications;
    }
    
    public void setNotifications(List<NotificationConfigAST> notifications) {
        this.notifications = notifications;
    }
    
    public Long getDelayMs() {
        return delayMs;
    }
    
    public void setDelayMs(Long delayMs) {
        this.delayMs = delayMs;
    }
    
    public String getCronExpression() {
        return cronExpression;
    }
    
    public void setCronExpression(String cronExpression) {
        this.cronExpression = cronExpression;
    }
    
    public String getSubWorkflowId() {
        return subWorkflowId;
    }
    
    public void setSubWorkflowId(String subWorkflowId) {
        this.subWorkflowId = subWorkflowId;
    }
    
    public String getSubWorkflowVersion() {
        return subWorkflowVersion;
    }
    
    public void setSubWorkflowVersion(String subWorkflowVersion) {
        this.subWorkflowVersion = subWorkflowVersion;
    }
    
    public String getAssignee() {
        return assignee;
    }
    
    public void setAssignee(String assignee) {
        this.assignee = assignee;
    }
    
    public String getCandidateGroup() {
        return candidateGroup;
    }
    
    public void setCandidateGroup(String candidateGroup) {
        this.candidateGroup = candidateGroup;
    }
    
    public List<FormFieldAST> getFormFields() {
        return formFields;
    }
    
    public void setFormFields(List<FormFieldAST> formFields) {
        this.formFields = formFields;
    }
    
    public String getOnSuccess() {
        return onSuccess;
    }
    
    public void setOnSuccess(String onSuccess) {
        this.onSuccess = onSuccess;
    }
    
    public String getOnFailure() {
        return onFailure;
    }
    
    public void setOnFailure(String onFailure) {
        this.onFailure = onFailure;
    }
    
    public String getOnTimeout() {
        return onTimeout;
    }
    
    public void setOnTimeout(String onTimeout) {
        this.onTimeout = onTimeout;
    }
    
    public String getOnCancel() {
        return onCancel;
    }
    
    public void setOnCancel(String onCancel) {
        this.onCancel = onCancel;
    }
    
    // 业务方法
    public void addProperty(String key, Object value) {
        this.properties.put(key, value);
    }
    
    public Object getProperty(String key) {
        return this.properties.get(key);
    }
    
    public void addInputMapping(String key, Object value) {
        this.inputMapping.put(key, value);
    }
    
    public void addOutputMapping(String key, Object value) {
        this.outputMapping.put(key, value);
    }
    
    public void addServiceParameter(String key, Object value) {
        this.serviceParameters.put(key, value);
    }
    
    public void addHttpHeader(String key, String value) {
        this.httpHeaders.put(key, value);
    }
    
    public void addSqlParameter(Object parameter) {
        this.sqlParameters.add(parameter);
    }
    
    public void addNotification(NotificationConfigAST notification) {
        this.notifications.add(notification);
    }
    
    public void addConditionBranch(ConditionBranchAST branch) {
        this.conditionBranches.add(branch);
    }
    
    public void addParallelBranch(ParallelBranchAST branch) {
        this.parallelBranches.add(branch);
    }
    
    public void addFormField(FormFieldAST field) {
        this.formFields.add(field);
    }
    
    @Override
    public String toString() {
        return "NodeAST{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", type='" + type + '\'' +
                ", description='" + description + '\'' +
                ", async=" + async +
                ", priority=" + priority +
                '}';
    }
}
