package com.company.workflow.dsl.model;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * DSL AST 辅助模型类集合
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class ASTModels {

    /**
     * 边连接 AST 模型
     */
    public static class EdgeAST {
        private String id;
        private String sourceId;
        private String targetId;
        private String condition;
        private String label;
        private Map<String, Object> properties = new HashMap<>();

        public EdgeAST() {}

        public EdgeAST(String sourceId, String targetId) {
            this.sourceId = sourceId;
            this.targetId = targetId;
        }

        // Getters and Setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getSourceId() { return sourceId; }
        public void setSourceId(String sourceId) { this.sourceId = sourceId; }
        public String getTargetId() { return targetId; }
        public void setTargetId(String targetId) { this.targetId = targetId; }
        public String getCondition() { return condition; }
        public void setCondition(String condition) { this.condition = condition; }
        public String getLabel() { return label; }
        public void setLabel(String label) { this.label = label; }
        public Map<String, Object> getProperties() { return properties; }
        public void setProperties(Map<String, Object> properties) { this.properties = properties; }
    }

    /**
     * 条件分支 AST 模型
     */
    public static class ConditionBranchAST {
        private String condition;
        private String targetNode;
        private String label;

        public ConditionBranchAST() {}

        public ConditionBranchAST(String condition, String targetNode) {
            this.condition = condition;
            this.targetNode = targetNode;
        }

        // Getters and Setters
        public String getCondition() { return condition; }
        public void setCondition(String condition) { this.condition = condition; }
        public String getTargetNode() { return targetNode; }
        public void setTargetNode(String targetNode) { this.targetNode = targetNode; }
        public String getLabel() { return label; }
        public void setLabel(String label) { this.label = label; }
    }

    /**
     * 并行分支 AST 模型
     */
    public static class ParallelBranchAST {
        private String name;
        private List<NodeAST> nodes = new ArrayList<>();
        private String joinStrategy = "waitAll";
        private Long timeoutMs;

        public ParallelBranchAST() {}

        public ParallelBranchAST(String name) {
            this.name = name;
        }

        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public List<NodeAST> getNodes() { return nodes; }
        public void setNodes(List<NodeAST> nodes) { this.nodes = nodes; }
        public String getJoinStrategy() { return joinStrategy; }
        public void setJoinStrategy(String joinStrategy) { this.joinStrategy = joinStrategy; }
        public Long getTimeoutMs() { return timeoutMs; }
        public void setTimeoutMs(Long timeoutMs) { this.timeoutMs = timeoutMs; }

        public void addNode(NodeAST node) {
            this.nodes.add(node);
        }
    }

    /**
     * 通知配置 AST 模型
     */
    public static class NotificationConfigAST {
        private String type; // email, sms, wechat, dingtalk
        private Map<String, Object> config = new HashMap<>();

        public NotificationConfigAST() {}

        public NotificationConfigAST(String type) {
            this.type = type;
        }

        // Getters and Setters
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        public Map<String, Object> getConfig() { return config; }
        public void setConfig(Map<String, Object> config) { this.config = config; }

        public void addConfig(String key, Object value) {
            this.config.put(key, value);
        }
    }

    /**
     * 表单字段 AST 模型
     */
    public static class FormFieldAST {
        private String name;
        private String type;
        private String label;
        private boolean required = false;
        private Object defaultValue;
        private List<String> options = new ArrayList<>();
        private Map<String, Object> validation = new HashMap<>();

        public FormFieldAST() {}

        public FormFieldAST(String name, String type, String label) {
            this.name = name;
            this.type = type;
            this.label = label;
        }

        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        public String getLabel() { return label; }
        public void setLabel(String label) { this.label = label; }
        public boolean isRequired() { return required; }
        public void setRequired(boolean required) { this.required = required; }
        public Object getDefaultValue() { return defaultValue; }
        public void setDefaultValue(Object defaultValue) { this.defaultValue = defaultValue; }
        public List<String> getOptions() { return options; }
        public void setOptions(List<String> options) { this.options = options; }
        public Map<String, Object> getValidation() { return validation; }
        public void setValidation(Map<String, Object> validation) { this.validation = validation; }
    }

    /**
     * 错误处理器 AST 模型
     */
    public static class ErrorHandlerAST {
        private String exceptionType;
        private String handlerType; // retry, skip, terminate, custom
        private String targetNode;
        private Integer maxRetryCount;
        private Long retryDelayMs;
        private String customHandler;
        private Map<String, Object> properties = new HashMap<>();

        public ErrorHandlerAST() {}

        public ErrorHandlerAST(String exceptionType, String handlerType) {
            this.exceptionType = exceptionType;
            this.handlerType = handlerType;
        }

        // Getters and Setters
        public String getExceptionType() { return exceptionType; }
        public void setExceptionType(String exceptionType) { this.exceptionType = exceptionType; }
        public String getHandlerType() { return handlerType; }
        public void setHandlerType(String handlerType) { this.handlerType = handlerType; }
        public String getTargetNode() { return targetNode; }
        public void setTargetNode(String targetNode) { this.targetNode = targetNode; }
        public Integer getMaxRetryCount() { return maxRetryCount; }
        public void setMaxRetryCount(Integer maxRetryCount) { this.maxRetryCount = maxRetryCount; }
        public Long getRetryDelayMs() { return retryDelayMs; }
        public void setRetryDelayMs(Long retryDelayMs) { this.retryDelayMs = retryDelayMs; }
        public String getCustomHandler() { return customHandler; }
        public void setCustomHandler(String customHandler) { this.customHandler = customHandler; }
        public Map<String, Object> getProperties() { return properties; }
        public void setProperties(Map<String, Object> properties) { this.properties = properties; }
    }

    /**
     * 事件监听器 AST 模型
     */
    public static class ListenerAST {
        private String eventType; // onStart, onComplete, onError, onNodeEnter, onNodeExit
        private String handlerType; // script, service, notification
        private String handler;
        private Map<String, Object> properties = new HashMap<>();

        public ListenerAST() {}

        public ListenerAST(String eventType, String handlerType, String handler) {
            this.eventType = eventType;
            this.handlerType = handlerType;
            this.handler = handler;
        }

        // Getters and Setters
        public String getEventType() { return eventType; }
        public void setEventType(String eventType) { this.eventType = eventType; }
        public String getHandlerType() { return handlerType; }
        public void setHandlerType(String handlerType) { this.handlerType = handlerType; }
        public String getHandler() { return handler; }
        public void setHandler(String handler) { this.handler = handler; }
        public Map<String, Object> getProperties() { return properties; }
        public void setProperties(Map<String, Object> properties) { this.properties = properties; }
    }

    /**
     * 重试策略 AST 模型
     */
    public static class RetryStrategyAST {
        private String type; // fixed, exponential, linear
        private Long initialDelayMs;
        private Double multiplier;
        private Long maxDelayMs;
        private Integer maxAttempts;
        private List<String> retryOnExceptions = new ArrayList<>();
        private List<String> stopOnExceptions = new ArrayList<>();

        public RetryStrategyAST() {}

        public RetryStrategyAST(String type, Integer maxAttempts) {
            this.type = type;
            this.maxAttempts = maxAttempts;
        }

        // Getters and Setters
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        public Long getInitialDelayMs() { return initialDelayMs; }
        public void setInitialDelayMs(Long initialDelayMs) { this.initialDelayMs = initialDelayMs; }
        public Double getMultiplier() { return multiplier; }
        public void setMultiplier(Double multiplier) { this.multiplier = multiplier; }
        public Long getMaxDelayMs() { return maxDelayMs; }
        public void setMaxDelayMs(Long maxDelayMs) { this.maxDelayMs = maxDelayMs; }
        public Integer getMaxAttempts() { return maxAttempts; }
        public void setMaxAttempts(Integer maxAttempts) { this.maxAttempts = maxAttempts; }
        public List<String> getRetryOnExceptions() { return retryOnExceptions; }
        public void setRetryOnExceptions(List<String> retryOnExceptions) { this.retryOnExceptions = retryOnExceptions; }
        public List<String> getStopOnExceptions() { return stopOnExceptions; }
        public void setStopOnExceptions(List<String> stopOnExceptions) { this.stopOnExceptions = stopOnExceptions; }
    }

    /**
     * 验证结果模型
     */
    public static class ValidationResult {
        private boolean valid = true;
        private List<ValidationError> errors = new ArrayList<>();
        private List<ValidationWarning> warnings = new ArrayList<>();

        public ValidationResult() {}

        // Getters and Setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        public List<ValidationError> getErrors() { return errors; }
        public void setErrors(List<ValidationError> errors) { this.errors = errors; }
        public List<ValidationWarning> getWarnings() { return warnings; }
        public void setWarnings(List<ValidationWarning> warnings) { this.warnings = warnings; }

        public void addError(String code, String message, String location) {
            this.errors.add(new ValidationError(code, message, location));
            this.valid = false;
        }

        public void addWarning(String code, String message, String location) {
            this.warnings.add(new ValidationWarning(code, message, location));
        }

        public boolean hasErrors() {
            return !errors.isEmpty();
        }

        public boolean hasWarnings() {
            return !warnings.isEmpty();
        }
    }

    /**
     * 验证错误模型
     */
    public static class ValidationError {
        private String code;
        private String message;
        private String location;

        public ValidationError(String code, String message, String location) {
            this.code = code;
            this.message = message;
            this.location = location;
        }

        // Getters and Setters
        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public String getLocation() { return location; }
        public void setLocation(String location) { this.location = location; }
    }

    /**
     * 验证警告模型
     */
    public static class ValidationWarning {
        private String code;
        private String message;
        private String location;

        public ValidationWarning(String code, String message, String location) {
            this.code = code;
            this.message = message;
            this.location = location;
        }

        // Getters and Setters
        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public String getLocation() { return location; }
        public void setLocation(String location) { this.location = location; }
    }
}
