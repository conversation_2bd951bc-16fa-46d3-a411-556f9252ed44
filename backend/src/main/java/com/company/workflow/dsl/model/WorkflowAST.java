package com.company.workflow.dsl.model;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工作流抽象语法树模型
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class WorkflowAST {
    
    private String name;
    private String version;
    private String description;
    private String category;
    private List<String> tags = new ArrayList<>();
    
    // 全局变量定义
    private Map<String, Object> variables = new HashMap<>();
    
    // 节点列表
    private List<NodeAST> nodes = new ArrayList<>();
    
    // 连接关系
    private List<EdgeAST> edges = new ArrayList<>();
    
    // 异常处理
    private List<ErrorHandlerAST> errorHandlers = new ArrayList<>();
    
    // 事件监听器
    private List<ListenerAST> listeners = new ArrayList<>();
    
    // 构造函数
    public WorkflowAST() {}
    
    public WorkflowAST(String name, String version) {
        this.name = name;
        this.version = version;
    }
    
    // Getters and Setters
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getVersion() {
        return version;
    }
    
    public void setVersion(String version) {
        this.version = version;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public List<String> getTags() {
        return tags;
    }
    
    public void setTags(List<String> tags) {
        this.tags = tags;
    }
    
    public Map<String, Object> getVariables() {
        return variables;
    }
    
    public void setVariables(Map<String, Object> variables) {
        this.variables = variables;
    }
    
    public List<NodeAST> getNodes() {
        return nodes;
    }
    
    public void setNodes(List<NodeAST> nodes) {
        this.nodes = nodes;
    }
    
    public List<EdgeAST> getEdges() {
        return edges;
    }
    
    public void setEdges(List<EdgeAST> edges) {
        this.edges = edges;
    }
    
    public List<ErrorHandlerAST> getErrorHandlers() {
        return errorHandlers;
    }
    
    public void setErrorHandlers(List<ErrorHandlerAST> errorHandlers) {
        this.errorHandlers = errorHandlers;
    }
    
    public List<ListenerAST> getListeners() {
        return listeners;
    }
    
    public void setListeners(List<ListenerAST> listeners) {
        this.listeners = listeners;
    }
    
    // 业务方法
    public void addNode(NodeAST node) {
        this.nodes.add(node);
    }
    
    public void addEdge(EdgeAST edge) {
        this.edges.add(edge);
    }
    
    public void addErrorHandler(ErrorHandlerAST errorHandler) {
        this.errorHandlers.add(errorHandler);
    }
    
    public void addListener(ListenerAST listener) {
        this.listeners.add(listener);
    }
    
    public NodeAST findNodeById(String nodeId) {
        return nodes.stream()
                .filter(node -> nodeId.equals(node.getId()))
                .findFirst()
                .orElse(null);
    }
    
    public NodeAST findNodeByName(String nodeName) {
        return nodes.stream()
                .filter(node -> nodeName.equals(node.getName()))
                .findFirst()
                .orElse(null);
    }
    
    public List<NodeAST> findNodesByType(String nodeType) {
        return nodes.stream()
                .filter(node -> nodeType.equals(node.getType()))
                .toList();
    }
    
    public NodeAST getStartNode() {
        return findNodesByType("start").stream().findFirst().orElse(null);
    }
    
    public List<NodeAST> getEndNodes() {
        return findNodesByType("end");
    }
    
    public List<EdgeAST> getOutgoingEdges(String nodeId) {
        return edges.stream()
                .filter(edge -> nodeId.equals(edge.getSourceId()))
                .toList();
    }
    
    public List<EdgeAST> getIncomingEdges(String nodeId) {
        return edges.stream()
                .filter(edge -> nodeId.equals(edge.getTargetId()))
                .toList();
    }
    
    public void addVariable(String name, Object value) {
        this.variables.put(name, value);
    }
    
    public Object getVariable(String name) {
        return this.variables.get(name);
    }
    
    public void addTag(String tag) {
        if (!this.tags.contains(tag)) {
            this.tags.add(tag);
        }
    }
    
    @Override
    public String toString() {
        return "WorkflowAST{" +
                "name='" + name + '\'' +
                ", version='" + version + '\'' +
                ", description='" + description + '\'' +
                ", category='" + category + '\'' +
                ", tags=" + tags +
                ", nodesCount=" + nodes.size() +
                ", edgesCount=" + edges.size() +
                ", errorHandlersCount=" + errorHandlers.size() +
                ", listenersCount=" + listeners.size() +
                '}';
    }
}
