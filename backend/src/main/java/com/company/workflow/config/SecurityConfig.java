package com.company.workflow.config;

import com.company.workflow.security.JwtAuthenticationEntryPoint;
import com.company.workflow.security.JwtAuthenticationFilter;
import com.company.workflow.security.WorkflowUserDetailsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;
import java.util.List;

/**
 * Spring Security 安全配置
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true, securedEnabled = true, jsr250Enabled = true)
public class SecurityConfig {
    
    @Autowired
    private WorkflowUserDetailsService userDetailsService;
    
    @Autowired
    private JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;
    
    @Autowired
    private JwtAuthenticationFilter jwtAuthenticationFilter;
    
    /**
     * 密码编码器
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
    
    /**
     * 认证管理器
     */
    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();
    }
    
    /**
     * 认证提供者
     */
    @Bean
    public DaoAuthenticationProvider authenticationProvider() {
        DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
        authProvider.setUserDetailsService(userDetailsService);
        authProvider.setPasswordEncoder(passwordEncoder());
        return authProvider;
    }
    
    /**
     * CORS 配置
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(List.of("*"));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"));
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setAllowCredentials(true);
        configuration.setMaxAge(3600L);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
    
    /**
     * 安全过滤器链
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            // 禁用 CSRF
            .csrf().disable()
            
            // 启用 CORS
            .cors().configurationSource(corsConfigurationSource())
            
            .and()
            
            // 异常处理
            .exceptionHandling()
            .authenticationEntryPoint(jwtAuthenticationEntryPoint)
            
            .and()
            
            // 会话管理 - 无状态
            .sessionManagement()
            .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            
            .and()
            
            // 请求授权配置
            .authorizeHttpRequests(authz -> authz
                // 公开接口
                .requestMatchers("/api/v1/auth/**").permitAll()
                .requestMatchers("/api/v1/public/**").permitAll()
                
                // Swagger 文档
                .requestMatchers("/swagger-ui/**", "/v3/api-docs/**", "/swagger-resources/**").permitAll()
                
                // 健康检查
                .requestMatchers("/actuator/health", "/actuator/info").permitAll()
                
                // 静态资源
                .requestMatchers("/static/**", "/favicon.ico").permitAll()
                
                // 工作流定义接口权限
                .requestMatchers(HttpMethod.GET, "/api/v1/workflow-definitions/**").hasAuthority("WORKFLOW_READ")
                .requestMatchers(HttpMethod.POST, "/api/v1/workflow-definitions").hasAuthority("WORKFLOW_CREATE")
                .requestMatchers(HttpMethod.PUT, "/api/v1/workflow-definitions/**").hasAuthority("WORKFLOW_UPDATE")
                .requestMatchers(HttpMethod.DELETE, "/api/v1/workflow-definitions/**").hasAuthority("WORKFLOW_DELETE")
                .requestMatchers(HttpMethod.POST, "/api/v1/workflow-definitions/*/publish").hasAuthority("WORKFLOW_MANAGE")
                .requestMatchers(HttpMethod.POST, "/api/v1/workflow-definitions/*/deactivate").hasAuthority("WORKFLOW_MANAGE")
                .requestMatchers(HttpMethod.POST, "/api/v1/workflow-definitions/*/deprecate").hasAuthority("WORKFLOW_MANAGE")
                
                // 工作流执行接口权限
                .requestMatchers(HttpMethod.POST, "/api/v1/workflow-executions/start").hasAuthority("WORKFLOW_EXECUTE")
                .requestMatchers(HttpMethod.POST, "/api/v1/workflow-executions/start-by-name/**").hasAuthority("WORKFLOW_EXECUTE")
                .requestMatchers(HttpMethod.GET, "/api/v1/workflow-executions/**").hasAuthority("INSTANCE_READ")
                .requestMatchers(HttpMethod.POST, "/api/v1/workflow-executions/*/suspend").hasAuthority("INSTANCE_UPDATE")
                .requestMatchers(HttpMethod.POST, "/api/v1/workflow-executions/*/resume").hasAuthority("INSTANCE_UPDATE")
                .requestMatchers(HttpMethod.POST, "/api/v1/workflow-executions/*/cancel").hasAuthority("INSTANCE_UPDATE")
                .requestMatchers(HttpMethod.POST, "/api/v1/workflow-executions/*/retry").hasAuthority("INSTANCE_UPDATE")
                .requestMatchers(HttpMethod.PUT, "/api/v1/workflow-executions/*/variables").hasAuthority("INSTANCE_UPDATE")
                .requestMatchers(HttpMethod.DELETE, "/api/v1/workflow-executions/**").hasAuthority("INSTANCE_DELETE")
                
                // 用户管理接口权限
                .requestMatchers("/api/v1/users/**").hasAuthority("USER_READ")
                .requestMatchers(HttpMethod.POST, "/api/v1/users").hasAuthority("USER_CREATE")
                .requestMatchers(HttpMethod.PUT, "/api/v1/users/**").hasAuthority("USER_UPDATE")
                .requestMatchers(HttpMethod.DELETE, "/api/v1/users/**").hasAuthority("USER_DELETE")
                
                // 角色管理接口权限
                .requestMatchers("/api/v1/roles/**").hasAuthority("ROLE_READ")
                .requestMatchers(HttpMethod.POST, "/api/v1/roles").hasAuthority("ROLE_CREATE")
                .requestMatchers(HttpMethod.PUT, "/api/v1/roles/**").hasAuthority("ROLE_UPDATE")
                .requestMatchers(HttpMethod.DELETE, "/api/v1/roles/**").hasAuthority("ROLE_DELETE")
                
                // 系统管理接口权限
                .requestMatchers("/api/v1/system/**").hasAuthority("SYSTEM_ADMIN")
                
                // 其他所有请求都需要认证
                .anyRequest().authenticated()
            );
        
        // 添加 JWT 过滤器
        http.addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);
        
        return http.build();
    }
}
