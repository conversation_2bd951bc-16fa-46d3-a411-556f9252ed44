package com.company.workflow.service;

import com.company.workflow.dto.*;
import com.company.workflow.engine.WorkflowEngine;
import com.company.workflow.engine.context.ExecutionContext;
import com.company.workflow.exception.WorkflowException;
import com.company.workflow.model.entity.*;
import com.company.workflow.repository.WorkflowDefinitionRepository;
import com.company.workflow.repository.WorkflowInstanceRepository;
import com.company.workflow.repository.NodeExecutionRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 工作流执行服务
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
@Transactional
public class WorkflowExecutionService {
    
    private static final Logger logger = LoggerFactory.getLogger(WorkflowExecutionService.class);
    
    @Autowired
    private WorkflowEngine workflowEngine;
    
    @Autowired
    private WorkflowDefinitionRepository workflowDefinitionRepository;
    
    @Autowired
    private WorkflowInstanceRepository workflowInstanceRepository;
    
    @Autowired
    private NodeExecutionRepository nodeExecutionRepository;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 启动工作流实例
     */
    public WorkflowInstanceDTO startWorkflowInstance(WorkflowExecutionRequest request) {
        logger.info("启动工作流实例: {}", request.getWorkflowDefinitionId());
        
        try {
            // 1. 验证工作流定义
            WorkflowDefinition definition = workflowDefinitionRepository.findById(request.getWorkflowDefinitionId())
                    .orElseThrow(() -> new WorkflowException("工作流定义不存在: " + request.getWorkflowDefinitionId()));
            
            if (!definition.isPublished() || !definition.isActive()) {
                throw new WorkflowException("工作流定义未发布或已停用，无法执行");
            }
            
            // 2. 检查业务键唯一性
            if (request.getBusinessKey() != null && 
                workflowInstanceRepository.existsByBusinessKey(request.getBusinessKey())) {
                throw new WorkflowException("业务键已存在: " + request.getBusinessKey());
            }
            
            // 3. 启动工作流引擎
            String instanceId = workflowEngine.startWorkflow(
                request.getWorkflowDefinitionId(),
                request.getInputData(),
                getCurrentUserId()
            );
            
            // 4. 获取创建的实例
            WorkflowInstance instance = workflowInstanceRepository.findById(instanceId)
                    .orElseThrow(() -> new WorkflowException("工作流实例创建失败"));
            
            // 5. 设置业务键和其他属性
            if (request.getBusinessKey() != null) {
                instance.setBusinessKey(request.getBusinessKey());
            }
            if (request.getPriority() != null) {
                instance.setPriority(request.getPriority());
            }
            if (request.getTenantId() != null) {
                instance.setTenantId(request.getTenantId());
            }
            
            instance = workflowInstanceRepository.save(instance);
            
            logger.info("工作流实例启动成功: {}", instanceId);
            return convertToDTO(instance);
            
        } catch (Exception e) {
            logger.error("启动工作流实例失败", e);
            throw new WorkflowException("启动工作流实例失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 根据工作流名称启动实例
     */
    public WorkflowInstanceDTO startWorkflowByName(String workflowName, Map<String, Object> inputData) {
        logger.info("根据名称启动工作流: {}", workflowName);
        
        // 获取最新的已发布版本
        WorkflowDefinition definition = workflowDefinitionRepository.findByNameAndIsActiveTrue(workflowName)
                .orElseThrow(() -> new WorkflowException("未找到可执行的工作流: " + workflowName));
        
        WorkflowExecutionRequest request = new WorkflowExecutionRequest();
        request.setWorkflowDefinitionId(definition.getId());
        request.setInputData(inputData);
        
        return startWorkflowInstance(request);
    }
    
    /**
     * 获取工作流实例详情
     */
    @Transactional(readOnly = true)
    public WorkflowInstanceDTO getWorkflowInstance(String instanceId) {
        logger.debug("获取工作流实例: {}", instanceId);
        
        WorkflowInstance instance = workflowInstanceRepository.findById(instanceId)
                .orElseThrow(() -> new WorkflowException("工作流实例不存在: " + instanceId));
        
        return convertToDTO(instance);
    }
    
    /**
     * 暂停工作流实例
     */
    public WorkflowInstanceDTO suspendWorkflowInstance(String instanceId) {
        logger.info("暂停工作流实例: {}", instanceId);
        
        WorkflowInstance instance = workflowInstanceRepository.findById(instanceId)
                .orElseThrow(() -> new WorkflowException("工作流实例不存在: " + instanceId));
        
        if (!instance.getStatus().canSuspend()) {
            throw new WorkflowException("工作流实例当前状态不允许暂停: " + instance.getStatus());
        }
        
        workflowEngine.suspendInstance(instanceId);
        
        // 重新获取更新后的实例
        instance = workflowInstanceRepository.findById(instanceId).orElse(instance);
        
        logger.info("工作流实例暂停成功: {}", instanceId);
        return convertToDTO(instance);
    }
    
    /**
     * 恢复工作流实例
     */
    public WorkflowInstanceDTO resumeWorkflowInstance(String instanceId) {
        logger.info("恢复工作流实例: {}", instanceId);
        
        WorkflowInstance instance = workflowInstanceRepository.findById(instanceId)
                .orElseThrow(() -> new WorkflowException("工作流实例不存在: " + instanceId));
        
        if (!instance.getStatus().canResume()) {
            throw new WorkflowException("工作流实例当前状态不允许恢复: " + instance.getStatus());
        }
        
        workflowEngine.resumeInstance(instanceId);
        
        // 重新获取更新后的实例
        instance = workflowInstanceRepository.findById(instanceId).orElse(instance);
        
        logger.info("工作流实例恢复成功: {}", instanceId);
        return convertToDTO(instance);
    }
    
    /**
     * 取消工作流实例
     */
    public WorkflowInstanceDTO cancelWorkflowInstance(String instanceId) {
        logger.info("取消工作流实例: {}", instanceId);
        
        WorkflowInstance instance = workflowInstanceRepository.findById(instanceId)
                .orElseThrow(() -> new WorkflowException("工作流实例不存在: " + instanceId));
        
        if (!instance.getStatus().canCancel()) {
            throw new WorkflowException("工作流实例当前状态不允许取消: " + instance.getStatus());
        }
        
        workflowEngine.cancelInstance(instanceId);
        
        // 重新获取更新后的实例
        instance = workflowInstanceRepository.findById(instanceId).orElse(instance);
        
        logger.info("工作流实例取消成功: {}", instanceId);
        return convertToDTO(instance);
    }
    
    /**
     * 重试失败的工作流实例
     */
    public WorkflowInstanceDTO retryWorkflowInstance(String instanceId) {
        logger.info("重试工作流实例: {}", instanceId);
        
        WorkflowInstance instance = workflowInstanceRepository.findById(instanceId)
                .orElseThrow(() -> new WorkflowException("工作流实例不存在: " + instanceId));
        
        if (instance.getStatus() != InstanceStatus.FAILED) {
            throw new WorkflowException("只有失败的工作流实例才能重试");
        }
        
        if (instance.getRetryCount() >= instance.getMaxRetryCount()) {
            throw new WorkflowException("已达到最大重试次数");
        }
        
        // 重置实例状态并重新启动
        instance.setStatus(InstanceStatus.PENDING);
        instance.setErrorMessage(null);
        instance.setEndTime(null);
        instance.setDurationMs(null);
        instance.incrementRetryCount();
        instance = workflowInstanceRepository.save(instance);
        
        // 重新启动执行
        try {
            Map<String, Object> inputData = new HashMap<>();
            if (instance.getInputData() != null) {
                inputData = objectMapper.readValue(instance.getInputData(), Map.class);
            }
            
            workflowEngine.startWorkflow(instance.getWorkflowDefinitionId(), inputData, getCurrentUserId());
            
        } catch (Exception e) {
            logger.error("重试工作流实例失败", e);
            throw new WorkflowException("重试工作流实例失败: " + e.getMessage(), e);
        }
        
        logger.info("工作流实例重试成功: {}", instanceId);
        return convertToDTO(instance);
    }
    
    /**
     * 分页查询工作流实例
     */
    @Transactional(readOnly = true)
    public PageResult<WorkflowInstanceDTO> getWorkflowInstances(Pageable pageable, String workflowDefinitionId,
                                                               InstanceStatus status, String createdBy, 
                                                               String businessKey, String keyword) {
        logger.debug("分页查询工作流实例");
        
        Specification<WorkflowInstance> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            if (workflowDefinitionId != null && !workflowDefinitionId.trim().isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("workflowDefinitionId"), workflowDefinitionId));
            }
            
            if (status != null) {
                predicates.add(criteriaBuilder.equal(root.get("status"), status));
            }
            
            if (createdBy != null && !createdBy.trim().isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("createdBy"), createdBy));
            }
            
            if (businessKey != null && !businessKey.trim().isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("businessKey"), businessKey));
            }
            
            if (keyword != null && !keyword.trim().isEmpty()) {
                String keywordPattern = "%" + keyword.toLowerCase() + "%";
                Predicate namePredicate = criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("instanceName")), keywordPattern
                );
                Predicate businessKeyPredicate = criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("businessKey")), keywordPattern
                );
                predicates.add(criteriaBuilder.or(namePredicate, businessKeyPredicate));
            }
            
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        
        Page<WorkflowInstance> page = workflowInstanceRepository.findAll(spec, pageable);
        List<WorkflowInstanceDTO> dtos = page.getContent().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        
        return new PageResult<>(dtos, page.getNumber(), page.getSize(), page.getTotalElements());
    }
    
    /**
     * 获取工作流实例的节点执行记录
     */
    @Transactional(readOnly = true)
    public List<NodeExecutionDTO> getNodeExecutions(String instanceId) {
        logger.debug("获取节点执行记录: {}", instanceId);
        
        List<NodeExecution> executions = nodeExecutionRepository.findByInstanceId(instanceId);
        return executions.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取工作流实例的执行上下文
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getExecutionContext(String instanceId) {
        logger.debug("获取执行上下文: {}", instanceId);
        
        ExecutionContext context = workflowEngine.getExecutionContext(instanceId);
        if (context != null) {
            ExecutionContext.ExecutionSummary summary = context.getExecutionSummary();
            Map<String, Object> result = new HashMap<>();
            result.put("instanceId", summary.getInstanceId());
            result.put("workflowName", summary.getWorkflowName());
            result.put("status", summary.getStatus());
            result.put("currentNodeId", summary.getCurrentNodeId());
            result.put("currentNodeName", summary.getCurrentNodeName());
            result.put("completedNodes", summary.getCompletedNodes());
            result.put("failedNodes", summary.getFailedNodes());
            result.put("totalNodes", summary.getTotalNodes());
            result.put("progressPercentage", summary.getProgressPercentage());
            result.put("executionDuration", summary.getExecutionDuration());
            result.put("startTime", summary.getStartTime());
            result.put("endTime", summary.getEndTime());
            result.put("suspended", summary.isSuspended());
            result.put("cancelled", summary.isCancelled());
            return result;
        }
        
        // 如果没有运行时上下文，从数据库获取基本信息
        WorkflowInstance instance = workflowInstanceRepository.findById(instanceId)
                .orElseThrow(() -> new WorkflowException("工作流实例不存在: " + instanceId));
        
        Map<String, Object> result = new HashMap<>();
        result.put("instanceId", instance.getId());
        result.put("status", instance.getStatus());
        result.put("currentNodeId", instance.getCurrentNodeId());
        result.put("currentNodeName", instance.getCurrentNodeName());
        result.put("startTime", instance.getStartTime());
        result.put("endTime", instance.getEndTime());
        result.put("duration", instance.getDurationMs());
        
        return result;
    }
    
    /**
     * 获取工作流实例的变量
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getInstanceVariables(String instanceId) {
        logger.debug("获取实例变量: {}", instanceId);
        
        WorkflowInstance instance = workflowInstanceRepository.findById(instanceId)
                .orElseThrow(() -> new WorkflowException("工作流实例不存在: " + instanceId));
        
        if (instance.getVariables() != null) {
            try {
                @SuppressWarnings("unchecked")
                Map<String, Object> variables = objectMapper.readValue(instance.getVariables(), Map.class);
                return variables;
            } catch (Exception e) {
                logger.warn("解析实例变量失败: {}", instanceId, e);
            }
        }
        
        return new HashMap<>();
    }
    
    /**
     * 更新工作流实例的变量
     */
    public void updateInstanceVariables(String instanceId, Map<String, Object> variables) {
        logger.info("更新实例变量: {}", instanceId);
        
        WorkflowInstance instance = workflowInstanceRepository.findById(instanceId)
                .orElseThrow(() -> new WorkflowException("工作流实例不存在: " + instanceId));
        
        try {
            String variablesJson = objectMapper.writeValueAsString(variables);
            instance.setVariables(variablesJson);
            workflowInstanceRepository.save(instance);
            
            // 如果实例正在运行，也更新运行时上下文
            ExecutionContext context = workflowEngine.getExecutionContext(instanceId);
            if (context != null) {
                context.setVariables(variables);
            }
            
            logger.info("实例变量更新成功: {}", instanceId);
            
        } catch (Exception e) {
            logger.error("更新实例变量失败: {}", instanceId, e);
            throw new WorkflowException("更新实例变量失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取运行中的工作流实例
     */
    @Transactional(readOnly = true)
    public PageResult<WorkflowInstanceDTO> getActiveInstances(Pageable pageable) {
        logger.debug("获取运行中的实例");
        
        Page<WorkflowInstance> page = workflowInstanceRepository.findActiveInstances(pageable);
        List<WorkflowInstanceDTO> dtos = page.getContent().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        
        return new PageResult<>(dtos, page.getNumber(), page.getSize(), page.getTotalElements());
    }
    
    /**
     * 获取已完成的工作流实例
     */
    @Transactional(readOnly = true)
    public PageResult<WorkflowInstanceDTO> getCompletedInstances(Pageable pageable) {
        logger.debug("获取已完成的实例");
        
        Page<WorkflowInstance> page = workflowInstanceRepository.findCompletedInstances(pageable);
        List<WorkflowInstanceDTO> dtos = page.getContent().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        
        return new PageResult<>(dtos, page.getNumber(), page.getSize(), page.getTotalElements());
    }
    
    /**
     * 删除工作流实例
     */
    public void deleteWorkflowInstance(String instanceId) {
        logger.info("删除工作流实例: {}", instanceId);
        
        WorkflowInstance instance = workflowInstanceRepository.findById(instanceId)
                .orElseThrow(() -> new WorkflowException("工作流实例不存在: " + instanceId));
        
        if (instance.getStatus().isActive()) {
            throw new WorkflowException("无法删除运行中的工作流实例");
        }
        
        // 删除相关的节点执行记录
        nodeExecutionRepository.deleteAll(nodeExecutionRepository.findByInstanceId(instanceId));
        
        // 删除实例
        workflowInstanceRepository.delete(instance);
        
        logger.info("工作流实例删除成功: {}", instanceId);
    }
    
    // 辅助方法
    
    private WorkflowInstanceDTO convertToDTO(WorkflowInstance instance) {
        WorkflowInstanceDTO dto = new WorkflowInstanceDTO();
        dto.setId(instance.getId());
        dto.setWorkflowDefinitionId(instance.getWorkflowDefinitionId());
        dto.setInstanceName(instance.getInstanceName());
        dto.setBusinessKey(instance.getBusinessKey());
        dto.setStatus(instance.getStatus());
        dto.setCurrentNodeId(instance.getCurrentNodeId());
        dto.setCurrentNodeName(instance.getCurrentNodeName());
        dto.setPriority(instance.getPriority());
        dto.setTenantId(instance.getTenantId());
        dto.setParentInstanceId(instance.getParentInstanceId());
        dto.setRootInstanceId(instance.getRootInstanceId());
        dto.setRetryCount(instance.getRetryCount());
        dto.setMaxRetryCount(instance.getMaxRetryCount());
        dto.setErrorMessage(instance.getErrorMessage());
        dto.setCreatedBy(instance.getCreatedBy());
        dto.setCreatedAt(instance.getCreatedAt());
        dto.setUpdatedAt(instance.getUpdatedAt());
        dto.setStartTime(instance.getStartTime());
        dto.setEndTime(instance.getEndTime());
        dto.setDurationMs(instance.getDurationMs());
        
        // 解析输入数据
        if (instance.getInputData() != null) {
            try {
                @SuppressWarnings("unchecked")
                Map<String, Object> inputData = objectMapper.readValue(instance.getInputData(), Map.class);
                dto.setInputData(inputData);
            } catch (Exception e) {
                logger.warn("解析输入数据失败: {}", instance.getId(), e);
            }
        }
        
        // 解析输出数据
        if (instance.getOutputData() != null) {
            try {
                @SuppressWarnings("unchecked")
                Map<String, Object> outputData = objectMapper.readValue(instance.getOutputData(), Map.class);
                dto.setOutputData(outputData);
            } catch (Exception e) {
                logger.warn("解析输出数据失败: {}", instance.getId(), e);
            }
        }
        
        // 解析变量
        if (instance.getVariables() != null) {
            try {
                @SuppressWarnings("unchecked")
                Map<String, Object> variables = objectMapper.readValue(instance.getVariables(), Map.class);
                dto.setVariables(variables);
            } catch (Exception e) {
                logger.warn("解析变量失败: {}", instance.getId(), e);
            }
        }
        
        return dto;
    }
    
    private NodeExecutionDTO convertToDTO(NodeExecution execution) {
        NodeExecutionDTO dto = new NodeExecutionDTO();
        dto.setId(execution.getId());
        dto.setInstanceId(execution.getInstanceId());
        dto.setNodeId(execution.getNodeId());
        dto.setNodeName(execution.getNodeName());
        dto.setNodeType(execution.getNodeType());
        dto.setStatus(execution.getStatus());
        dto.setExecutorId(execution.getExecutorId());
        dto.setExecutorName(execution.getExecutorName());
        dto.setPriority(execution.getPriority());
        dto.setRetryCount(execution.getRetryCount());
        dto.setMaxRetryCount(execution.getMaxRetryCount());
        dto.setTimeoutMs(execution.getTimeoutMs());
        dto.setErrorMessage(execution.getErrorMessage());
        dto.setErrorStackTrace(execution.getErrorStackTrace());
        dto.setCreatedAt(execution.getCreatedAt());
        dto.setStartTime(execution.getStartTime());
        dto.setEndTime(execution.getEndTime());
        dto.setDurationMs(execution.getDurationMs());
        
        // 解析输入数据
        if (execution.getInputData() != null) {
            try {
                @SuppressWarnings("unchecked")
                Map<String, Object> inputData = objectMapper.readValue(execution.getInputData(), Map.class);
                dto.setInputData(inputData);
            } catch (Exception e) {
                logger.warn("解析节点输入数据失败: {}", execution.getId(), e);
            }
        }
        
        // 解析输出数据
        if (execution.getOutputData() != null) {
            try {
                @SuppressWarnings("unchecked")
                Map<String, Object> outputData = objectMapper.readValue(execution.getOutputData(), Map.class);
                dto.setOutputData(outputData);
            } catch (Exception e) {
                logger.warn("解析节点输出数据失败: {}", execution.getId(), e);
            }
        }
        
        return dto;
    }
    
    private String getCurrentUserId() {
        try {
            return SecurityContextHolder.getContext().getAuthentication().getName();
        } catch (Exception e) {
            return "system";
        }
    }
    
    // 其他业务方法的实现...
    
    @Transactional(readOnly = true)
    public Map<String, Object> getInstanceStatistics() {
        // 实现统计逻辑
        return new HashMap<>();
    }
    
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getExecutionLogs(String instanceId, String level, Integer limit) {
        // 实现日志查询逻辑
        return new ArrayList<>();
    }
    
    public Map<String, Object> batchOperateInstances(String action, List<String> instanceIds) {
        // 实现批量操作逻辑
        return new HashMap<>();
    }
    
    public Map<String, Object> cleanupHistoryInstances(Integer retentionDays) {
        // 实现历史清理逻辑
        return new HashMap<>();
    }
}
