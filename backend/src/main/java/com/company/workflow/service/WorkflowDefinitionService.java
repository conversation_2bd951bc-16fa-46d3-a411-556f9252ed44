package com.company.workflow.service;

import com.company.workflow.dsl.WorkflowDslParser;
import com.company.workflow.dsl.ValidationEngine;
import com.company.workflow.dsl.model.WorkflowAST;
import com.company.workflow.dsl.model.ASTModels.ValidationResult;
import com.company.workflow.dto.*;
import com.company.workflow.exception.WorkflowException;
import com.company.workflow.model.entity.WorkflowDefinition;
import com.company.workflow.model.entity.WorkflowStatus;
import com.company.workflow.repository.WorkflowDefinitionRepository;
import com.company.workflow.repository.WorkflowInstanceRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 工作流定义服务
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
@Transactional
public class WorkflowDefinitionService {
    
    private static final Logger logger = LoggerFactory.getLogger(WorkflowDefinitionService.class);
    
    @Autowired
    private WorkflowDefinitionRepository workflowDefinitionRepository;
    
    @Autowired
    private WorkflowInstanceRepository workflowInstanceRepository;
    
    @Autowired
    private WorkflowDslParser dslParser;
    
    @Autowired
    private ValidationEngine validationEngine;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 创建工作流定义
     */
    public WorkflowDefinitionDTO createWorkflowDefinition(WorkflowDefinitionCreateRequest request) {
        logger.info("创建工作流定义: {}", request.getName());
        
        try {
            // 1. 验证DSL
            ValidationResult validationResult = validateDsl(request.getDslContent());
            if (!validationResult.isValid()) {
                throw new WorkflowException("DSL验证失败: " + validationResult.getErrors());
            }
            
            // 2. 解析DSL获取工作流信息
            WorkflowAST workflowAST = dslParser.parse(request.getDslContent());
            
            // 3. 检查名称和版本是否已存在
            if (workflowDefinitionRepository.existsByNameAndVersion(request.getName(), request.getVersion())) {
                throw new WorkflowException("工作流定义已存在: " + request.getName() + " v" + request.getVersion());
            }
            
            // 4. 创建工作流定义实体
            WorkflowDefinition definition = new WorkflowDefinition();
            definition.setId(generateDefinitionId());
            definition.setName(request.getName());
            definition.setVersion(request.getVersion());
            definition.setDescription(request.getDescription());
            definition.setCategory(request.getCategory());
            definition.setDslContent(request.getDslContent());
            definition.setStatus(WorkflowStatus.DRAFT);
            definition.setCreatedBy(getCurrentUserId());
            definition.setTags(request.getTags());
            
            // 5. 设置从DSL解析的信息
            if (workflowAST.getVariables() != null) {
                definition.setVariables(objectMapper.writeValueAsString(workflowAST.getVariables()));
            }
            
            // 6. 保存到数据库
            definition = workflowDefinitionRepository.save(definition);
            
            logger.info("工作流定义创建成功: {} ({})", definition.getName(), definition.getId());
            return convertToDTO(definition);
            
        } catch (Exception e) {
            logger.error("创建工作流定义失败: {}", request.getName(), e);
            throw new WorkflowException("创建工作流定义失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取工作流定义
     */
    @Transactional(readOnly = true)
    public WorkflowDefinitionDTO getWorkflowDefinition(String id) {
        logger.debug("获取工作流定义: {}", id);
        
        WorkflowDefinition definition = workflowDefinitionRepository.findById(id)
                .orElseThrow(() -> new WorkflowException("工作流定义不存在: " + id));
        
        return convertToDTO(definition);
    }
    
    /**
     * 更新工作流定义
     */
    public WorkflowDefinitionDTO updateWorkflowDefinition(String id, WorkflowDefinitionUpdateRequest request) {
        logger.info("更新工作流定义: {}", id);
        
        try {
            WorkflowDefinition definition = workflowDefinitionRepository.findById(id)
                    .orElseThrow(() -> new WorkflowException("工作流定义不存在: " + id));
            
            // 检查是否可以更新
            if (definition.getStatus() == WorkflowStatus.PUBLISHED && definition.isActive()) {
                throw new WorkflowException("已发布的工作流定义不能直接更新，请创建新版本");
            }
            
            // 验证DSL（如果提供了新的DSL）
            if (request.getDslContent() != null && !request.getDslContent().equals(definition.getDslContent())) {
                ValidationResult validationResult = validateDsl(request.getDslContent());
                if (!validationResult.isValid()) {
                    throw new WorkflowException("DSL验证失败: " + validationResult.getErrors());
                }
                definition.setDslContent(request.getDslContent());
                
                // 更新从DSL解析的信息
                WorkflowAST workflowAST = dslParser.parse(request.getDslContent());
                if (workflowAST.getVariables() != null) {
                    definition.setVariables(objectMapper.writeValueAsString(workflowAST.getVariables()));
                }
            }
            
            // 更新其他字段
            if (request.getDescription() != null) {
                definition.setDescription(request.getDescription());
            }
            if (request.getCategory() != null) {
                definition.setCategory(request.getCategory());
            }
            if (request.getTags() != null) {
                definition.setTags(request.getTags());
            }
            
            definition.setUpdatedBy(getCurrentUserId());
            definition = workflowDefinitionRepository.save(definition);
            
            logger.info("工作流定义更新成功: {}", id);
            return convertToDTO(definition);
            
        } catch (Exception e) {
            logger.error("更新工作流定义失败: {}", id, e);
            throw new WorkflowException("更新工作流定义失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 删除工作流定义
     */
    public void deleteWorkflowDefinition(String id) {
        logger.info("删除工作流定义: {}", id);
        
        WorkflowDefinition definition = workflowDefinitionRepository.findById(id)
                .orElseThrow(() -> new WorkflowException("工作流定义不存在: " + id));
        
        // 检查是否有运行中的实例
        long runningInstances = workflowInstanceRepository.countByWorkflowDefinitionId(id);
        if (runningInstances > 0) {
            throw new WorkflowException("存在运行中的实例，无法删除工作流定义");
        }
        
        workflowDefinitionRepository.delete(definition);
        logger.info("工作流定义删除成功: {}", id);
    }
    
    /**
     * 分页查询工作流定义
     */
    @Transactional(readOnly = true)
    public PageResult<WorkflowDefinitionDTO> getWorkflowDefinitions(Pageable pageable, String name, 
                                                                   WorkflowStatus status, String category, 
                                                                   String createdBy, String keyword) {
        logger.debug("分页查询工作流定义");
        
        Specification<WorkflowDefinition> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            if (name != null && !name.trim().isEmpty()) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("name")), 
                    "%" + name.toLowerCase() + "%"
                ));
            }
            
            if (status != null) {
                predicates.add(criteriaBuilder.equal(root.get("status"), status));
            }
            
            if (category != null && !category.trim().isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("category"), category));
            }
            
            if (createdBy != null && !createdBy.trim().isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("createdBy"), createdBy));
            }
            
            if (keyword != null && !keyword.trim().isEmpty()) {
                String keywordPattern = "%" + keyword.toLowerCase() + "%";
                Predicate namePredicate = criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("name")), keywordPattern
                );
                Predicate descPredicate = criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("description")), keywordPattern
                );
                predicates.add(criteriaBuilder.or(namePredicate, descPredicate));
            }
            
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        
        Page<WorkflowDefinition> page = workflowDefinitionRepository.findAll(spec, pageable);
        List<WorkflowDefinitionDTO> dtos = page.getContent().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        
        return new PageResult<>(dtos, page.getNumber(), page.getSize(), page.getTotalElements());
    }
    
    /**
     * 发布工作流定义
     */
    public WorkflowDefinitionDTO publishWorkflowDefinition(String id) {
        logger.info("发布工作流定义: {}", id);
        
        WorkflowDefinition definition = workflowDefinitionRepository.findById(id)
                .orElseThrow(() -> new WorkflowException("工作流定义不存在: " + id));
        
        // 验证DSL
        ValidationResult validationResult = validateDsl(definition.getDslContent());
        if (!validationResult.isValid()) {
            throw new WorkflowException("DSL验证失败，无法发布: " + validationResult.getErrors());
        }
        
        // 停用同名的其他版本
        workflowDefinitionRepository.deactivateOtherVersions(definition.getName(), id);
        
        // 发布当前版本
        definition.setStatus(WorkflowStatus.PUBLISHED);
        definition.setActive(true);
        definition.setPublishedAt(LocalDateTime.now());
        definition.setPublishedBy(getCurrentUserId());
        definition.setUpdatedBy(getCurrentUserId());
        
        definition = workflowDefinitionRepository.save(definition);
        
        logger.info("工作流定义发布成功: {}", id);
        return convertToDTO(definition);
    }
    
    /**
     * 停用工作流定义
     */
    public WorkflowDefinitionDTO deactivateWorkflowDefinition(String id) {
        logger.info("停用工作流定义: {}", id);
        
        WorkflowDefinition definition = workflowDefinitionRepository.findById(id)
                .orElseThrow(() -> new WorkflowException("工作流定义不存在: " + id));
        
        definition.setActive(false);
        definition.setUpdatedBy(getCurrentUserId());
        definition = workflowDefinitionRepository.save(definition);
        
        logger.info("工作流定义停用成功: {}", id);
        return convertToDTO(definition);
    }
    
    /**
     * 弃用工作流定义
     */
    public WorkflowDefinitionDTO deprecateWorkflowDefinition(String id) {
        logger.info("弃用工作流定义: {}", id);
        
        WorkflowDefinition definition = workflowDefinitionRepository.findById(id)
                .orElseThrow(() -> new WorkflowException("工作流定义不存在: " + id));
        
        definition.setStatus(WorkflowStatus.DEPRECATED);
        definition.setActive(false);
        definition.setUpdatedBy(getCurrentUserId());
        definition = workflowDefinitionRepository.save(definition);
        
        logger.info("工作流定义弃用成功: {}", id);
        return convertToDTO(definition);
    }
    
    /**
     * 验证工作流DSL
     */
    @Transactional(readOnly = true)
    public Map<String, Object> validateWorkflowDsl(String dslContent) {
        logger.debug("验证工作流DSL");
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            ValidationResult validationResult = validateDsl(dslContent);
            result.put("valid", validationResult.isValid());
            result.put("errors", validationResult.getErrors());
            result.put("warnings", validationResult.getWarnings());
            
            if (validationResult.isValid()) {
                // 如果验证通过，返回解析的工作流信息
                WorkflowAST workflowAST = dslParser.parse(dslContent);
                result.put("workflowInfo", Map.of(
                    "name", workflowAST.getName(),
                    "version", workflowAST.getVersion(),
                    "description", workflowAST.getDescription(),
                    "nodeCount", workflowAST.getNodes().size(),
                    "variables", workflowAST.getVariables()
                ));
            }
            
        } catch (Exception e) {
            logger.error("DSL验证异常", e);
            result.put("valid", false);
            result.put("errors", List.of("DSL解析异常: " + e.getMessage()));
            result.put("warnings", List.of());
        }
        
        return result;
    }
    
    /**
     * 复制工作流定义
     */
    public WorkflowDefinitionDTO copyWorkflowDefinition(String id, String newName, String newVersion) {
        logger.info("复制工作流定义: {} -> {} v{}", id, newName, newVersion);
        
        WorkflowDefinition original = workflowDefinitionRepository.findById(id)
                .orElseThrow(() -> new WorkflowException("工作流定义不存在: " + id));
        
        // 检查新名称和版本是否已存在
        if (workflowDefinitionRepository.existsByNameAndVersion(newName, newVersion)) {
            throw new WorkflowException("工作流定义已存在: " + newName + " v" + newVersion);
        }
        
        // 创建副本
        WorkflowDefinition copy = new WorkflowDefinition();
        copy.setId(generateDefinitionId());
        copy.setName(newName);
        copy.setVersion(newVersion);
        copy.setDescription("复制自: " + original.getName() + " v" + original.getVersion());
        copy.setCategory(original.getCategory());
        copy.setDslContent(original.getDslContent());
        copy.setVariables(original.getVariables());
        copy.setTags(original.getTags());
        copy.setStatus(WorkflowStatus.DRAFT);
        copy.setCreatedBy(getCurrentUserId());
        
        copy = workflowDefinitionRepository.save(copy);
        
        logger.info("工作流定义复制成功: {}", copy.getId());
        return convertToDTO(copy);
    }
    
    /**
     * 获取工作流定义的所有版本
     */
    @Transactional(readOnly = true)
    public List<WorkflowDefinitionDTO> getWorkflowVersions(String name) {
        logger.debug("获取工作流版本列表: {}", name);
        
        List<WorkflowDefinition> versions = workflowDefinitionRepository.findByNameOrderByCreatedAtDesc(name);
        return versions.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取工作流定义的最新版本
     */
    @Transactional(readOnly = true)
    public WorkflowDefinitionDTO getLatestWorkflowVersion(String name) {
        logger.debug("获取最新版本: {}", name);
        
        WorkflowDefinition latest = workflowDefinitionRepository.findLatestVersionByName(name)
                .orElseThrow(() -> new WorkflowException("工作流定义不存在: " + name));
        
        return convertToDTO(latest);
    }
    
    // 辅助方法
    
    private ValidationResult validateDsl(String dslContent) {
        try {
            WorkflowAST workflowAST = dslParser.parse(dslContent);
            return validationEngine.validate(workflowAST);
        } catch (Exception e) {
            ValidationResult result = new ValidationResult();
            result.addError("DSL解析失败: " + e.getMessage());
            return result;
        }
    }
    
    private WorkflowDefinitionDTO convertToDTO(WorkflowDefinition definition) {
        WorkflowDefinitionDTO dto = new WorkflowDefinitionDTO();
        dto.setId(definition.getId());
        dto.setName(definition.getName());
        dto.setVersion(definition.getVersion());
        dto.setDescription(definition.getDescription());
        dto.setCategory(definition.getCategory());
        dto.setDslContent(definition.getDslContent());
        dto.setStatus(definition.getStatus());
        dto.setActive(definition.isActive());
        dto.setTags(definition.getTags());
        dto.setExecutionCount(definition.getExecutionCount());
        dto.setSuccessCount(definition.getSuccessCount());
        dto.setFailureCount(definition.getFailureCount());
        dto.setAvgExecutionTime(definition.getAvgExecutionTime());
        dto.setCreatedBy(definition.getCreatedBy());
        dto.setCreatedAt(definition.getCreatedAt());
        dto.setUpdatedBy(definition.getUpdatedBy());
        dto.setUpdatedAt(definition.getUpdatedAt());
        dto.setPublishedBy(definition.getPublishedBy());
        dto.setPublishedAt(definition.getPublishedAt());
        
        // 解析变量
        if (definition.getVariables() != null) {
            try {
                @SuppressWarnings("unchecked")
                Map<String, Object> variables = objectMapper.readValue(definition.getVariables(), Map.class);
                dto.setVariables(variables);
            } catch (Exception e) {
                logger.warn("解析工作流变量失败: {}", definition.getId(), e);
            }
        }
        
        return dto;
    }
    
    private String generateDefinitionId() {
        return "wf_" + UUID.randomUUID().toString().replace("-", "");
    }
    
    private String getCurrentUserId() {
        // 从Spring Security上下文获取当前用户ID
        try {
            return SecurityContextHolder.getContext().getAuthentication().getName();
        } catch (Exception e) {
            return "system"; // 默认用户
        }
    }
    
    // 其他业务方法的实现...
    
    @Transactional(readOnly = true)
    public Map<String, Object> getWorkflowStatistics() {
        // 实现统计逻辑
        return new HashMap<>();
    }
    
    @Transactional(readOnly = true)
    public List<WorkflowDefinitionDTO> getPopularWorkflows(Integer limit) {
        // 实现热门工作流查询
        return new ArrayList<>();
    }
    
    @Transactional(readOnly = true)
    public List<WorkflowDefinitionDTO> getRecentWorkflows(Integer limit) {
        // 实现最近工作流查询
        return new ArrayList<>();
    }
    
    @Transactional(readOnly = true)
    public PageResult<WorkflowDefinitionDTO> searchWorkflowDefinitions(String keyword, Pageable pageable) {
        // 实现搜索逻辑
        return PageResult.empty();
    }
    
    @Transactional(readOnly = true)
    public Map<String, Object> exportWorkflowDefinition(String id) {
        // 实现导出逻辑
        return new HashMap<>();
    }
    
    public WorkflowDefinitionDTO importWorkflowDefinition(Map<String, Object> workflowData) {
        // 实现导入逻辑
        throw new WorkflowException("导入功能暂未实现");
    }
}
