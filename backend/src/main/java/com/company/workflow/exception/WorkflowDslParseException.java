package com.company.workflow.exception;

/**
 * 工作流 DSL 解析异常
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class WorkflowDslParseException extends WorkflowException {
    
    private String dslContent;
    private int lineNumber;
    private int columnNumber;
    
    public WorkflowDslParseException(String message) {
        super(message);
    }
    
    public WorkflowDslParseException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public WorkflowDslParseException(String message, String dslContent) {
        super(message);
        this.dslContent = dslContent;
    }
    
    public WorkflowDslParseException(String message, String dslContent, int lineNumber, int columnNumber) {
        super(message);
        this.dslContent = dslContent;
        this.lineNumber = lineNumber;
        this.columnNumber = columnNumber;
    }
    
    public WorkflowDslParseException(String message, Throwable cause, String dslContent, int lineNumber, int columnNumber) {
        super(message, cause);
        this.dslContent = dslContent;
        this.lineNumber = lineNumber;
        this.columnNumber = columnNumber;
    }
    
    public String getDslContent() {
        return dslContent;
    }
    
    public void setDslContent(String dslContent) {
        this.dslContent = dslContent;
    }
    
    public int getLineNumber() {
        return lineNumber;
    }
    
    public void setLineNumber(int lineNumber) {
        this.lineNumber = lineNumber;
    }
    
    public int getColumnNumber() {
        return columnNumber;
    }
    
    public void setColumnNumber(int columnNumber) {
        this.columnNumber = columnNumber;
    }
    
    @Override
    public String getMessage() {
        StringBuilder sb = new StringBuilder(super.getMessage());
        
        if (lineNumber > 0) {
            sb.append(" (行: ").append(lineNumber);
            if (columnNumber > 0) {
                sb.append(", 列: ").append(columnNumber);
            }
            sb.append(")");
        }
        
        return sb.toString();
    }
}
