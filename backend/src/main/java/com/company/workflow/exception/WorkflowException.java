package com.company.workflow.exception;

/**
 * 工作流基础异常类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class WorkflowException extends RuntimeException {
    
    private String errorCode;
    private Object[] errorArgs;
    
    public WorkflowException(String message) {
        super(message);
    }
    
    public WorkflowException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public WorkflowException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public WorkflowException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public WorkflowException(String errorCode, String message, Object... errorArgs) {
        super(message);
        this.errorCode = errorCode;
        this.errorArgs = errorArgs;
    }
    
    public WorkflowException(String errorCode, String message, Throwable cause, Object... errorArgs) {
        super(message, cause);
        this.errorCode = errorCode;
        this.errorArgs = errorArgs;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }
    
    public Object[] getErrorArgs() {
        return errorArgs;
    }
    
    public void setErrorArgs(Object[] errorArgs) {
        this.errorArgs = errorArgs;
    }
}
