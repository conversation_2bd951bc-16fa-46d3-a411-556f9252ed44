package com.company.workflow.repository;

import com.company.workflow.model.entity.WorkflowInstance;
import com.company.workflow.model.entity.InstanceStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 工作流实例数据访问接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Repository
public interface WorkflowInstanceRepository extends JpaRepository<WorkflowInstance, String>, 
                                                   JpaSpecificationExecutor<WorkflowInstance> {

    /**
     * 根据工作流定义ID查找实例
     */
    List<WorkflowInstance> findByWorkflowDefinitionId(String workflowDefinitionId);

    /**
     * 根据工作流定义ID分页查找实例
     */
    Page<WorkflowInstance> findByWorkflowDefinitionId(String workflowDefinitionId, Pageable pageable);

    /**
     * 根据状态查找实例
     */
    List<WorkflowInstance> findByStatus(InstanceStatus status);

    /**
     * 根据状态分页查找实例
     */
    Page<WorkflowInstance> findByStatus(InstanceStatus status, Pageable pageable);

    /**
     * 根据创建者查找实例
     */
    List<WorkflowInstance> findByCreatedBy(String createdBy);

    /**
     * 根据创建者分页查找实例
     */
    Page<WorkflowInstance> findByCreatedBy(String createdBy, Pageable pageable);

    /**
     * 根据业务键查找实例
     */
    Optional<WorkflowInstance> findByBusinessKey(String businessKey);

    /**
     * 根据父实例ID查找子实例
     */
    List<WorkflowInstance> findByParentInstanceId(String parentInstanceId);

    /**
     * 根据根实例ID查找所有相关实例
     */
    List<WorkflowInstance> findByRootInstanceId(String rootInstanceId);

    /**
     * 根据租户ID查找实例
     */
    List<WorkflowInstance> findByTenantId(String tenantId);

    /**
     * 根据租户ID分页查找实例
     */
    Page<WorkflowInstance> findByTenantId(String tenantId, Pageable pageable);

    /**
     * 根据当前节点ID查找实例
     */
    List<WorkflowInstance> findByCurrentNodeId(String currentNodeId);

    /**
     * 根据优先级查找实例
     */
    List<WorkflowInstance> findByPriorityGreaterThanEqualOrderByPriorityDesc(Integer priority);

    /**
     * 根据创建时间范围查找实例
     */
    List<WorkflowInstance> findByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据开始时间范围查找实例
     */
    List<WorkflowInstance> findByStartTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据结束时间范围查找实例
     */
    List<WorkflowInstance> findByEndTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计指定状态的实例数量
     */
    long countByStatus(InstanceStatus status);

    /**
     * 统计指定工作流定义的实例数量
     */
    long countByWorkflowDefinitionId(String workflowDefinitionId);

    /**
     * 统计指定创建者的实例数量
     */
    long countByCreatedBy(String createdBy);

    /**
     * 统计指定租户的实例数量
     */
    long countByTenantId(String tenantId);

    /**
     * 检查业务键是否存在
     */
    boolean existsByBusinessKey(String businessKey);

    /**
     * 查找运行中的实例
     */
    @Query("SELECT w FROM WorkflowInstance w WHERE w.status IN ('PENDING', 'RUNNING', 'SUSPENDED')")
    List<WorkflowInstance> findActiveInstances();

    /**
     * 查找运行中的实例（分页）
     */
    @Query("SELECT w FROM WorkflowInstance w WHERE w.status IN ('PENDING', 'RUNNING', 'SUSPENDED')")
    Page<WorkflowInstance> findActiveInstances(Pageable pageable);

    /**
     * 查找已完成的实例
     */
    @Query("SELECT w FROM WorkflowInstance w WHERE w.status IN ('COMPLETED', 'FAILED', 'CANCELLED')")
    List<WorkflowInstance> findCompletedInstances();

    /**
     * 查找已完成的实例（分页）
     */
    @Query("SELECT w FROM WorkflowInstance w WHERE w.status IN ('COMPLETED', 'FAILED', 'CANCELLED')")
    Page<WorkflowInstance> findCompletedInstances(Pageable pageable);

    /**
     * 查找超时的实例
     */
    @Query("SELECT w FROM WorkflowInstance w WHERE w.status = 'RUNNING' AND w.startTime < :timeoutBefore")
    List<WorkflowInstance> findTimeoutInstances(@Param("timeoutBefore") LocalDateTime timeoutBefore);

    /**
     * 查找需要重试的失败实例
     */
    @Query("SELECT w FROM WorkflowInstance w WHERE w.status = 'FAILED' AND w.retryCount < w.maxRetryCount")
    List<WorkflowInstance> findRetryableInstances();

    /**
     * 查找长时间运行的实例
     */
    @Query("SELECT w FROM WorkflowInstance w WHERE w.status = 'RUNNING' AND w.startTime < :longRunningBefore")
    List<WorkflowInstance> findLongRunningInstances(@Param("longRunningBefore") LocalDateTime longRunningBefore);

    /**
     * 根据工作流定义和状态统计实例数量
     */
    @Query("SELECT w.status, COUNT(*) FROM WorkflowInstance w WHERE w.workflowDefinitionId = :workflowDefinitionId GROUP BY w.status")
    List<Object[]> countByWorkflowDefinitionIdGroupByStatus(@Param("workflowDefinitionId") String workflowDefinitionId);

    /**
     * 查找执行时间最长的实例
     */
    @Query("SELECT w FROM WorkflowInstance w WHERE w.durationMs IS NOT NULL ORDER BY w.durationMs DESC")
    Page<WorkflowInstance> findLongestExecutionInstances(Pageable pageable);

    /**
     * 查找执行时间最短的实例
     */
    @Query("SELECT w FROM WorkflowInstance w WHERE w.durationMs IS NOT NULL ORDER BY w.durationMs ASC")
    Page<WorkflowInstance> findShortestExecutionInstances(Pageable pageable);

    /**
     * 计算平均执行时间
     */
    @Query("SELECT AVG(w.durationMs) FROM WorkflowInstance w WHERE w.workflowDefinitionId = :workflowDefinitionId AND w.status = 'COMPLETED'")
    Double calculateAvgExecutionTime(@Param("workflowDefinitionId") String workflowDefinitionId);

    /**
     * 计算成功率
     */
    @Query("SELECT " +
           "COUNT(CASE WHEN w.status = 'COMPLETED' THEN 1 END) * 1.0 / COUNT(*) " +
           "FROM WorkflowInstance w WHERE w.workflowDefinitionId = :workflowDefinitionId")
    Double calculateSuccessRate(@Param("workflowDefinitionId") String workflowDefinitionId);

    /**
     * 更新实例状态
     */
    @Modifying
    @Query("UPDATE WorkflowInstance w SET w.status = :status, w.updatedAt = CURRENT_TIMESTAMP WHERE w.id = :id")
    int updateStatus(@Param("id") String id, @Param("status") InstanceStatus status);

    /**
     * 更新当前节点信息
     */
    @Modifying
    @Query("UPDATE WorkflowInstance w SET w.currentNodeId = :nodeId, w.currentNodeName = :nodeName, w.updatedAt = CURRENT_TIMESTAMP WHERE w.id = :id")
    int updateCurrentNode(@Param("id") String id, @Param("nodeId") String nodeId, @Param("nodeName") String nodeName);

    /**
     * 更新实例变量
     */
    @Modifying
    @Query("UPDATE WorkflowInstance w SET w.variables = :variables, w.updatedAt = CURRENT_TIMESTAMP WHERE w.id = :id")
    int updateVariables(@Param("id") String id, @Param("variables") String variables);

    /**
     * 更新输出数据
     */
    @Modifying
    @Query("UPDATE WorkflowInstance w SET w.outputData = :outputData, w.updatedAt = CURRENT_TIMESTAMP WHERE w.id = :id")
    int updateOutputData(@Param("id") String id, @Param("outputData") String outputData);

    /**
     * 开始执行实例
     */
    @Modifying
    @Query("UPDATE WorkflowInstance w SET w.status = 'RUNNING', w.startTime = CURRENT_TIMESTAMP, w.updatedAt = CURRENT_TIMESTAMP WHERE w.id = :id")
    int startExecution(@Param("id") String id);

    /**
     * 完成实例执行
     */
    @Modifying
    @Query("UPDATE WorkflowInstance w SET w.status = 'COMPLETED', w.endTime = CURRENT_TIMESTAMP, w.durationMs = :durationMs, w.updatedAt = CURRENT_TIMESTAMP WHERE w.id = :id")
    int completeExecution(@Param("id") String id, @Param("durationMs") Long durationMs);

    /**
     * 失败实例执行
     */
    @Modifying
    @Query("UPDATE WorkflowInstance w SET w.status = 'FAILED', w.endTime = CURRENT_TIMESTAMP, w.durationMs = :durationMs, w.errorMessage = :errorMessage, w.updatedAt = CURRENT_TIMESTAMP WHERE w.id = :id")
    int failExecution(@Param("id") String id, @Param("durationMs") Long durationMs, @Param("errorMessage") String errorMessage);

    /**
     * 取消实例执行
     */
    @Modifying
    @Query("UPDATE WorkflowInstance w SET w.status = 'CANCELLED', w.endTime = CURRENT_TIMESTAMP, w.durationMs = :durationMs, w.updatedAt = CURRENT_TIMESTAMP WHERE w.id = :id")
    int cancelExecution(@Param("id") String id, @Param("durationMs") Long durationMs);

    /**
     * 暂停实例执行
     */
    @Modifying
    @Query("UPDATE WorkflowInstance w SET w.status = 'SUSPENDED', w.updatedAt = CURRENT_TIMESTAMP WHERE w.id = :id")
    int suspendExecution(@Param("id") String id);

    /**
     * 恢复实例执行
     */
    @Modifying
    @Query("UPDATE WorkflowInstance w SET w.status = 'RUNNING', w.updatedAt = CURRENT_TIMESTAMP WHERE w.id = :id")
    int resumeExecution(@Param("id") String id);

    /**
     * 增加重试次数
     */
    @Modifying
    @Query("UPDATE WorkflowInstance w SET w.retryCount = w.retryCount + 1, w.updatedAt = CURRENT_TIMESTAMP WHERE w.id = :id")
    int incrementRetryCount(@Param("id") String id);

    /**
     * 删除指定时间之前的已完成实例
     */
    @Modifying
    @Query("DELETE FROM WorkflowInstance w WHERE w.status IN ('COMPLETED', 'FAILED', 'CANCELLED') AND w.endTime < :beforeTime")
    int deleteCompletedInstancesBefore(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 查找需要清理的实例
     */
    @Query("SELECT w FROM WorkflowInstance w WHERE w.status IN ('COMPLETED', 'FAILED', 'CANCELLED') AND w.endTime < :beforeTime")
    List<WorkflowInstance> findInstancesToCleanup(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 全文搜索实例
     */
    @Query("SELECT w FROM WorkflowInstance w WHERE " +
           "LOWER(w.instanceName) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(w.businessKey) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(w.currentNodeName) LIKE LOWER(CONCAT('%', :keyword, '%'))")
    Page<WorkflowInstance> searchByKeyword(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 获取实例统计信息
     */
    @Query("SELECT " +
           "COUNT(*) as totalCount, " +
           "SUM(CASE WHEN w.status = 'PENDING' THEN 1 ELSE 0 END) as pendingCount, " +
           "SUM(CASE WHEN w.status = 'RUNNING' THEN 1 ELSE 0 END) as runningCount, " +
           "SUM(CASE WHEN w.status = 'COMPLETED' THEN 1 ELSE 0 END) as completedCount, " +
           "SUM(CASE WHEN w.status = 'FAILED' THEN 1 ELSE 0 END) as failedCount, " +
           "SUM(CASE WHEN w.status = 'CANCELLED' THEN 1 ELSE 0 END) as cancelledCount, " +
           "SUM(CASE WHEN w.status = 'SUSPENDED' THEN 1 ELSE 0 END) as suspendedCount, " +
           "AVG(w.durationMs) as avgDuration " +
           "FROM WorkflowInstance w")
    Object[] getInstanceStatistics();

    /**
     * 获取每日实例统计
     */
    @Query("SELECT DATE(w.createdAt) as date, COUNT(*) as count " +
           "FROM WorkflowInstance w " +
           "WHERE w.createdAt >= :startDate " +
           "GROUP BY DATE(w.createdAt) " +
           "ORDER BY DATE(w.createdAt)")
    List<Object[]> getDailyInstanceStatistics(@Param("startDate") LocalDateTime startDate);
}
