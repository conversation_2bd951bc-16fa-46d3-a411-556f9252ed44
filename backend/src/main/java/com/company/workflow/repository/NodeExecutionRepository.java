package com.company.workflow.repository;

import com.company.workflow.model.entity.NodeExecution;
import com.company.workflow.model.entity.NodeStatus;
import com.company.workflow.model.entity.NodeType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 节点执行数据访问接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Repository
public interface NodeExecutionRepository extends JpaRepository<NodeExecution, String>, 
                                                JpaSpecificationExecutor<NodeExecution> {

    /**
     * 根据实例ID查找节点执行记录
     */
    List<NodeExecution> findByInstanceId(String instanceId);

    /**
     * 根据实例ID分页查找节点执行记录
     */
    Page<NodeExecution> findByInstanceId(String instanceId, Pageable pageable);

    /**
     * 根据实例ID和节点ID查找执行记录
     */
    Optional<NodeExecution> findByInstanceIdAndNodeId(String instanceId, String nodeId);

    /**
     * 根据状态查找节点执行记录
     */
    List<NodeExecution> findByStatus(NodeStatus status);

    /**
     * 根据状态分页查找节点执行记录
     */
    Page<NodeExecution> findByStatus(NodeStatus status, Pageable pageable);

    /**
     * 根据节点类型查找执行记录
     */
    List<NodeExecution> findByNodeType(NodeType nodeType);

    /**
     * 根据节点类型分页查找执行记录
     */
    Page<NodeExecution> findByNodeType(NodeType nodeType, Pageable pageable);

    /**
     * 根据执行器ID查找执行记录
     */
    List<NodeExecution> findByExecutorId(String executorId);

    /**
     * 根据开始时间范围查找执行记录
     */
    List<NodeExecution> findByStartTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据结束时间范围查找执行记录
     */
    List<NodeExecution> findByEndTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计指定状态的执行记录数量
     */
    long countByStatus(NodeStatus status);

    /**
     * 统计指定实例的执行记录数量
     */
    long countByInstanceId(String instanceId);

    /**
     * 统计指定节点类型的执行记录数量
     */
    long countByNodeType(NodeType nodeType);

    /**
     * 查找运行中的节点执行记录
     */
    @Query("SELECT n FROM NodeExecution n WHERE n.status IN ('PENDING', 'RUNNING', 'WAITING')")
    List<NodeExecution> findActiveExecutions();

    /**
     * 查找运行中的节点执行记录（分页）
     */
    @Query("SELECT n FROM NodeExecution n WHERE n.status IN ('PENDING', 'RUNNING', 'WAITING')")
    Page<NodeExecution> findActiveExecutions(Pageable pageable);

    /**
     * 查找已完成的节点执行记录
     */
    @Query("SELECT n FROM NodeExecution n WHERE n.status IN ('COMPLETED', 'FAILED', 'SKIPPED', 'CANCELLED', 'TIMEOUT')")
    List<NodeExecution> findCompletedExecutions();

    /**
     * 查找已完成的节点执行记录（分页）
     */
    @Query("SELECT n FROM NodeExecution n WHERE n.status IN ('COMPLETED', 'FAILED', 'SKIPPED', 'CANCELLED', 'TIMEOUT')")
    Page<NodeExecution> findCompletedExecutions(Pageable pageable);

    /**
     * 查找超时的节点执行记录
     */
    @Query("SELECT n FROM NodeExecution n WHERE n.status = 'RUNNING' AND n.timeoutMs IS NOT NULL AND n.startTime < :timeoutBefore")
    List<NodeExecution> findTimeoutExecutions(@Param("timeoutBefore") LocalDateTime timeoutBefore);

    /**
     * 查找需要重试的失败执行记录
     */
    @Query("SELECT n FROM NodeExecution n WHERE n.status IN ('FAILED', 'TIMEOUT') AND n.retryCount < n.maxRetryCount")
    List<NodeExecution> findRetryableExecutions();

    /**
     * 查找长时间运行的执行记录
     */
    @Query("SELECT n FROM NodeExecution n WHERE n.status = 'RUNNING' AND n.startTime < :longRunningBefore")
    List<NodeExecution> findLongRunningExecutions(@Param("longRunningBefore") LocalDateTime longRunningBefore);

    /**
     * 根据实例和状态统计执行记录数量
     */
    @Query("SELECT n.status, COUNT(*) FROM NodeExecution n WHERE n.instanceId = :instanceId GROUP BY n.status")
    List<Object[]> countByInstanceIdGroupByStatus(@Param("instanceId") String instanceId);

    /**
     * 查找执行时间最长的记录
     */
    @Query("SELECT n FROM NodeExecution n WHERE n.durationMs IS NOT NULL ORDER BY n.durationMs DESC")
    Page<NodeExecution> findLongestExecutions(Pageable pageable);

    /**
     * 查找执行时间最短的记录
     */
    @Query("SELECT n FROM NodeExecution n WHERE n.durationMs IS NOT NULL ORDER BY n.durationMs ASC")
    Page<NodeExecution> findShortestExecutions(Pageable pageable);

    /**
     * 计算平均执行时间
     */
    @Query("SELECT AVG(n.durationMs) FROM NodeExecution n WHERE n.nodeType = :nodeType AND n.status = 'COMPLETED'")
    Double calculateAvgExecutionTime(@Param("nodeType") NodeType nodeType);

    /**
     * 计算成功率
     */
    @Query("SELECT " +
           "COUNT(CASE WHEN n.status = 'COMPLETED' THEN 1 END) * 1.0 / COUNT(*) " +
           "FROM NodeExecution n WHERE n.nodeType = :nodeType")
    Double calculateSuccessRate(@Param("nodeType") NodeType nodeType);

    /**
     * 更新执行状态
     */
    @Modifying
    @Query("UPDATE NodeExecution n SET n.status = :status WHERE n.id = :id")
    int updateStatus(@Param("id") String id, @Param("status") NodeStatus status);

    /**
     * 开始执行
     */
    @Modifying
    @Query("UPDATE NodeExecution n SET n.status = 'RUNNING', n.startTime = CURRENT_TIMESTAMP WHERE n.id = :id")
    int startExecution(@Param("id") String id);

    /**
     * 完成执行
     */
    @Modifying
    @Query("UPDATE NodeExecution n SET n.status = 'COMPLETED', n.endTime = CURRENT_TIMESTAMP, n.durationMs = :durationMs WHERE n.id = :id")
    int completeExecution(@Param("id") String id, @Param("durationMs") Long durationMs);

    /**
     * 失败执行
     */
    @Modifying
    @Query("UPDATE NodeExecution n SET n.status = 'FAILED', n.endTime = CURRENT_TIMESTAMP, n.durationMs = :durationMs, n.errorMessage = :errorMessage WHERE n.id = :id")
    int failExecution(@Param("id") String id, @Param("durationMs") Long durationMs, @Param("errorMessage") String errorMessage);

    /**
     * 取消执行
     */
    @Modifying
    @Query("UPDATE NodeExecution n SET n.status = 'CANCELLED', n.endTime = CURRENT_TIMESTAMP, n.durationMs = :durationMs WHERE n.id = :id")
    int cancelExecution(@Param("id") String id, @Param("durationMs") Long durationMs);

    /**
     * 跳过执行
     */
    @Modifying
    @Query("UPDATE NodeExecution n SET n.status = 'SKIPPED', n.endTime = CURRENT_TIMESTAMP, n.durationMs = :durationMs WHERE n.id = :id")
    int skipExecution(@Param("id") String id, @Param("durationMs") Long durationMs);

    /**
     * 设置超时
     */
    @Modifying
    @Query("UPDATE NodeExecution n SET n.status = 'TIMEOUT', n.endTime = CURRENT_TIMESTAMP, n.durationMs = :durationMs WHERE n.id = :id")
    int timeoutExecution(@Param("id") String id, @Param("durationMs") Long durationMs);

    /**
     * 增加重试次数
     */
    @Modifying
    @Query("UPDATE NodeExecution n SET n.retryCount = n.retryCount + 1 WHERE n.id = :id")
    int incrementRetryCount(@Param("id") String id);

    /**
     * 更新输出数据
     */
    @Modifying
    @Query("UPDATE NodeExecution n SET n.outputData = :outputData WHERE n.id = :id")
    int updateOutputData(@Param("id") String id, @Param("outputData") String outputData);

    /**
     * 更新错误信息
     */
    @Modifying
    @Query("UPDATE NodeExecution n SET n.errorMessage = :errorMessage, n.errorStackTrace = :stackTrace WHERE n.id = :id")
    int updateErrorInfo(@Param("id") String id, @Param("errorMessage") String errorMessage, @Param("stackTrace") String stackTrace);

    /**
     * 删除指定时间之前的已完成执行记录
     */
    @Modifying
    @Query("DELETE FROM NodeExecution n WHERE n.status IN ('COMPLETED', 'FAILED', 'SKIPPED', 'CANCELLED', 'TIMEOUT') AND n.endTime < :beforeTime")
    int deleteCompletedExecutionsBefore(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 查找需要清理的执行记录
     */
    @Query("SELECT n FROM NodeExecution n WHERE n.status IN ('COMPLETED', 'FAILED', 'SKIPPED', 'CANCELLED', 'TIMEOUT') AND n.endTime < :beforeTime")
    List<NodeExecution> findExecutionsToCleanup(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 全文搜索执行记录
     */
    @Query("SELECT n FROM NodeExecution n WHERE " +
           "LOWER(n.nodeName) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(n.executorName) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(n.errorMessage) LIKE LOWER(CONCAT('%', :keyword, '%'))")
    Page<NodeExecution> searchByKeyword(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 获取执行统计信息
     */
    @Query("SELECT " +
           "COUNT(*) as totalCount, " +
           "SUM(CASE WHEN n.status = 'PENDING' THEN 1 ELSE 0 END) as pendingCount, " +
           "SUM(CASE WHEN n.status = 'RUNNING' THEN 1 ELSE 0 END) as runningCount, " +
           "SUM(CASE WHEN n.status = 'COMPLETED' THEN 1 ELSE 0 END) as completedCount, " +
           "SUM(CASE WHEN n.status = 'FAILED' THEN 1 ELSE 0 END) as failedCount, " +
           "SUM(CASE WHEN n.status = 'SKIPPED' THEN 1 ELSE 0 END) as skippedCount, " +
           "SUM(CASE WHEN n.status = 'CANCELLED' THEN 1 ELSE 0 END) as cancelledCount, " +
           "SUM(CASE WHEN n.status = 'TIMEOUT' THEN 1 ELSE 0 END) as timeoutCount, " +
           "AVG(n.durationMs) as avgDuration " +
           "FROM NodeExecution n")
    Object[] getExecutionStatistics();

    /**
     * 获取每日执行统计
     */
    @Query("SELECT DATE(n.startTime) as date, COUNT(*) as count " +
           "FROM NodeExecution n " +
           "WHERE n.startTime >= :startDate " +
           "GROUP BY DATE(n.startTime) " +
           "ORDER BY DATE(n.startTime)")
    List<Object[]> getDailyExecutionStatistics(@Param("startDate") LocalDateTime startDate);

    /**
     * 获取节点类型执行统计
     */
    @Query("SELECT n.nodeType, COUNT(*) as count, AVG(n.durationMs) as avgDuration " +
           "FROM NodeExecution n " +
           "WHERE n.status = 'COMPLETED' " +
           "GROUP BY n.nodeType " +
           "ORDER BY count DESC")
    List<Object[]> getNodeTypeStatistics();
}
