package com.company.workflow.repository;

import com.company.workflow.model.entity.WorkflowDefinition;
import com.company.workflow.model.entity.WorkflowStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 工作流定义数据访问接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Repository
public interface WorkflowDefinitionRepository extends JpaRepository<WorkflowDefinition, String>, 
                                                     JpaSpecificationExecutor<WorkflowDefinition> {

    /**
     * 根据名称和版本查找工作流定义
     */
    Optional<WorkflowDefinition> findByNameAndVersion(String name, String version);

    /**
     * 根据名称查找所有版本的工作流定义
     */
    List<WorkflowDefinition> findByNameOrderByCreatedAtDesc(String name);

    /**
     * 根据状态查找工作流定义
     */
    List<WorkflowDefinition> findByStatus(WorkflowStatus status);

    /**
     * 根据状态分页查找工作流定义
     */
    Page<WorkflowDefinition> findByStatus(WorkflowStatus status, Pageable pageable);

    /**
     * 根据分类查找工作流定义
     */
    List<WorkflowDefinition> findByCategory(String category);

    /**
     * 根据分类分页查找工作流定义
     */
    Page<WorkflowDefinition> findByCategory(String category, Pageable pageable);

    /**
     * 根据创建者查找工作流定义
     */
    List<WorkflowDefinition> findByCreatedBy(String createdBy);

    /**
     * 根据创建者分页查找工作流定义
     */
    Page<WorkflowDefinition> findByCreatedBy(String createdBy, Pageable pageable);

    /**
     * 查找激活的工作流定义
     */
    List<WorkflowDefinition> findByIsActiveTrue();

    /**
     * 根据名称查找激活的工作流定义
     */
    Optional<WorkflowDefinition> findByNameAndIsActiveTrue(String name);

    /**
     * 根据名称模糊查找工作流定义
     */
    Page<WorkflowDefinition> findByNameContainingIgnoreCase(String name, Pageable pageable);

    /**
     * 根据描述模糊查找工作流定义
     */
    Page<WorkflowDefinition> findByDescriptionContainingIgnoreCase(String description, Pageable pageable);

    /**
     * 根据创建时间范围查找工作流定义
     */
    List<WorkflowDefinition> findByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计指定状态的工作流定义数量
     */
    long countByStatus(WorkflowStatus status);

    /**
     * 统计指定创建者的工作流定义数量
     */
    long countByCreatedBy(String createdBy);

    /**
     * 统计指定分类的工作流定义数量
     */
    long countByCategory(String category);

    /**
     * 检查工作流名称和版本是否存在
     */
    boolean existsByNameAndVersion(String name, String version);

    /**
     * 检查工作流名称是否存在
     */
    boolean existsByName(String name);

    /**
     * 查找最新版本的工作流定义
     */
    @Query("SELECT w FROM WorkflowDefinition w WHERE w.name = :name ORDER BY w.createdAt DESC LIMIT 1")
    Optional<WorkflowDefinition> findLatestVersionByName(@Param("name") String name);

    /**
     * 查找指定名称的所有已发布版本
     */
    @Query("SELECT w FROM WorkflowDefinition w WHERE w.name = :name AND w.status = 'PUBLISHED' ORDER BY w.createdAt DESC")
    List<WorkflowDefinition> findPublishedVersionsByName(@Param("name") String name);

    /**
     * 查找执行次数最多的工作流定义
     */
    @Query("SELECT w FROM WorkflowDefinition w ORDER BY w.executionCount DESC")
    Page<WorkflowDefinition> findMostExecutedWorkflows(Pageable pageable);

    /**
     * 查找成功率最高的工作流定义
     */
    @Query("SELECT w FROM WorkflowDefinition w WHERE w.executionCount > 0 ORDER BY (w.successCount * 1.0 / w.executionCount) DESC")
    Page<WorkflowDefinition> findHighestSuccessRateWorkflows(Pageable pageable);

    /**
     * 查找平均执行时间最短的工作流定义
     */
    @Query("SELECT w FROM WorkflowDefinition w WHERE w.executionCount > 0 ORDER BY w.avgExecutionTime ASC")
    Page<WorkflowDefinition> findFastestWorkflows(Pageable pageable);

    /**
     * 更新工作流定义的执行统计信息
     */
    @Modifying
    @Query("UPDATE WorkflowDefinition w SET w.executionCount = w.executionCount + 1, w.updatedAt = CURRENT_TIMESTAMP WHERE w.id = :id")
    int incrementExecutionCount(@Param("id") String id);

    /**
     * 更新工作流定义的成功统计信息
     */
    @Modifying
    @Query("UPDATE WorkflowDefinition w SET w.successCount = w.successCount + 1, w.updatedAt = CURRENT_TIMESTAMP WHERE w.id = :id")
    int incrementSuccessCount(@Param("id") String id);

    /**
     * 更新工作流定义的失败统计信息
     */
    @Modifying
    @Query("UPDATE WorkflowDefinition w SET w.failureCount = w.failureCount + 1, w.updatedAt = CURRENT_TIMESTAMP WHERE w.id = :id")
    int incrementFailureCount(@Param("id") String id);

    /**
     * 更新工作流定义的平均执行时间
     */
    @Modifying
    @Query("UPDATE WorkflowDefinition w SET w.avgExecutionTime = :avgExecutionTime, w.updatedAt = CURRENT_TIMESTAMP WHERE w.id = :id")
    int updateAvgExecutionTime(@Param("id") String id, @Param("avgExecutionTime") Long avgExecutionTime);

    /**
     * 激活工作流定义
     */
    @Modifying
    @Query("UPDATE WorkflowDefinition w SET w.isActive = true, w.publishedAt = CURRENT_TIMESTAMP, w.publishedBy = :publishedBy, w.status = 'PUBLISHED', w.updatedAt = CURRENT_TIMESTAMP WHERE w.id = :id")
    int activateWorkflow(@Param("id") String id, @Param("publishedBy") String publishedBy);

    /**
     * 停用工作流定义
     */
    @Modifying
    @Query("UPDATE WorkflowDefinition w SET w.isActive = false, w.updatedAt = CURRENT_TIMESTAMP WHERE w.id = :id")
    int deactivateWorkflow(@Param("id") String id);

    /**
     * 弃用工作流定义
     */
    @Modifying
    @Query("UPDATE WorkflowDefinition w SET w.status = 'DEPRECATED', w.isActive = false, w.updatedAt = CURRENT_TIMESTAMP WHERE w.id = :id")
    int deprecateWorkflow(@Param("id") String id);

    /**
     * 根据名称停用所有其他版本
     */
    @Modifying
    @Query("UPDATE WorkflowDefinition w SET w.isActive = false, w.updatedAt = CURRENT_TIMESTAMP WHERE w.name = :name AND w.id != :excludeId")
    int deactivateOtherVersions(@Param("name") String name, @Param("excludeId") String excludeId);

    /**
     * 删除指定时间之前的草稿状态工作流定义
     */
    @Modifying
    @Query("DELETE FROM WorkflowDefinition w WHERE w.status = 'DRAFT' AND w.createdAt < :beforeTime")
    int deleteDraftsBefore(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 查找需要清理的工作流定义
     */
    @Query("SELECT w FROM WorkflowDefinition w WHERE w.status = 'DEPRECATED' AND w.updatedAt < :beforeTime")
    List<WorkflowDefinition> findDeprecatedWorkflowsBefore(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 根据标签查找工作流定义
     */
    @Query("SELECT w FROM WorkflowDefinition w WHERE JSON_CONTAINS(w.tags, :tag)")
    List<WorkflowDefinition> findByTag(@Param("tag") String tag);

    /**
     * 全文搜索工作流定义
     */
    @Query("SELECT w FROM WorkflowDefinition w WHERE " +
           "LOWER(w.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(w.description) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(w.category) LIKE LOWER(CONCAT('%', :keyword, '%'))")
    Page<WorkflowDefinition> searchByKeyword(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 获取工作流定义统计信息
     */
    @Query("SELECT " +
           "COUNT(*) as totalCount, " +
           "SUM(CASE WHEN w.status = 'PUBLISHED' THEN 1 ELSE 0 END) as publishedCount, " +
           "SUM(CASE WHEN w.status = 'DRAFT' THEN 1 ELSE 0 END) as draftCount, " +
           "SUM(CASE WHEN w.status = 'DEPRECATED' THEN 1 ELSE 0 END) as deprecatedCount, " +
           "SUM(CASE WHEN w.isActive = true THEN 1 ELSE 0 END) as activeCount " +
           "FROM WorkflowDefinition w")
    Object[] getWorkflowStatistics();
}
