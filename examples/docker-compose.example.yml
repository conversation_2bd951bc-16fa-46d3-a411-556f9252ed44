# 企业级工作流引擎系统 - Docker Compose 示例配置
# 用于快速搭建开发和测试环境

version: '3.8'

services:
  # 工作流引擎服务
  workflow-engine:
    image: workflow-engine:latest
    container_name: workflow-engine
    ports:
      - "8080:8080"
      - "8081:8081"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - MYSQL_HOST=mysql
      - MYSQL_USERNAME=workflow
      - MYSQL_PASSWORD=workflow123
      - REDIS_HOST=redis
      - REDIS_PASSWORD=
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_USERNAME=workflow
      - RABBITMQ_PASSWORD=workflow123
      - JWT_SECRET=your-jwt-secret-key-here
      - ENCRYPTION_KEY=your-encryption-key-here
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
    networks:
      - workflow-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # MySQL 数据库
  mysql:
    image: mysql:8.0
    container_name: workflow-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: workflow_engine
      MYSQL_USER: workflow
      MYSQL_PASSWORD: workflow123
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql-data:/var/lib/mysql
      - ./sql/init.sql:/docker-entrypoint-initdb.d/01-init.sql
      - ./sql/schema.sql:/docker-entrypoint-initdb.d/02-schema.sql
      - ./sql/data.sql:/docker-entrypoint-initdb.d/03-data.sql
      - ./mysql/conf.d:/etc/mysql/conf.d
    networks:
      - workflow-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-proot123"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    command: --default-authentication-plugin=mysql_native_password

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: workflow-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - workflow-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    command: redis-server /usr/local/etc/redis/redis.conf

  # RabbitMQ 消息队列
  rabbitmq:
    image: rabbitmq:3-management
    container_name: workflow-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: workflow
      RABBITMQ_DEFAULT_PASS: workflow123
      RABBITMQ_DEFAULT_VHOST: /
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq-data:/var/lib/rabbitmq
      - ./rabbitmq/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf
      - ./rabbitmq/definitions.json:/etc/rabbitmq/definitions.json
    networks:
      - workflow-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "check_port_connectivity"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # 前端应用
  workflow-frontend:
    image: workflow-frontend:latest
    container_name: workflow-frontend
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - workflow-engine
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./ssl:/etc/nginx/ssl
    networks:
      - workflow-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus 监控
  prometheus:
    image: prom/prometheus:latest
    container_name: workflow-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./monitoring/rules:/etc/prometheus/rules
      - prometheus-data:/prometheus
    networks:
      - workflow-network
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # Grafana 仪表板
  grafana:
    image: grafana/grafana:latest
    container_name: workflow-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    networks:
      - workflow-network
    restart: unless-stopped
    depends_on:
      - prometheus

  # AlertManager 告警管理
  alertmanager:
    image: prom/alertmanager:latest
    container_name: workflow-alertmanager
    ports:
      - "9093:9093"
    volumes:
      - ./monitoring/alertmanager.yml:/etc/alertmanager/alertmanager.yml
      - alertmanager-data:/alertmanager
    networks:
      - workflow-network
    restart: unless-stopped
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'

  # Elasticsearch (日志存储)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: workflow-elasticsearch
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    networks:
      - workflow-network
    restart: unless-stopped

  # Kibana (日志可视化)
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: workflow-kibana
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - workflow-network
    restart: unless-stopped

  # Logstash (日志处理)
  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: workflow-logstash
    volumes:
      - ./logging/logstash.conf:/usr/share/logstash/pipeline/logstash.conf
      - ./logs:/usr/share/logstash/logs
    depends_on:
      - elasticsearch
    networks:
      - workflow-network
    restart: unless-stopped

# 数据卷定义
volumes:
  mysql-data:
    driver: local
  redis-data:
    driver: local
  rabbitmq-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  alertmanager-data:
    driver: local
  elasticsearch-data:
    driver: local

# 网络定义
networks:
  workflow-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
