/**
 * 订单处理工作流示例
 * 
 * 这个工作流演示了一个完整的电商订单处理流程，包括：
 * - 订单验证
 * - 库存检查
 * - 支付处理
 * - 并行执行库存扣减和订单创建
 * - 通知发送
 * - 异常处理和重试机制
 */

workflow "订单处理流程" {
    version "1.0.0"
    description "处理用户订单的完整流程，包括验证、支付、库存管理等"
    category "电商业务"
    tags ["订单", "支付", "库存", "通知"]
    
    // 全局变量定义
    variables {
        orderId = ""
        userId = ""
        productId = ""
        quantity = 0
        amount = 0.0
        userEmail = ""
        userPhone = ""
        status = "pending"
        paymentId = ""
        inventoryReserved = false
        orderCreated = false
    }
    
    // 流程定义
    process {
        // 开始节点
        start "开始处理订单" {
            description "接收订单处理请求"
            next "验证订单信息"
        }
        
        // 订单验证任务
        task "验证订单信息" {
            description "验证订单基本信息的完整性和有效性"
            type "service"
            service "orderService.validateOrder"
            
            input {
                orderId = variables.orderId
                userId = variables.userId
                productId = variables.productId
                quantity = variables.quantity
                amount = variables.amount
            }
            
            output {
                variables.status = result.status
                variables.userEmail = result.userEmail
                variables.userPhone = result.userPhone
            }
            
            timeout 30.seconds
            
            retry {
                maxAttempts 3
                backoff exponential(1.second, 2.0)
                retryOn ["TimeoutException", "ConnectException"]
                stopOn ["ValidationException", "IllegalArgumentException"]
            }
            
            onSuccess "检查库存"
            onFailure "发送验证失败通知"
        }
        
        // 库存检查任务
        task "检查库存" {
            description "检查商品库存是否充足"
            type "service"
            service "inventoryService.checkStock"
            
            input {
                productId = variables.productId
                quantity = variables.quantity
            }
            
            output {
                variables.stockAvailable = result.available
                variables.reservationId = result.reservationId
            }
            
            timeout 15.seconds
            
            onSuccess "库存充足判断"
            onFailure "发送库存不足通知"
        }
        
        // 库存判断决策节点
        decision "库存充足判断" {
            description "判断库存是否充足"
            condition { variables.stockAvailable >= variables.quantity }
            onTrue "金额判断"
            onFalse "发送库存不足通知"
        }
        
        // 金额判断决策节点
        decision "金额判断" {
            description "根据订单金额决定处理流程"
            when {
                case { variables.amount > 10000 } then "人工审核"
                case { variables.amount > 1000 } then "高级验证"
                default "处理支付"
            }
        }
        
        // 人工审核任务
        task "人工审核" {
            description "高额订单需要人工审核"
            type "manual"
            assignee "finance-team"
            
            form {
                field "审核结果" {
                    type "select"
                    options ["通过", "拒绝", "需要补充材料"]
                    required true
                }
                field "审核意见" {
                    type "textarea"
                    maxLength 500
                }
            }
            
            timeout 2.hours
            
            onComplete "审核结果判断"
            onTimeout "审核超时处理"
        }
        
        // 审核结果判断
        decision "审核结果判断" {
            condition { taskResult.审核结果 == "通过" }
            onTrue "处理支付"
            onFalse "发送审核拒绝通知"
        }
        
        // 高级验证任务
        task "高级验证" {
            description "中等金额订单的高级验证"
            type "script"
            script {
                language "groovy"
                code """
                    // 风险评估逻辑
                    def riskScore = 0
                    
                    // 用户历史订单检查
                    def userHistory = userService.getUserOrderHistory(variables.userId)
                    if (userHistory.totalOrders < 5) {
                        riskScore += 20
                    }
                    
                    // 支付方式检查
                    if (variables.paymentMethod == "credit_card") {
                        riskScore += 10
                    }
                    
                    // 地址验证
                    def addressValid = addressService.validateAddress(variables.shippingAddress)
                    if (!addressValid) {
                        riskScore += 30
                    }
                    
                    return [riskScore: riskScore, passed: riskScore < 50]
                """
            }
            
            output {
                variables.riskScore = result.riskScore
                variables.riskPassed = result.passed
            }
            
            onSuccess "风险评估结果"
        }
        
        // 风险评估结果判断
        decision "风险评估结果" {
            condition { variables.riskPassed }
            onTrue "处理支付"
            onFalse "发送风险拒绝通知"
        }
        
        // 支付处理任务
        task "处理支付" {
            description "调用支付服务处理订单支付"
            type "service"
            service "paymentService.processPayment"
            
            input {
                orderId = variables.orderId
                userId = variables.userId
                amount = variables.amount
                paymentMethod = variables.paymentMethod
            }
            
            output {
                variables.paymentId = result.paymentId
                variables.paymentStatus = result.status
            }
            
            timeout 60.seconds
            
            retry {
                maxAttempts 2
                backoff fixed(5.seconds)
            }
            
            onSuccess "支付结果判断"
            onFailure "支付失败处理"
        }
        
        // 支付结果判断
        decision "支付结果判断" {
            condition { variables.paymentStatus == "success" }
            onTrue "并行处理订单"
            onFalse "支付失败处理"
        }
        
        // 并行处理网关
        parallel "并行处理订单" {
            description "并行执行库存扣减和订单创建"
            
            branch "库存处理分支" {
                task "扣减库存" {
                    description "从库存中扣减商品数量"
                    type "service"
                    service "inventoryService.deductStock"
                    
                    input {
                        productId = variables.productId
                        quantity = variables.quantity
                        reservationId = variables.reservationId
                    }
                    
                    output {
                        variables.inventoryReserved = result.success
                    }
                    
                    timeout 30.seconds
                    
                    retry {
                        maxAttempts 3
                        backoff exponential(1.second, 1.5)
                    }
                }
            }
            
            branch "订单创建分支" {
                task "创建订单记录" {
                    description "在数据库中创建正式订单记录"
                    type "service"
                    service "orderService.createOrder"
                    
                    input {
                        orderId = variables.orderId
                        userId = variables.userId
                        productId = variables.productId
                        quantity = variables.quantity
                        amount = variables.amount
                        paymentId = variables.paymentId
                    }
                    
                    output {
                        variables.orderCreated = result.success
                        variables.orderNumber = result.orderNumber
                    }
                    
                    timeout 30.seconds
                }
            }
            
            // 合并策略：等待所有分支完成
            join "合并处理结果" {
                strategy "waitAll"
                timeout 2.minutes
                onTimeout "并行处理超时"
            }
        }
        
        // 处理结果验证
        decision "处理结果验证" {
            condition { variables.inventoryReserved && variables.orderCreated }
            onTrue "发送成功通知"
            onFalse "回滚处理"
        }
        
        // 延迟任务示例
        delay "等待系统同步" {
            description "等待各系统数据同步"
            duration 30.seconds
            next "发送成功通知"
        }
        
        // 成功通知
        notification "发送成功通知" {
            description "向用户发送订单处理成功通知"
            
            // 邮件通知
            email {
                to variables.userEmail
                subject "订单处理成功 - ${variables.orderNumber}"
                template "order-success"
                data {
                    orderNumber = variables.orderNumber
                    amount = variables.amount
                    productName = variables.productName
                }
            }
            
            // 短信通知
            sms {
                to variables.userPhone
                template "order-success-sms"
                data {
                    orderNumber = variables.orderNumber
                }
            }
            
            // 企业微信通知
            wechat {
                webhook "https://qyapi.weixin.qq.com/cgi-bin/webhook/send"
                message "订单 ${variables.orderNumber} 处理完成，金额：${variables.amount} 元"
            }
            
            next "订单处理完成"
        }
        
        // 结束节点
        end "订单处理完成" {
            description "订单处理流程成功完成"
            output {
                orderId = variables.orderId
                orderNumber = variables.orderNumber
                status = "completed"
                paymentId = variables.paymentId
                processedAt = now()
            }
        }
        
        // 错误处理节点
        task "回滚处理" {
            description "处理失败时的回滚操作"
            type "service"
            service "orderService.rollbackOrder"
            
            input {
                orderId = variables.orderId
                paymentId = variables.paymentId
                inventoryReserved = variables.inventoryReserved
                orderCreated = variables.orderCreated
            }
            
            next "发送失败通知"
        }
        
        // 各种失败通知
        notification "发送失败通知" {
            email {
                to variables.userEmail
                subject "订单处理失败 - ${variables.orderId}"
                template "order-failure"
            }
            next "订单处理失败"
        }
        
        notification "发送验证失败通知" {
            email {
                to "<EMAIL>"
                subject "订单验证失败 - ${variables.orderId}"
                template "order-validation-failure"
            }
            next "订单处理失败"
        }
        
        notification "发送库存不足通知" {
            email {
                to variables.userEmail
                subject "商品库存不足 - ${variables.orderId}"
                template "stock-insufficient"
            }
            next "订单处理失败"
        }
        
        end "订单处理失败" {
            description "订单处理失败结束"
            output {
                orderId = variables.orderId
                status = "failed"
                failureReason = variables.failureReason
                processedAt = now()
            }
        }
    }
    
    // 全局异常处理
    errorHandling {
        catch "ValidationException" {
            log "订单验证异常: ${error.message}"
            notification "发送验证异常通知" {
                email {
                    to "<EMAIL>"
                    subject "订单验证异常"
                    body "订单 ${variables.orderId} 验证时发生异常: ${error.message}"
                }
            }
            end "验证异常结束"
        }
        
        catch "PaymentException" {
            log "支付处理异常: ${error.message}"
            task "支付异常处理" {
                service "paymentService.handleException"
            }
            end "支付异常结束"
        }
        
        catch "TimeoutException" {
            log "处理超时: ${error.message}"
            task "超时处理" {
                service "orderService.handleTimeout"
            }
            retry "重新处理"
        }
        
        // 默认异常处理
        catchAll {
            log "未知异常: ${error.message}"
            notification "发送系统异常通知" {
                email {
                    to "<EMAIL>"
                    subject "工作流系统异常"
                    body "工作流执行时发生未知异常: ${error.message}"
                }
            }
            end "系统异常结束"
        }
    }
    
    // 事件监听器
    listeners {
        onStart {
            log "开始处理订单: ${variables.orderId}"
            metrics.counter("workflow.order.start").increment()
        }
        
        onComplete {
            log "订单处理完成: ${variables.orderId}, 耗时: ${executionTime}ms"
            metrics.timer("workflow.order.duration").record(executionTime)
            metrics.counter("workflow.order.success").increment()
        }
        
        onError {
            log "订单处理异常: ${variables.orderId}, 错误: ${error.message}"
            metrics.counter("workflow.order.error").increment()
            alert.send("workflow.order.error", [
                orderId: variables.orderId,
                error: error.message,
                timestamp: now()
            ])
        }
        
        onNodeEnter { nodeId ->
            log "进入节点: ${nodeId}"
            metrics.counter("workflow.node.enter", [node: nodeId]).increment()
        }
        
        onNodeExit { nodeId, duration ->
            log "离开节点: ${nodeId}, 耗时: ${duration}ms"
            metrics.timer("workflow.node.duration", [node: nodeId]).record(duration)
        }
    }
}
