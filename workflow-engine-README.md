# 企业级工作流引擎系统

[![License](https://img.shields.io/badge/license-Apache%202-blue.svg)](https://www.apache.org/licenses/LICENSE-2.0)
[![Java](https://img.shields.io/badge/Java-17+-orange.svg)](https://openjdk.java.net/)
[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.x-green.svg)](https://spring.io/projects/spring-boot)
[![Vue](https://img.shields.io/badge/Vue-3.x-brightgreen.svg)](https://vuejs.org/)
[![LogicFlow](https://img.shields.io/badge/LogicFlow-1.2+-blue.svg)](https://site.logic-flow.cn/)

基于 Groovy DSL 和 LogicFlow 的企业级工作流引擎系统，提供可视化流程设计、强大的执行引擎、完善的监控管理等功能。

## ✨ 核心特性

- 🎨 **可视化设计**: 基于滴滴 LogicFlow 的专业流程设计器
- 🚀 **DSL 语法**: 直观的 Groovy DSL 语法，支持复杂业务逻辑
- ⚡ **高性能执行**: 支持并行、异步、分布式执行
- 🔒 **企业级安全**: 完善的权限控制、数据加密、审计日志
- 📊 **监控告警**: 实时监控、性能统计、智能告警
- 🔧 **丰富集成**: 邮件、企业微信、HTTP 服务等多种集成方式
- 🌐 **云原生**: 支持 Docker、Kubernetes 部署，弹性扩展

## 🏗️ 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    前端展示层                                │
├─────────────────────────────────────────────────────────────┤
│  LogicFlow 可视化编辑器  │  工作流管理界面  │  监控仪表板    │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    API 网关层                               │
├─────────────────────────────────────────────────────────────┤
│  Spring Cloud Gateway │ 认证授权 │ 限流熔断 │ 路由转发      │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   微服务应用层                              │
├─────────────────────────────────────────────────────────────┤
│ 工作流设计服务 │ 执行引擎服务 │ 监控服务 │ 通知服务 │ 用户服务│
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   基础设施层                                │
├─────────────────────────────────────────────────────────────┤
│   MySQL   │   Redis   │  RabbitMQ  │  文件存储  │  配置中心  │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 快速开始

### 环境要求

- **Java**: 17+
- **Node.js**: 16+
- **MySQL**: 8.0+
- **Redis**: 7.0+
- **RabbitMQ**: 3.11+
- **Docker**: 20.10+ (可选)
- **Kubernetes**: 1.24+ (可选)

### 本地开发环境搭建

#### 1. 克隆项目

```bash
git clone https://github.com/your-org/workflow-engine.git
cd workflow-engine
```

#### 2. 启动基础服务

使用 Docker Compose 快速启动依赖服务：

```bash
# 启动 MySQL、Redis、RabbitMQ
docker-compose up -d mysql redis rabbitmq

# 等待服务启动完成
docker-compose logs -f mysql
```

#### 3. 初始化数据库

```bash
# 连接到 MySQL 并执行初始化脚本
mysql -h localhost -P 3306 -u root -p < sql/init.sql
```

#### 4. 启动后端服务

```bash
cd backend

# 安装依赖
mvn clean install

# 启动应用 (开发模式)
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

后端服务将在 `http://localhost:8080` 启动。

#### 5. 启动前端应用

```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

前端应用将在 `http://localhost:3000` 启动。

### Docker 部署

#### 1. 构建镜像

```bash
# 构建后端镜像
docker build -t workflow-engine:latest .

# 构建前端镜像
cd frontend
docker build -t workflow-frontend:latest .
```

#### 2. 启动完整服务

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f workflow-engine
```

访问 `http://localhost` 即可使用系统。

## 📖 使用示例

### Groovy DSL 工作流定义

```groovy
workflow "订单处理流程" {
    version "1.0.0"
    description "处理用户订单的完整流程"
    
    variables {
        orderId = ""
        userId = ""
        amount = 0.0
        status = "pending"
    }
    
    process {
        start "开始" {
            next "验证订单"
        }
        
        task "验证订单" {
            type "service"
            service "orderService.validate"
            input {
                orderId = variables.orderId
                userId = variables.userId
            }
            timeout 30.seconds
            retry {
                maxAttempts 3
                backoff exponential(1.second, 2.0)
            }
            onSuccess "检查库存"
            onFailure "发送失败通知"
        }
        
        decision "检查库存" {
            condition { variables.amount > 1000 }
            onTrue "人工审核"
            onFalse "自动处理"
        }
        
        parallel "并行处理" {
            branch "扣减库存" {
                task "库存扣减" {
                    service "inventoryService.deduct"
                }
            }
            branch "生成订单" {
                task "创建订单" {
                    service "orderService.create"
                }
            }
            join "合并结果"
        }
        
        notification "发送通知" {
            email {
                to variables.userEmail
                subject "订单处理完成"
                template "order-completion"
            }
            wechat {
                webhook "https://qyapi.weixin.qq.com/cgi-bin/webhook/send"
                message "订单 ${variables.orderId} 处理完成"
            }
        }
        
        end "完成"
    }
}
```

### API 调用示例

```bash
# 创建工作流
curl -X POST http://localhost:8080/api/v1/workflows \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "订单处理流程",
    "dslContent": "workflow \"订单处理流程\" { ... }",
    "description": "处理用户订单的完整流程"
  }'

# 启动工作流实例
curl -X POST http://localhost:8080/api/v1/executions/start \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "workflowId": "workflow-id",
    "inputData": {
      "orderId": "ORDER-001",
      "userId": "USER-001",
      "amount": 1500.0
    }
  }'

# 查询执行状态
curl -X GET http://localhost:8080/api/v1/executions/instance-id \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 📚 文档

- [技术设计文档](docs/技术设计文档.md) - 详细的系统架构和技术设计
- [项目结构说明](docs/项目结构说明.md) - 项目代码组织结构
- [API 接口文档](docs/API接口文档.md) - REST API 接口说明
- [部署运维手册](docs/部署运维手册.md) - 生产环境部署指南
- [用户使用指南](docs/用户使用指南.md) - 用户操作手册

## 🤝 贡献指南

我们欢迎所有形式的贡献，包括但不限于：

- 🐛 报告 Bug
- 💡 提出新功能建议
- 📝 改进文档
- 🔧 提交代码修复
- ⭐ 为项目点星

## 📄 许可证

本项目基于 [Apache License 2.0](LICENSE) 开源协议。

## 🙏 致谢

感谢以下开源项目的支持：

- [Spring Boot](https://spring.io/projects/spring-boot) - 企业级 Java 应用框架
- [Vue.js](https://vuejs.org/) - 渐进式 JavaScript 框架
- [LogicFlow](https://site.logic-flow.cn/) - 专业的流程图编辑框架
- [Groovy](https://groovy-lang.org/) - 动态编程语言
- [Element Plus](https://element-plus.org/) - Vue 3 组件库

## 📞 联系我们

- 📧 邮箱: <EMAIL>
- 💬 微信群: 扫描二维码加入技术交流群
- 🐛 问题反馈: [GitHub Issues](https://github.com/your-org/workflow-engine/issues)

---

⭐ 如果这个项目对你有帮助，请给我们一个 Star！
