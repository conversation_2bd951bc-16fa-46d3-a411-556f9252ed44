<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/workflow-icon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="企业级工作流引擎可视化设计器" />
    <meta name="keywords" content="工作流,流程设计,可视化,企业级,LogicFlow" />
    <meta name="author" content="Workflow Team" />
    <title>工作流引擎 - 可视化设计器</title>
    
    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- 全局样式 -->
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      html, body {
        height: 100%;
        font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        background-color: #f5f7fa;
      }
      
      #app {
        height: 100%;
      }
      
      /* 加载动画 */
      .loading-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #f5f7fa;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e4e7ed;
        border-top: 4px solid #409eff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      .loading-text {
        margin-top: 16px;
        color: #606266;
        font-size: 14px;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* 隐藏加载动画 */
      .loaded .loading-container {
        display: none;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <!-- 初始加载动画 -->
      <div class="loading-container">
        <div>
          <div class="loading-spinner"></div>
          <div class="loading-text">正在加载工作流设计器...</div>
        </div>
      </div>
    </div>
    
    <script type="module" src="/src/main.ts"></script>
    
    <!-- 错误处理 -->
    <script>
      window.addEventListener('error', function(e) {
        console.error('应用加载错误:', e.error);
      });
      
      window.addEventListener('unhandledrejection', function(e) {
        console.error('未处理的Promise拒绝:', e.reason);
      });
    </script>
  </body>
</html>
