<template>
  <div class="node-palette">
    <div class="palette-header">
      <h3 class="palette-title">节点库</h3>
      <el-input
        v-model="searchKeyword"
        placeholder="搜索节点..."
        size="small"
        clearable
        :prefix-icon="Search"
      />
    </div>
    
    <div class="palette-content">
      <el-collapse v-model="activeCategories" accordion>
        <el-collapse-item
          v-for="category in filteredCategories"
          :key="category.name"
          :title="category.title"
          :name="category.name"
        >
          <template #title>
            <div class="category-title">
              <el-icon>
                <component :is="category.icon" />
              </el-icon>
              <span>{{ category.title }}</span>
              <el-badge :value="category.nodes.length" class="category-badge" />
            </div>
          </template>
          
          <div class="node-list">
            <div
              v-for="node in category.nodes"
              :key="node.type"
              class="node-item"
              :draggable="true"
              @dragstart="handleDragStart($event, node)"
              @dragend="handleDragEnd"
            >
              <div class="node-icon">
                <el-icon>
                  <component :is="node.icon" />
                </el-icon>
              </div>
              <div class="node-info">
                <div class="node-name">{{ node.name }}</div>
                <div class="node-description">{{ node.description }}</div>
              </div>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Search } from '@element-plus/icons-vue'

interface NodeDefinition {
  type: string
  name: string
  description: string
  icon: string
  category: string
  properties?: Record<string, any>
}

interface NodeCategory {
  name: string
  title: string
  icon: string
  nodes: NodeDefinition[]
}

const emit = defineEmits<{
  nodeDrag: [nodeType: string, nodeData: NodeDefinition]
}>()

const searchKeyword = ref('')
const activeCategories = ref(['basic'])

// 节点定义
const nodeDefinitions: NodeDefinition[] = [
  // 基础节点
  {
    type: 'start',
    name: '开始节点',
    description: '工作流的起始点',
    icon: 'VideoPlay',
    category: 'basic'
  },
  {
    type: 'end',
    name: '结束节点',
    description: '工作流的结束点',
    icon: 'VideoPause',
    category: 'basic'
  },
  {
    type: 'task',
    name: '任务节点',
    description: '执行具体的业务任务',
    icon: 'Document',
    category: 'basic'
  },
  {
    type: 'decision',
    name: '决策节点',
    description: '根据条件进行分支判断',
    icon: 'Share',
    category: 'basic'
  },
  
  // 流程控制
  {
    type: 'parallel',
    name: '并行网关',
    description: '并行执行多个分支',
    icon: 'Connection',
    category: 'control'
  },
  {
    type: 'merge',
    name: '合并网关',
    description: '合并多个并行分支',
    icon: 'Merge',
    category: 'control'
  },
  {
    type: 'timer',
    name: '定时器',
    description: '定时触发或延时执行',
    icon: 'Timer',
    category: 'control'
  },
  {
    type: 'loop',
    name: '循环节点',
    description: '循环执行子流程',
    icon: 'Refresh',
    category: 'control'
  },
  
  // 系统集成
  {
    type: 'http',
    name: 'HTTP请求',
    description: '发送HTTP请求到外部系统',
    icon: 'Link',
    category: 'integration'
  },
  {
    type: 'database',
    name: '数据库操作',
    description: '执行数据库查询或更新',
    icon: 'Coin',
    category: 'integration'
  },
  {
    type: 'email',
    name: '邮件发送',
    description: '发送邮件通知',
    icon: 'Message',
    category: 'integration'
  },
  {
    type: 'file',
    name: '文件操作',
    description: '读取、写入或处理文件',
    icon: 'Folder',
    category: 'integration'
  },
  
  // 通知节点
  {
    type: 'notification',
    name: '系统通知',
    description: '发送系统内通知',
    icon: 'Bell',
    category: 'notification'
  },
  {
    type: 'sms',
    name: '短信通知',
    description: '发送短信消息',
    icon: 'ChatDotRound',
    category: 'notification'
  },
  {
    type: 'webhook',
    name: 'Webhook',
    description: '发送Webhook回调',
    icon: 'Connection',
    category: 'notification'
  },
  
  // 脚本节点
  {
    type: 'script',
    name: '脚本执行',
    description: '执行自定义脚本代码',
    icon: 'DocumentCopy',
    category: 'script'
  },
  {
    type: 'groovy',
    name: 'Groovy脚本',
    description: '执行Groovy脚本',
    icon: 'Edit',
    category: 'script'
  },
  {
    type: 'javascript',
    name: 'JavaScript',
    description: '执行JavaScript代码',
    icon: 'EditPen',
    category: 'script'
  }
]

// 节点分类
const categories: NodeCategory[] = [
  {
    name: 'basic',
    title: '基础节点',
    icon: 'Grid',
    nodes: []
  },
  {
    name: 'control',
    title: '流程控制',
    icon: 'Share',
    nodes: []
  },
  {
    name: 'integration',
    title: '系统集成',
    icon: 'Connection',
    nodes: []
  },
  {
    name: 'notification',
    title: '通知节点',
    icon: 'Bell',
    nodes: []
  },
  {
    name: 'script',
    title: '脚本节点',
    icon: 'DocumentCopy',
    nodes: []
  }
]

// 将节点分配到对应分类
categories.forEach(category => {
  category.nodes = nodeDefinitions.filter(node => node.category === category.name)
})

// 过滤后的分类
const filteredCategories = computed(() => {
  if (!searchKeyword.value) {
    return categories
  }
  
  const keyword = searchKeyword.value.toLowerCase()
  return categories.map(category => ({
    ...category,
    nodes: category.nodes.filter(node =>
      node.name.toLowerCase().includes(keyword) ||
      node.description.toLowerCase().includes(keyword)
    )
  })).filter(category => category.nodes.length > 0)
})

// 处理拖拽开始
const handleDragStart = (event: DragEvent, node: NodeDefinition) => {
  if (!event.dataTransfer) return
  
  // 设置拖拽数据
  event.dataTransfer.setData('application/json', JSON.stringify(node))
  event.dataTransfer.effectAllowed = 'copy'
  
  // 设置拖拽图像
  const dragImage = createDragImage(node)
  event.dataTransfer.setDragImage(dragImage, 50, 25)
  
  // 添加拖拽样式
  const target = event.target as HTMLElement
  target.classList.add('dragging')
  
  emit('nodeDrag', node.type, node)
}

// 处理拖拽结束
const handleDragEnd = (event: DragEvent) => {
  const target = event.target as HTMLElement
  target.classList.remove('dragging')
}

// 创建拖拽图像
const createDragImage = (node: NodeDefinition) => {
  const dragImage = document.createElement('div')
  dragImage.className = 'drag-image'
  dragImage.innerHTML = `
    <div class="drag-node">
      <div class="drag-node-icon">
        <i class="el-icon-${node.icon.toLowerCase()}"></i>
      </div>
      <div class="drag-node-name">${node.name}</div>
    </div>
  `
  
  // 添加样式
  dragImage.style.cssText = `
    position: absolute;
    top: -1000px;
    left: -1000px;
    width: 100px;
    height: 50px;
    background: var(--el-color-primary);
    color: white;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  `
  
  document.body.appendChild(dragImage)
  
  // 清理
  setTimeout(() => {
    document.body.removeChild(dragImage)
  }, 0)
  
  return dragImage
}
</script>

<style lang="scss" scoped>
.node-palette {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);
}

.palette-header {
  padding: 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  
  .palette-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0 0 12px 0;
  }
}

.palette-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
  
  :deep(.el-collapse) {
    border: none;
    
    .el-collapse-item {
      margin-bottom: 8px;
      border: 1px solid var(--el-border-color-lighter);
      border-radius: 6px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .el-collapse-item__header {
        height: 48px;
        padding: 0 16px;
        background: var(--el-fill-color-lighter);
        border: none;
        border-radius: 6px 6px 0 0;
        
        &.is-active {
          border-bottom: 1px solid var(--el-border-color-lighter);
          border-radius: 6px 6px 0 0;
        }
      }
      
      .el-collapse-item__content {
        padding: 0;
        border-radius: 0 0 6px 6px;
      }
    }
  }
}

.category-title {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  
  .el-icon {
    font-size: 16px;
    color: var(--el-color-primary);
  }
  
  span {
    flex: 1;
    font-weight: 500;
  }
  
  .category-badge {
    :deep(.el-badge__content) {
      font-size: 10px;
      height: 16px;
      line-height: 16px;
      padding: 0 4px;
    }
  }
}

.node-list {
  padding: 8px;
}

.node-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  margin-bottom: 8px;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
  cursor: grab;
  transition: all 0.3s;
  
  &:hover {
    background: var(--el-fill-color-light);
    border-color: var(--el-color-primary-light-7);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  &:active,
  &.dragging {
    cursor: grabbing;
    transform: scale(0.95);
  }
  
  &:last-child {
    margin-bottom: 0;
  }
}

.node-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--el-color-primary-light-9);
  border-radius: 6px;
  flex-shrink: 0;
  
  .el-icon {
    font-size: 16px;
    color: var(--el-color-primary);
  }
}

.node-info {
  flex: 1;
  min-width: 0;
  
  .node-name {
    font-size: 14px;
    font-weight: 500;
    color: var(--el-text-color-primary);
    margin-bottom: 4px;
  }
  
  .node-description {
    font-size: 12px;
    color: var(--el-text-color-regular);
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
}

// 拖拽图像样式
:global(.drag-image) {
  .drag-node {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;
    
    &-icon {
      font-size: 16px;
    }
    
    &-name {
      font-size: 10px;
      text-align: center;
    }
  }
}
</style>
