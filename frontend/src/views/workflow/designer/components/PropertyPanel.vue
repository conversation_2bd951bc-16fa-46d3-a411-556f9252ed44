<template>
  <div class="property-panel">
    <div class="panel-header">
      <h3 class="panel-title">属性面板</h3>
      <el-button v-if="selectedElement" type="text" size="small" @click="clearSelection">
        <el-icon><Close /></el-icon>
      </el-button>
    </div>
    
    <div class="panel-content">
      <!-- 未选择任何元素 -->
      <div v-if="!selectedElement" class="empty-state">
        <el-icon class="empty-icon"><Select /></el-icon>
        <p class="empty-text">请选择一个节点或连线</p>
      </div>
      
      <!-- 节点属性 -->
      <div v-else-if="isNode" class="property-form">
        <el-form :model="nodeProperties" label-position="top" size="small">
          <!-- 基础信息 -->
          <div class="property-section">
            <h4 class="section-title">基础信息</h4>
            
            <el-form-item label="节点ID">
              <el-input v-model="nodeProperties.id" disabled />
            </el-form-item>
            
            <el-form-item label="节点名称">
              <el-input 
                v-model="nodeProperties.name" 
                placeholder="请输入节点名称"
                @input="handlePropertyChange('name', $event)"
              />
            </el-form-item>
            
            <el-form-item label="节点类型">
              <el-select v-model="nodeProperties.type" disabled>
                <el-option 
                  v-for="type in nodeTypes" 
                  :key="type.value" 
                  :label="type.label" 
                  :value="type.value" 
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="描述">
              <el-input 
                v-model="nodeProperties.description" 
                type="textarea" 
                :rows="3"
                placeholder="请输入节点描述"
                @input="handlePropertyChange('description', $event)"
              />
            </el-form-item>
          </div>
          
          <!-- 位置信息 -->
          <div class="property-section">
            <h4 class="section-title">位置信息</h4>
            
            <el-row :gutter="12">
              <el-col :span="12">
                <el-form-item label="X坐标">
                  <el-input-number 
                    v-model="nodeProperties.x" 
                    :min="0"
                    controls-position="right"
                    @change="handlePropertyChange('x', $event)"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="Y坐标">
                  <el-input-number 
                    v-model="nodeProperties.y" 
                    :min="0"
                    controls-position="right"
                    @change="handlePropertyChange('y', $event)"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="12" v-if="nodeProperties.width !== undefined">
              <el-col :span="12">
                <el-form-item label="宽度">
                  <el-input-number 
                    v-model="nodeProperties.width" 
                    :min="50"
                    controls-position="right"
                    @change="handlePropertyChange('width', $event)"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="高度">
                  <el-input-number 
                    v-model="nodeProperties.height" 
                    :min="30"
                    controls-position="right"
                    @change="handlePropertyChange('height', $event)"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          
          <!-- 特定节点属性 -->
          <div v-if="nodeSpecificProperties.length > 0" class="property-section">
            <h4 class="section-title">{{ getNodeTypeName(nodeProperties.type) }}属性</h4>
            
            <template v-for="prop in nodeSpecificProperties" :key="prop.key">
              <!-- 文本输入 -->
              <el-form-item v-if="prop.type === 'text'" :label="prop.label">
                <el-input 
                  v-model="nodeProperties.properties[prop.key]" 
                  :placeholder="prop.placeholder"
                  @input="handlePropertyChange(`properties.${prop.key}`, $event)"
                />
              </el-form-item>
              
              <!-- 多行文本 -->
              <el-form-item v-else-if="prop.type === 'textarea'" :label="prop.label">
                <el-input 
                  v-model="nodeProperties.properties[prop.key]" 
                  type="textarea"
                  :rows="prop.rows || 3"
                  :placeholder="prop.placeholder"
                  @input="handlePropertyChange(`properties.${prop.key}`, $event)"
                />
              </el-form-item>
              
              <!-- 数字输入 -->
              <el-form-item v-else-if="prop.type === 'number'" :label="prop.label">
                <el-input-number 
                  v-model="nodeProperties.properties[prop.key]" 
                  :min="prop.min"
                  :max="prop.max"
                  controls-position="right"
                  @change="handlePropertyChange(`properties.${prop.key}`, $event)"
                />
              </el-form-item>
              
              <!-- 选择器 -->
              <el-form-item v-else-if="prop.type === 'select'" :label="prop.label">
                <el-select 
                  v-model="nodeProperties.properties[prop.key]"
                  :placeholder="prop.placeholder"
                  @change="handlePropertyChange(`properties.${prop.key}`, $event)"
                >
                  <el-option 
                    v-for="option in prop.options" 
                    :key="option.value" 
                    :label="option.label" 
                    :value="option.value" 
                  />
                </el-select>
              </el-form-item>
              
              <!-- 开关 -->
              <el-form-item v-else-if="prop.type === 'switch'" :label="prop.label">
                <el-switch 
                  v-model="nodeProperties.properties[prop.key]"
                  @change="handlePropertyChange(`properties.${prop.key}`, $event)"
                />
              </el-form-item>
            </template>
          </div>
        </el-form>
      </div>
      
      <!-- 连线属性 -->
      <div v-else class="property-form">
        <el-form :model="edgeProperties" label-position="top" size="small">
          <div class="property-section">
            <h4 class="section-title">连线信息</h4>
            
            <el-form-item label="连线ID">
              <el-input v-model="edgeProperties.id" disabled />
            </el-form-item>
            
            <el-form-item label="连线标签">
              <el-input 
                v-model="edgeProperties.label" 
                placeholder="请输入连线标签"
                @input="handlePropertyChange('label', $event)"
              />
            </el-form-item>
            
            <el-form-item label="条件表达式">
              <el-input 
                v-model="edgeProperties.condition" 
                type="textarea"
                :rows="3"
                placeholder="请输入条件表达式"
                @input="handlePropertyChange('condition', $event)"
              />
            </el-form-item>
            
            <el-form-item label="源节点">
              <el-input v-model="edgeProperties.sourceNodeId" disabled />
            </el-form-item>
            
            <el-form-item label="目标节点">
              <el-input v-model="edgeProperties.targetNodeId" disabled />
            </el-form-item>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { WorkflowNode, WorkflowEdge, NodeType } from '@/types/workflow'

interface Props {
  selectedElement?: WorkflowNode | WorkflowEdge | null
}

const props = withDefaults(defineProps<Props>(), {
  selectedElement: null
})

const emit = defineEmits<{
  propertyChange: [property: string, value: any]
}>()

// 节点属性
const nodeProperties = ref<WorkflowNode>({
  id: '',
  type: 'task' as NodeType,
  name: '',
  description: '',
  x: 0,
  y: 0,
  width: 120,
  height: 60,
  properties: {}
})

// 连线属性
const edgeProperties = ref<WorkflowEdge & { condition?: string }>({
  id: '',
  sourceNodeId: '',
  targetNodeId: '',
  label: '',
  condition: '',
  properties: {}
})

// 节点类型选项
const nodeTypes = [
  { label: '开始节点', value: 'start' },
  { label: '结束节点', value: 'end' },
  { label: '任务节点', value: 'task' },
  { label: '决策节点', value: 'decision' },
  { label: '并行网关', value: 'parallel' },
  { label: '合并网关', value: 'merge' },
  { label: '定时器', value: 'timer' },
  { label: 'HTTP请求', value: 'http' },
  { label: '邮件发送', value: 'email' },
  { label: '脚本执行', value: 'script' }
]

// 判断是否为节点
const isNode = computed(() => {
  return props.selectedElement && 'type' in props.selectedElement
})

// 获取节点特定属性配置
const nodeSpecificProperties = computed(() => {
  if (!isNode.value) return []
  
  const nodeType = (props.selectedElement as WorkflowNode).type
  
  switch (nodeType) {
    case 'task':
      return [
        {
          key: 'assignee',
          label: '执行人',
          type: 'text',
          placeholder: '请输入执行人'
        },
        {
          key: 'dueDate',
          label: '截止时间',
          type: 'text',
          placeholder: '请输入截止时间'
        },
        {
          key: 'priority',
          label: '优先级',
          type: 'select',
          placeholder: '请选择优先级',
          options: [
            { label: '低', value: 'low' },
            { label: '中', value: 'medium' },
            { label: '高', value: 'high' }
          ]
        }
      ]
    
    case 'decision':
      return [
        {
          key: 'condition',
          label: '判断条件',
          type: 'textarea',
          placeholder: '请输入判断条件表达式',
          rows: 4
        }
      ]
    
    case 'timer':
      return [
        {
          key: 'duration',
          label: '延时时长(秒)',
          type: 'number',
          min: 1
        },
        {
          key: 'repeat',
          label: '重复执行',
          type: 'switch'
        }
      ]
    
    case 'http':
      return [
        {
          key: 'url',
          label: '请求URL',
          type: 'text',
          placeholder: 'https://api.example.com/endpoint'
        },
        {
          key: 'method',
          label: '请求方法',
          type: 'select',
          options: [
            { label: 'GET', value: 'GET' },
            { label: 'POST', value: 'POST' },
            { label: 'PUT', value: 'PUT' },
            { label: 'DELETE', value: 'DELETE' }
          ]
        },
        {
          key: 'headers',
          label: '请求头',
          type: 'textarea',
          placeholder: '{"Content-Type": "application/json"}',
          rows: 3
        },
        {
          key: 'body',
          label: '请求体',
          type: 'textarea',
          placeholder: '请求体内容',
          rows: 4
        }
      ]
    
    case 'email':
      return [
        {
          key: 'to',
          label: '收件人',
          type: 'text',
          placeholder: '<EMAIL>'
        },
        {
          key: 'subject',
          label: '邮件主题',
          type: 'text',
          placeholder: '请输入邮件主题'
        },
        {
          key: 'template',
          label: '邮件模板',
          type: 'select',
          placeholder: '请选择邮件模板',
          options: [
            { label: '默认模板', value: 'default' },
            { label: '通知模板', value: 'notification' },
            { label: '警告模板', value: 'warning' }
          ]
        }
      ]
    
    case 'script':
      return [
        {
          key: 'language',
          label: '脚本语言',
          type: 'select',
          options: [
            { label: 'Groovy', value: 'groovy' },
            { label: 'JavaScript', value: 'javascript' },
            { label: 'Python', value: 'python' }
          ]
        },
        {
          key: 'script',
          label: '脚本内容',
          type: 'textarea',
          placeholder: '请输入脚本代码',
          rows: 8
        }
      ]
    
    default:
      return []
  }
})

// 获取节点类型名称
const getNodeTypeName = (type: string): string => {
  const typeMap: Record<string, string> = {
    start: '开始节点',
    end: '结束节点',
    task: '任务节点',
    decision: '决策节点',
    parallel: '并行网关',
    merge: '合并网关',
    timer: '定时器',
    http: 'HTTP请求',
    email: '邮件发送',
    script: '脚本执行'
  }
  return typeMap[type] || type
}

// 处理属性变化
const handlePropertyChange = (property: string, value: any) => {
  emit('propertyChange', property, value)
}

// 清除选择
const clearSelection = () => {
  emit('propertyChange', 'clear', null)
}

// 监听选中元素变化
watch(() => props.selectedElement, (newElement) => {
  if (!newElement) return
  
  if ('type' in newElement) {
    // 节点
    nodeProperties.value = {
      ...newElement,
      properties: newElement.properties || {}
    }
  } else {
    // 连线
    edgeProperties.value = {
      ...newElement,
      condition: newElement.properties?.condition || '',
      properties: newElement.properties || {}
    }
  }
}, { immediate: true, deep: true })
</script>

<style lang="scss" scoped>
.property-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  
  .panel-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0;
  }
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--el-text-color-placeholder);
  
  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
  
  .empty-text {
    font-size: 14px;
    margin: 0;
  }
}

.property-form {
  .el-form-item {
    margin-bottom: 16px;
    
    :deep(.el-form-item__label) {
      font-size: 12px;
      font-weight: 500;
      color: var(--el-text-color-regular);
      margin-bottom: 4px;
    }
  }
  
  .el-input,
  .el-select,
  .el-input-number {
    width: 100%;
  }
}

.property-section {
  margin-bottom: 24px;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .section-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }
}
</style>
