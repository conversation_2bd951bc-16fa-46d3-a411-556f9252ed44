<template>
  <div class="workflow-canvas">
    <div ref="canvasContainer" class="canvas-container"></div>
    
    <!-- 小地图 -->
    <div v-show="showMinimap" class="minimap-container">
      <div ref="minimapContainer" class="minimap"></div>
    </div>
    
    <!-- 缩放控制器 -->
    <div class="zoom-controls">
      <el-button-group>
        <el-button size="small" @click="zoomIn">
          <el-icon><Plus /></el-icon>
        </el-button>
        <el-button size="small" @click="zoomOut">
          <el-icon><Minus /></el-icon>
        </el-button>
        <el-button size="small" @click="zoomToFit">
          <el-icon><FullScreen /></el-icon>
        </el-button>
      </el-button-group>
    </div>
    
    <!-- 网格控制器 -->
    <div class="grid-controls">
      <el-button size="small" @click="toggleGrid">
        <el-icon><Grid /></el-icon>
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue'
import LogicFlow from '@logicflow/core'
import { Menu, DndPanel, SelectionSelect, Snapshot } from '@logicflow/extension'
import type { GraphModel, NodeConfig, EdgeConfig } from '@logicflow/core'
import type { WorkflowGraph, WorkflowNode, WorkflowEdge } from '@/types/workflow'

// 导入 LogicFlow 样式
import '@logicflow/core/dist/style/index.css'
import '@logicflow/extension/lib/style/index.css'

interface Props {
  workflowData?: WorkflowGraph
}

const props = withDefaults(defineProps<Props>(), {
  workflowData: () => ({ nodes: [], edges: [], variables: {}, settings: {} })
})

const emit = defineEmits<{
  canvasReady: [canvas: LogicFlow]
  selectionChange: [element: WorkflowNode | WorkflowEdge | null]
  graphChange: [graph: WorkflowGraph]
  nodeAdd: [node: WorkflowNode]
  nodeDelete: [nodeId: string]
  edgeAdd: [edge: WorkflowEdge]
  edgeDelete: [edgeId: string]
}>()

const canvasContainer = ref<HTMLElement>()
const minimapContainer = ref<HTMLElement>()
const showMinimap = ref(false)
const showGrid = ref(true)

let logicFlow: LogicFlow | null = null

// 初始化 LogicFlow
const initLogicFlow = async () => {
  if (!canvasContainer.value) return
  
  try {
    // 创建 LogicFlow 实例
    logicFlow = new LogicFlow({
      container: canvasContainer.value,
      width: canvasContainer.value.offsetWidth,
      height: canvasContainer.value.offsetHeight,
      background: {
        backgroundImage: showGrid.value ? 'url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxwYXR0ZXJuIGlkPSJncmlkIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiPgogICAgICA8cGF0aCBkPSJNIDQwIDAgTCAwIDAgMCA0MCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZTBlMGUwIiBzdHJva2Utd2lkdGg9IjEiLz4KICAgIDwvcGF0dGVybj4KICA8L2RlZnM+CiAgPHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0idXJsKCNncmlkKSIvPgo8L3N2Zz4=)' : 'none'
      },
      grid: {
        size: 20,
        visible: showGrid.value,
        type: 'dot',
        config: {
          color: '#ababab',
          thickness: 1
        }
      },
      keyboard: {
        enabled: true
      },
      style: {
        rect: {
          rx: 6,
          ry: 6,
          strokeWidth: 2
        },
        circle: {
          strokeWidth: 2
        },
        nodeText: {
          overflowMode: 'ellipsis',
          maxWidth: 100
        },
        edgeText: {
          textAnchor: 'middle',
          dominantBaseline: 'middle'
        }
      },
      // 启用插件
      plugins: [Menu, DndPanel, SelectionSelect, Snapshot]
    })
    
    // 注册自定义节点
    registerCustomNodes()
    
    // 设置事件监听
    setupEventListeners()
    
    // 加载初始数据
    if (props.workflowData.nodes.length > 0 || props.workflowData.edges.length > 0) {
      loadWorkflowData(props.workflowData)
    }
    
    emit('canvasReady', logicFlow)
    
  } catch (error) {
    console.error('初始化 LogicFlow 失败:', error)
  }
}

// 注册自定义节点
const registerCustomNodes = () => {
  if (!logicFlow) return
  
  // 开始节点
  logicFlow.register('start', ({ CircleNode, CircleNodeModel }) => {
    class StartNode extends CircleNode {}
    class StartNodeModel extends CircleNodeModel {
      initNodeData(data: any) {
        super.initNodeData(data)
        this.r = 25
        this.fill = '#67C23A'
        this.stroke = '#67C23A'
        this.strokeWidth = 2
      }
      
      getConnectedTargetRules() {
        return {
          message: '开始节点只能连出，不能连入',
          validate: () => false
        }
      }
    }
    return {
      view: StartNode,
      model: StartNodeModel
    }
  })
  
  // 结束节点
  logicFlow.register('end', ({ CircleNode, CircleNodeModel }) => {
    class EndNode extends CircleNode {}
    class EndNodeModel extends CircleNodeModel {
      initNodeData(data: any) {
        super.initNodeData(data)
        this.r = 25
        this.fill = '#F56C6C'
        this.stroke = '#F56C6C'
        this.strokeWidth = 2
      }
      
      getConnectedSourceRules() {
        return {
          message: '结束节点只能连入，不能连出',
          validate: () => false
        }
      }
    }
    return {
      view: EndNode,
      model: EndNodeModel
    }
  })
  
  // 任务节点
  logicFlow.register('task', ({ RectNode, RectNodeModel }) => {
    class TaskNode extends RectNode {}
    class TaskNodeModel extends RectNodeModel {
      initNodeData(data: any) {
        super.initNodeData(data)
        this.width = 120
        this.height = 60
        this.fill = '#409EFF'
        this.stroke = '#409EFF'
        this.strokeWidth = 2
        this.radius = 6
      }
    }
    return {
      view: TaskNode,
      model: TaskNodeModel
    }
  })
  
  // 决策节点
  logicFlow.register('decision', ({ PolygonNode, PolygonNodeModel }) => {
    class DecisionNode extends PolygonNode {}
    class DecisionNodeModel extends PolygonNodeModel {
      initNodeData(data: any) {
        super.initNodeData(data)
        this.points = [
          [60, 0],
          [120, 30],
          [60, 60],
          [0, 30]
        ]
        this.fill = '#E6A23C'
        this.stroke = '#E6A23C'
        this.strokeWidth = 2
      }
    }
    return {
      view: DecisionNode,
      model: DecisionNodeModel
    }
  })
}

// 设置事件监听
const setupEventListeners = () => {
  if (!logicFlow) return
  
  // 节点选择事件
  logicFlow.on('node:click', ({ data }) => {
    const node = convertToWorkflowNode(data)
    emit('selectionChange', node)
  })
  
  // 边选择事件
  logicFlow.on('edge:click', ({ data }) => {
    const edge = convertToWorkflowEdge(data)
    emit('selectionChange', edge)
  })
  
  // 画布点击事件
  logicFlow.on('blank:click', () => {
    emit('selectionChange', null)
  })
  
  // 图形变化事件
  logicFlow.on('graph:updated', () => {
    const graphData = logicFlow!.getGraphData()
    const workflowGraph = convertToWorkflowGraph(graphData)
    emit('graphChange', workflowGraph)
  })
  
  // 节点添加事件
  logicFlow.on('node:add', ({ data }) => {
    const node = convertToWorkflowNode(data)
    emit('nodeAdd', node)
  })
  
  // 节点删除事件
  logicFlow.on('node:delete', ({ data }) => {
    emit('nodeDelete', data.id)
  })
  
  // 边添加事件
  logicFlow.on('edge:add', ({ data }) => {
    const edge = convertToWorkflowEdge(data)
    emit('edgeAdd', edge)
  })
  
  // 边删除事件
  logicFlow.on('edge:delete', ({ data }) => {
    emit('edgeDelete', data.id)
  })
}

// 加载工作流数据
const loadWorkflowData = (workflowData: WorkflowGraph) => {
  if (!logicFlow) return
  
  const graphData = {
    nodes: workflowData.nodes.map(node => ({
      id: node.id,
      type: node.type,
      x: node.x,
      y: node.y,
      text: node.name,
      properties: node.properties || {}
    })),
    edges: workflowData.edges.map(edge => ({
      id: edge.id,
      sourceNodeId: edge.sourceNodeId,
      targetNodeId: edge.targetNodeId,
      text: edge.label || '',
      properties: edge.properties || {}
    }))
  }
  
  logicFlow.render(graphData)
}

// 转换为工作流节点
const convertToWorkflowNode = (data: NodeConfig): WorkflowNode => {
  return {
    id: data.id!,
    type: data.type as any,
    name: data.text || '',
    x: data.x!,
    y: data.y!,
    width: (data as any).width,
    height: (data as any).height,
    properties: data.properties || {}
  }
}

// 转换为工作流边
const convertToWorkflowEdge = (data: EdgeConfig): WorkflowEdge => {
  return {
    id: data.id!,
    sourceNodeId: data.sourceNodeId!,
    targetNodeId: data.targetNodeId!,
    label: data.text || '',
    properties: data.properties || {}
  }
}

// 转换为工作流图形
const convertToWorkflowGraph = (graphData: any): WorkflowGraph => {
  return {
    nodes: graphData.nodes.map((node: any) => convertToWorkflowNode(node)),
    edges: graphData.edges.map((edge: any) => convertToWorkflowEdge(edge)),
    variables: props.workflowData.variables || {},
    settings: props.workflowData.settings || {}
  }
}

// 缩放操作
const zoomIn = () => {
  logicFlow?.zoom(true)
}

const zoomOut = () => {
  logicFlow?.zoom(false)
}

const zoomToFit = () => {
  logicFlow?.fitView()
}

// 切换网格
const toggleGrid = () => {
  showGrid.value = !showGrid.value
  if (logicFlow) {
    logicFlow.updateEditConfig({
      grid: {
        visible: showGrid.value
      }
    })
  }
}

// 切换小地图
const toggleMinimap = () => {
  showMinimap.value = !showMinimap.value
}

// 撤销/重做
const undo = () => {
  logicFlow?.undo()
}

const redo = () => {
  logicFlow?.redo()
}

// 添加节点
const addNode = (nodeType: string, position: { x: number; y: number }) => {
  if (!logicFlow) return
  
  const nodeId = `${nodeType}_${Date.now()}`
  logicFlow.addNode({
    id: nodeId,
    type: nodeType,
    x: position.x,
    y: position.y,
    text: getNodeName(nodeType)
  })
}

// 获取节点名称
const getNodeName = (nodeType: string): string => {
  const nodeNames: Record<string, string> = {
    start: '开始',
    end: '结束',
    task: '任务',
    decision: '决策'
  }
  return nodeNames[nodeType] || nodeType
}

// 处理拖放
const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  
  if (!event.dataTransfer || !logicFlow) return
  
  try {
    const nodeData = JSON.parse(event.dataTransfer.getData('application/json'))
    const rect = canvasContainer.value!.getBoundingClientRect()
    const position = {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top
    }
    
    addNode(nodeData.type, position)
  } catch (error) {
    console.error('处理拖放失败:', error)
  }
}

const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  event.dataTransfer!.dropEffect = 'copy'
}

// 窗口大小变化处理
const handleResize = () => {
  if (logicFlow && canvasContainer.value) {
    logicFlow.resize(canvasContainer.value.offsetWidth, canvasContainer.value.offsetHeight)
  }
}

// 暴露方法
defineExpose({
  zoomIn,
  zoomOut,
  zoomToFit,
  toggleGrid,
  toggleMinimap,
  undo,
  redo,
  addNode
})

onMounted(async () => {
  await nextTick()
  await initLogicFlow()
  
  // 添加拖放事件监听
  if (canvasContainer.value) {
    canvasContainer.value.addEventListener('drop', handleDrop)
    canvasContainer.value.addEventListener('dragover', handleDragOver)
  }
  
  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  if (canvasContainer.value) {
    canvasContainer.value.removeEventListener('drop', handleDrop)
    canvasContainer.value.removeEventListener('dragover', handleDragOver)
  }
  
  window.removeEventListener('resize', handleResize)
  
  if (logicFlow) {
    logicFlow.destroy()
  }
})
</script>

<style lang="scss" scoped>
.workflow-canvas {
  position: relative;
  width: 100%;
  height: 100%;
  background: var(--el-bg-color);
}

.canvas-container {
  width: 100%;
  height: 100%;
}

.minimap-container {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 200px;
  height: 150px;
  background: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
  box-shadow: var(--el-box-shadow-light);
  
  .minimap {
    width: 100%;
    height: 100%;
  }
}

.zoom-controls {
  position: absolute;
  bottom: 16px;
  right: 16px;
  
  .el-button-group {
    .el-button {
      padding: 8px;
      
      .el-icon {
        font-size: 16px;
      }
    }
  }
}

.grid-controls {
  position: absolute;
  bottom: 16px;
  right: 120px;
  
  .el-button {
    padding: 8px;
    
    .el-icon {
      font-size: 16px;
    }
  }
}

// LogicFlow 样式覆盖
:deep(.lf-canvas-overlay) {
  .lf-node-text {
    font-size: 12px;
    font-weight: 500;
  }
  
  .lf-edge-text {
    font-size: 11px;
  }
}
</style>
