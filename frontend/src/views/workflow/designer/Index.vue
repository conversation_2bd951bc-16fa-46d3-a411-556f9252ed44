<template>
  <div class="workflow-designer">
    <!-- 工具栏 -->
    <div class="designer-toolbar">
      <div class="toolbar-left">
        <el-button-group>
          <el-button @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
          <el-button @click="saveWorkflow" :loading="saving">
            <el-icon><Document /></el-icon>
            保存
          </el-button>
          <el-button @click="validateWorkflow">
            <el-icon><CircleCheck /></el-icon>
            验证
          </el-button>
        </el-button-group>
        
        <el-divider direction="vertical" />
        
        <el-button-group>
          <el-button @click="undo" :disabled="!canUndo">
            <el-icon><RefreshLeft /></el-icon>
            撤销
          </el-button>
          <el-button @click="redo" :disabled="!canRedo">
            <el-icon><RefreshRight /></el-icon>
            重做
          </el-button>
        </el-button-group>
        
        <el-divider direction="vertical" />
        
        <el-button-group>
          <el-button @click="zoomIn">
            <el-icon><ZoomIn /></el-icon>
          </el-button>
          <el-button @click="zoomOut">
            <el-icon><ZoomOut /></el-icon>
          </el-button>
          <el-button @click="zoomFit">
            <el-icon><FullScreen /></el-icon>
            适应画布
          </el-button>
        </el-button-group>
      </div>
      
      <div class="toolbar-center">
        <span class="workflow-title">{{ workflowName }}</span>
        <el-tag v-if="isDirty" type="warning" size="small">未保存</el-tag>
      </div>
      
      <div class="toolbar-right">
        <el-button-group>
          <el-button @click="toggleMinimap">
            <el-icon><View /></el-icon>
            小地图
          </el-button>
          <el-button @click="toggleGrid">
            <el-icon><Grid /></el-icon>
            网格
          </el-button>
        </el-button-group>
        
        <el-divider direction="vertical" />
        
        <el-dropdown @command="handleMenuCommand">
          <el-button>
            <el-icon><More /></el-icon>
            更多
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="export">
                <el-icon><Download /></el-icon>
                导出
              </el-dropdown-item>
              <el-dropdown-item command="import">
                <el-icon><Upload /></el-icon>
                导入
              </el-dropdown-item>
              <el-dropdown-item divided command="preview">
                <el-icon><View /></el-icon>
                预览
              </el-dropdown-item>
              <el-dropdown-item command="fullscreen">
                <el-icon><FullScreen /></el-icon>
                全屏
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    
    <!-- 主要内容区 -->
    <div class="designer-content">
      <!-- 左侧节点面板 -->
      <div class="designer-sidebar designer-sidebar--left">
        <NodePalette @node-drag="handleNodeDrag" />
      </div>
      
      <!-- 中间画布区域 -->
      <div class="designer-canvas-container">
        <WorkflowCanvas
          ref="canvasRef"
          :workflow-data="workflowData"
          @canvas-ready="handleCanvasReady"
          @selection-change="handleSelectionChange"
          @graph-change="handleGraphChange"
        />
      </div>
      
      <!-- 右侧属性面板 -->
      <div class="designer-sidebar designer-sidebar--right">
        <PropertyPanel
          :selected-element="selectedElement"
          @property-change="handlePropertyChange"
        />
      </div>
    </div>
    
    <!-- 底部状态栏 -->
    <div class="designer-statusbar">
      <div class="statusbar-left">
        <span>节点: {{ nodeCount }}</span>
        <el-divider direction="vertical" />
        <span>连线: {{ edgeCount }}</span>
        <el-divider direction="vertical" />
        <span>缩放: {{ Math.round(zoomLevel * 100) }}%</span>
      </div>
      
      <div class="statusbar-right">
        <span>{{ statusMessage }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import NodePalette from './components/NodePalette.vue'
import WorkflowCanvas from './components/WorkflowCanvas.vue'
import PropertyPanel from './components/PropertyPanel.vue'
import type { WorkflowGraph, WorkflowNode, WorkflowEdge } from '@/types/workflow'

const route = useRoute()
const router = useRouter()

const canvasRef = ref()
const saving = ref(false)
const isDirty = ref(false)
const selectedElement = ref<WorkflowNode | WorkflowEdge | null>(null)
const workflowData = ref<WorkflowGraph>({
  nodes: [],
  edges: [],
  variables: {},
  settings: {}
})

// 计算属性
const workflowName = computed(() => {
  return route.params.name as string || '未命名工作流'
})

const nodeCount = computed(() => workflowData.value.nodes.length)
const edgeCount = computed(() => workflowData.value.edges.length)
const zoomLevel = ref(1)
const canUndo = ref(false)
const canRedo = ref(false)
const statusMessage = ref('就绪')

// 返回上一页
const goBack = async () => {
  if (isDirty.value) {
    try {
      await ElMessageBox.confirm('有未保存的更改，确定要离开吗？', '提示', {
        confirmButtonText: '离开',
        cancelButtonText: '取消',
        type: 'warning'
      })
    } catch {
      return
    }
  }
  
  router.back()
}

// 保存工作流
const saveWorkflow = async () => {
  try {
    saving.value = true
    statusMessage.value = '保存中...'
    
    // 这里调用保存 API
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟保存
    
    isDirty.value = false
    statusMessage.value = '保存成功'
    ElMessage.success('工作流保存成功')
  } catch (error) {
    console.error('保存失败:', error)
    statusMessage.value = '保存失败'
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
    setTimeout(() => {
      statusMessage.value = '就绪'
    }, 2000)
  }
}

// 验证工作流
const validateWorkflow = () => {
  statusMessage.value = '验证中...'
  
  // 这里实现验证逻辑
  setTimeout(() => {
    statusMessage.value = '验证通过'
    ElMessage.success('工作流验证通过')
    
    setTimeout(() => {
      statusMessage.value = '就绪'
    }, 2000)
  }, 1000)
}

// 撤销/重做
const undo = () => {
  canvasRef.value?.undo()
}

const redo = () => {
  canvasRef.value?.redo()
}

// 缩放操作
const zoomIn = () => {
  canvasRef.value?.zoomIn()
}

const zoomOut = () => {
  canvasRef.value?.zoomOut()
}

const zoomFit = () => {
  canvasRef.value?.zoomToFit()
}

// 切换小地图
const toggleMinimap = () => {
  canvasRef.value?.toggleMinimap()
}

// 切换网格
const toggleGrid = () => {
  canvasRef.value?.toggleGrid()
}

// 处理菜单命令
const handleMenuCommand = (command: string) => {
  switch (command) {
    case 'export':
      exportWorkflow()
      break
    case 'import':
      importWorkflow()
      break
    case 'preview':
      previewWorkflow()
      break
    case 'fullscreen':
      toggleFullscreen()
      break
  }
}

// 导出工作流
const exportWorkflow = () => {
  ElMessage.info('导出功能开发中...')
}

// 导入工作流
const importWorkflow = () => {
  ElMessage.info('导入功能开发中...')
}

// 预览工作流
const previewWorkflow = () => {
  ElMessage.info('预览功能开发中...')
}

// 切换全屏
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
  } else {
    document.exitFullscreen()
  }
}

// 处理节点拖拽
const handleNodeDrag = (nodeType: string) => {
  console.log('节点拖拽:', nodeType)
}

// 画布就绪
const handleCanvasReady = (canvas: any) => {
  console.log('画布就绪:', canvas)
}

// 选择变化
const handleSelectionChange = (element: WorkflowNode | WorkflowEdge | null) => {
  selectedElement.value = element
}

// 图形变化
const handleGraphChange = (graph: WorkflowGraph) => {
  workflowData.value = graph
  isDirty.value = true
}

// 属性变化
const handlePropertyChange = (property: string, value: any) => {
  if (selectedElement.value) {
    (selectedElement.value as any)[property] = value
    isDirty.value = true
  }
}

// 键盘快捷键
const handleKeydown = (e: KeyboardEvent) => {
  if (e.ctrlKey || e.metaKey) {
    switch (e.key) {
      case 's':
        e.preventDefault()
        saveWorkflow()
        break
      case 'z':
        e.preventDefault()
        if (e.shiftKey) {
          redo()
        } else {
          undo()
        }
        break
    }
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
  
  // 加载工作流数据
  loadWorkflowData()
})

onBeforeUnmount(() => {
  document.removeEventListener('keydown', handleKeydown)
})

// 加载工作流数据
const loadWorkflowData = async () => {
  try {
    const workflowId = route.params.id as string
    if (workflowId) {
      // 这里调用 API 加载工作流数据
      statusMessage.value = '加载中...'
      
      // 模拟加载
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      statusMessage.value = '加载完成'
      setTimeout(() => {
        statusMessage.value = '就绪'
      }, 1000)
    }
  } catch (error) {
    console.error('加载工作流数据失败:', error)
    statusMessage.value = '加载失败'
    ElMessage.error('加载工作流数据失败')
  }
}
</script>

<style lang="scss" scoped>
.workflow-designer {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--el-bg-color-page);
}

.designer-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 56px;
  padding: 0 16px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-lighter);
  
  .toolbar-left,
  .toolbar-right {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .toolbar-center {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .workflow-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }
}

.designer-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.designer-sidebar {
  width: 280px;
  background: var(--el-bg-color);
  border-right: 1px solid var(--el-border-color-lighter);
  
  &--right {
    border-right: none;
    border-left: 1px solid var(--el-border-color-lighter);
  }
}

.designer-canvas-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.designer-statusbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 32px;
  padding: 0 16px;
  background: var(--el-bg-color);
  border-top: 1px solid var(--el-border-color-lighter);
  font-size: 12px;
  color: var(--el-text-color-regular);
  
  .statusbar-left {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .designer-sidebar {
    width: 240px;
  }
}

@media (max-width: 768px) {
  .designer-toolbar {
    .toolbar-left,
    .toolbar-right {
      gap: 8px;
      
      .el-button-group {
        .el-button {
          padding: 8px 12px;
          
          span {
            display: none;
          }
        }
      }
    }
  }
  
  .designer-sidebar {
    position: absolute;
    top: 0;
    bottom: 0;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s;
    
    &--right {
      right: 0;
      transform: translateX(100%);
    }
    
    &.show {
      transform: translateX(0);
    }
  }
}
</style>
