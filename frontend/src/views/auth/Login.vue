<template>
  <div class="login-page">
    <div class="login-container">
      <!-- 左侧背景 -->
      <div class="login-banner">
        <div class="login-banner__content">
          <h1 class="login-banner__title">工作流引擎</h1>
          <p class="login-banner__subtitle">企业级可视化工作流设计与执行平台</p>
          <div class="login-banner__features">
            <div class="feature-item">
              <el-icon><Flow /></el-icon>
              <span>可视化设计</span>
            </div>
            <div class="feature-item">
              <el-icon><Monitor /></el-icon>
              <span>实时监控</span>
            </div>
            <div class="feature-item">
              <el-icon><Setting /></el-icon>
              <span>灵活配置</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右侧登录表单 -->
      <div class="login-form-container">
        <div class="login-form">
          <div class="login-header">
            <img src="/workflow-icon.svg" alt="Logo" class="login-logo" />
            <h2 class="login-title">用户登录</h2>
            <p class="login-description">欢迎使用工作流引擎管理平台</p>
          </div>
          
          <el-form
            ref="loginFormRef"
            :model="loginForm"
            :rules="loginRules"
            class="login-form-content"
            @keyup.enter="handleLogin"
          >
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                placeholder="请输入用户名或邮箱"
                size="large"
                :prefix-icon="User"
                clearable
              />
            </el-form-item>
            
            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="请输入密码"
                size="large"
                :prefix-icon="Lock"
                show-password
                clearable
              />
            </el-form-item>
            
            <el-form-item>
              <div class="login-options">
                <el-checkbox v-model="loginForm.rememberMe">
                  记住我
                </el-checkbox>
                <el-link type="primary" @click="showForgotPassword">
                  忘记密码？
                </el-link>
              </div>
            </el-form-item>
            
            <el-form-item>
              <el-button
                type="primary"
                size="large"
                :loading="loading"
                @click="handleLogin"
                class="login-button"
              >
                {{ loading ? '登录中...' : '登录' }}
              </el-button>
            </el-form-item>
            
            <el-form-item>
              <div class="login-footer">
                <span>还没有账户？</span>
                <el-link type="primary" @click="goToRegister">
                  立即注册
                </el-link>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    
    <!-- 忘记密码对话框 -->
    <el-dialog
      v-model="forgotPasswordVisible"
      title="忘记密码"
      width="400px"
      :before-close="closeForgotPassword"
    >
      <el-form
        ref="forgotPasswordFormRef"
        :model="forgotPasswordForm"
        :rules="forgotPasswordRules"
      >
        <el-form-item prop="email">
          <el-input
            v-model="forgotPasswordForm.email"
            placeholder="请输入邮箱地址"
            :prefix-icon="Message"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="closeForgotPassword">取消</el-button>
        <el-button
          type="primary"
          :loading="forgotPasswordLoading"
          @click="handleForgotPassword"
        >
          发送重置邮件
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { authApi } from '@/api/auth'
import { ElMessage, ElForm } from 'element-plus'
import { User, Lock, Message } from '@element-plus/icons-vue'
import type { LoginRequest } from '@/types/auth'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const loginFormRef = ref<InstanceType<typeof ElForm>>()
const forgotPasswordFormRef = ref<InstanceType<typeof ElForm>>()

const loading = ref(false)
const forgotPasswordVisible = ref(false)
const forgotPasswordLoading = ref(false)

// 登录表单
const loginForm = reactive<LoginRequest>({
  username: '',
  password: '',
  rememberMe: false
})

// 忘记密码表单
const forgotPasswordForm = reactive({
  email: ''
})

// 登录表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名或邮箱', trigger: 'blur' },
    { min: 3, max: 50, message: '长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 100, message: '长度在 6 到 100 个字符', trigger: 'blur' }
  ]
}

// 忘记密码表单验证规则
const forgotPasswordRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    const valid = await loginFormRef.value.validate()
    if (!valid) return
    
    loading.value = true
    
    const success = await userStore.login(loginForm)
    if (success) {
      // 登录成功，跳转到目标页面
      const redirect = route.query.redirect as string || '/dashboard'
      router.push(redirect)
    }
  } catch (error) {
    console.error('登录失败:', error)
  } finally {
    loading.value = false
  }
}

// 显示忘记密码对话框
const showForgotPassword = () => {
  forgotPasswordVisible.value = true
  forgotPasswordForm.email = ''
}

// 关闭忘记密码对话框
const closeForgotPassword = () => {
  forgotPasswordVisible.value = false
  forgotPasswordFormRef.value?.resetFields()
}

// 处理忘记密码
const handleForgotPassword = async () => {
  if (!forgotPasswordFormRef.value) return
  
  try {
    const valid = await forgotPasswordFormRef.value.validate()
    if (!valid) return
    
    forgotPasswordLoading.value = true
    
    await authApi.forgotPassword(forgotPasswordForm.email)
    ElMessage.success('重置邮件已发送，请查收邮箱')
    closeForgotPassword()
  } catch (error) {
    console.error('发送重置邮件失败:', error)
  } finally {
    forgotPasswordLoading.value = false
  }
}

// 跳转到注册页面
const goToRegister = () => {
  router.push('/register')
}

// 页面加载时的处理
onMounted(() => {
  // 如果已经登录，直接跳转
  if (userStore.isAuthenticated) {
    const redirect = route.query.redirect as string || '/dashboard'
    router.replace(redirect)
  }
  
  // 开发环境下自动填充表单
  if (import.meta.env.DEV) {
    loginForm.username = 'admin'
    loginForm.password = 'password123'
  }
})
</script>

<style lang="scss" scoped>
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-container {
  display: flex;
  width: 100%;
  max-width: 1000px;
  min-height: 600px;
  background: var(--el-bg-color);
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.login-banner {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  padding: 40px;
  
  &__content {
    text-align: center;
  }
  
  &__title {
    font-size: 36px;
    font-weight: 700;
    margin: 0 0 16px 0;
  }
  
  &__subtitle {
    font-size: 18px;
    opacity: 0.9;
    margin: 0 0 40px 0;
    line-height: 1.6;
  }
  
  &__features {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  
  .el-icon {
    font-size: 24px;
  }
}

.login-form-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.login-form {
  width: 100%;
  max-width: 400px;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.login-logo {
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
}

.login-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
}

.login-description {
  color: var(--el-text-color-regular);
  margin: 0;
}

.login-form-content {
  .el-form-item {
    margin-bottom: 24px;
  }
}

.login-options {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.login-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
}

.login-footer {
  text-align: center;
  color: var(--el-text-color-regular);
  
  .el-link {
    margin-left: 8px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
    max-width: 400px;
    min-height: auto;
  }
  
  .login-banner {
    padding: 30px 20px;
    
    &__title {
      font-size: 28px;
    }
    
    &__subtitle {
      font-size: 16px;
    }
    
    &__features {
      flex-direction: row;
      justify-content: center;
      gap: 30px;
      
      .feature-item {
        flex-direction: column;
        gap: 8px;
        font-size: 14px;
        
        .el-icon {
          font-size: 20px;
        }
      }
    }
  }
  
  .login-form-container {
    padding: 30px 20px;
  }
}
</style>
