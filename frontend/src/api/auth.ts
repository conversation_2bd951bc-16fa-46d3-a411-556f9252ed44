import { request } from '@/utils/request'
import type { 
  LoginRequest, 
  LoginResponse, 
  RegisterRequest, 
  RefreshTokenRequest,
  User 
} from '@/types/auth'

export const authApi = {
  /**
   * 用户登录
   */
  login(data: LoginRequest) {
    return request.post<LoginResponse>('/v1/auth/login', data, {
      skipAuth: true,
      showLoading: true,
      loadingText: '正在登录...'
    })
  },

  /**
   * 用户注册
   */
  register(data: RegisterRequest) {
    return request.post<User>('/v1/auth/register', data, {
      skipAuth: true,
      showLoading: true,
      loadingText: '正在注册...'
    })
  },

  /**
   * 用户登出
   */
  logout() {
    return request.post('/v1/auth/logout')
  },

  /**
   * 获取当前用户信息
   */
  getCurrentUser() {
    return request.get<User>('/v1/auth/me')
  },

  /**
   * 刷新令牌
   */
  refreshToken(data: RefreshTokenRequest) {
    return request.post<LoginResponse>('/v1/auth/refresh', data, {
      skipAuth: true
    })
  },

  /**
   * 验证令牌
   */
  validateToken(token: string) {
    return request.post<boolean>('/v1/auth/validate', null, {
      params: { token },
      skipAuth: true
    })
  },

  /**
   * 修改密码
   */
  changePassword(oldPassword: string, newPassword: string) {
    return request.post('/v1/auth/change-password', null, {
      params: { oldPassword, newPassword },
      showLoading: true,
      loadingText: '正在修改密码...'
    })
  },

  /**
   * 忘记密码
   */
  forgotPassword(email: string) {
    return request.post('/v1/auth/forgot-password', null, {
      params: { email },
      skipAuth: true,
      showLoading: true,
      loadingText: '正在发送重置邮件...'
    })
  },

  /**
   * 重置密码
   */
  resetPassword(token: string, newPassword: string) {
    return request.post('/v1/auth/reset-password', null, {
      params: { token, newPassword },
      skipAuth: true,
      showLoading: true,
      loadingText: '正在重置密码...'
    })
  }
}
