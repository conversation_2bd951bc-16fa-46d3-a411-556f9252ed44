import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置 NProgress
NProgress.configure({ 
  showSpinner: false,
  minimum: 0.2,
  easing: 'ease',
  speed: 500
})

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: {
      title: '用户登录',
      requiresAuth: false,
      hideInMenu: true
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/auth/Register.vue'),
    meta: {
      title: '用户注册',
      requiresAuth: false,
      hideInMenu: true
    }
  },
  {
    path: '/',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/Index.vue'),
        meta: {
          title: '工作台',
          icon: 'Dashboard',
          keepAlive: true
        }
      },
      {
        path: 'workflow',
        name: 'Workflow',
        meta: {
          title: '工作流管理',
          icon: 'Flow'
        },
        children: [
          {
            path: 'definitions',
            name: 'WorkflowDefinitions',
            component: () => import('@/views/workflow/definitions/Index.vue'),
            meta: {
              title: '流程定义',
              icon: 'Document',
              keepAlive: true
            }
          },
          {
            path: 'definitions/create',
            name: 'CreateWorkflowDefinition',
            component: () => import('@/views/workflow/definitions/Create.vue'),
            meta: {
              title: '创建流程',
              icon: 'Plus',
              hideInMenu: true,
              activeMenu: '/workflow/definitions'
            }
          },
          {
            path: 'definitions/:id/edit',
            name: 'EditWorkflowDefinition',
            component: () => import('@/views/workflow/definitions/Edit.vue'),
            meta: {
              title: '编辑流程',
              icon: 'Edit',
              hideInMenu: true,
              activeMenu: '/workflow/definitions'
            }
          },
          {
            path: 'definitions/:id/designer',
            name: 'WorkflowDesigner',
            component: () => import('@/views/workflow/designer/Index.vue'),
            meta: {
              title: '流程设计器',
              icon: 'Edit',
              hideInMenu: true,
              activeMenu: '/workflow/definitions',
              fullscreen: true
            }
          },
          {
            path: 'instances',
            name: 'WorkflowInstances',
            component: () => import('@/views/workflow/instances/Index.vue'),
            meta: {
              title: '流程实例',
              icon: 'List',
              keepAlive: true
            }
          },
          {
            path: 'instances/:id/detail',
            name: 'WorkflowInstanceDetail',
            component: () => import('@/views/workflow/instances/Detail.vue'),
            meta: {
              title: '实例详情',
              icon: 'View',
              hideInMenu: true,
              activeMenu: '/workflow/instances'
            }
          },
          {
            path: 'instances/:id/monitor',
            name: 'WorkflowInstanceMonitor',
            component: () => import('@/views/workflow/instances/Monitor.vue'),
            meta: {
              title: '实例监控',
              icon: 'Monitor',
              hideInMenu: true,
              activeMenu: '/workflow/instances'
            }
          }
        ]
      },
      {
        path: 'system',
        name: 'System',
        meta: {
          title: '系统管理',
          icon: 'Setting',
          roles: ['SYSTEM_ADMIN']
        },
        children: [
          {
            path: 'users',
            name: 'SystemUsers',
            component: () => import('@/views/system/users/Index.vue'),
            meta: {
              title: '用户管理',
              icon: 'User',
              keepAlive: true
            }
          },
          {
            path: 'roles',
            name: 'SystemRoles',
            component: () => import('@/views/system/roles/Index.vue'),
            meta: {
              title: '角色管理',
              icon: 'UserFilled',
              keepAlive: true
            }
          },
          {
            path: 'permissions',
            name: 'SystemPermissions',
            component: () => import('@/views/system/permissions/Index.vue'),
            meta: {
              title: '权限管理',
              icon: 'Lock',
              keepAlive: true
            }
          },
          {
            path: 'logs',
            name: 'SystemLogs',
            component: () => import('@/views/system/logs/Index.vue'),
            meta: {
              title: '系统日志',
              icon: 'Document',
              keepAlive: true
            }
          }
        ]
      },
      {
        path: 'profile',
        name: 'Profile',
        component: () => import('@/views/profile/Index.vue'),
        meta: {
          title: '个人中心',
          icon: 'User',
          hideInMenu: true
        }
      }
    ]
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: {
      title: '页面不存在',
      hideInMenu: true
    }
  },
  {
    path: '/403',
    name: 'Forbidden',
    component: () => import('@/views/error/403.vue'),
    meta: {
      title: '访问被拒绝',
      hideInMenu: true
    }
  },
  {
    path: '/500',
    name: 'ServerError',
    component: () => import('@/views/error/500.vue'),
    meta: {
      title: '服务器错误',
      hideInMenu: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 开始进度条
  NProgress.start()
  
  const userStore = useUserStore()
  const appStore = useAppStore()
  
  // 设置页面标题
  const title = to.meta?.title as string
  if (title) {
    document.title = `${title} - 工作流引擎`
  }
  
  // 检查是否需要认证
  if (to.meta?.requiresAuth !== false) {
    if (!userStore.isAuthenticated) {
      // 尝试从本地存储恢复登录状态
      const token = localStorage.getItem('access_token')
      if (token) {
        try {
          await userStore.getCurrentUser()
        } catch (error) {
          console.error('恢复登录状态失败:', error)
          userStore.logout()
          next('/login')
          return
        }
      } else {
        next('/login')
        return
      }
    }
    
    // 检查权限
    if (to.meta?.roles) {
      const roles = to.meta.roles as string[]
      if (!userStore.hasAnyRole(roles)) {
        next('/403')
        return
      }
    }
    
    if (to.meta?.permissions) {
      const permissions = to.meta.permissions as string[]
      if (!userStore.hasAnyPermission(permissions)) {
        next('/403')
        return
      }
    }
  }
  
  // 如果已登录用户访问登录页，重定向到首页
  if ((to.name === 'Login' || to.name === 'Register') && userStore.isAuthenticated) {
    next('/dashboard')
    return
  }
  
  next()
})

router.afterEach((to, from) => {
  // 结束进度条
  NProgress.done()
  
  const appStore = useAppStore()
  
  // 添加到访问历史
  appStore.addVisitedView({
    name: to.name as string,
    path: to.path,
    title: to.meta?.title as string,
    meta: to.meta
  })
  
  // 添加到缓存列表
  if (to.meta?.keepAlive) {
    appStore.addCachedView(to.name as string)
  }
})

// 路由错误处理
router.onError((error) => {
  console.error('路由错误:', error)
  NProgress.done()
})

export default router
