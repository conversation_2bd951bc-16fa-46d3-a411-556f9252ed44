<template>
  <div class="sidebar-menu">
    <el-menu
      :default-active="activeMenu"
      :collapse="appStore.sidebarCollapsed"
      :unique-opened="true"
      :router="true"
      class="sidebar-menu__content"
    >
      <template v-for="route in menuRoutes" :key="route.path">
        <SidebarMenuItem :route="route" />
      </template>
    </el-menu>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import SidebarMenuItem from './SidebarMenuItem.vue'
import type { RouteRecordRaw } from 'vue-router'

const route = useRoute()
const appStore = useAppStore()
const userStore = useUserStore()

// 当前激活的菜单
const activeMenu = computed(() => {
  const { meta, path } = route
  if (meta?.activeMenu) {
    return meta.activeMenu as string
  }
  return path
})

// 过滤菜单路由
const menuRoutes = computed(() => {
  const routes = route.matched[0]?.children || []
  return filterMenuRoutes(routes)
})

// 过滤路由，只显示有权限且不隐藏的菜单
function filterMenuRoutes(routes: RouteRecordRaw[]): RouteRecordRaw[] {
  return routes.filter(route => {
    // 隐藏的菜单不显示
    if (route.meta?.hideInMenu) {
      return false
    }
    
    // 检查角色权限
    if (route.meta?.roles) {
      const roles = route.meta.roles as string[]
      if (!userStore.hasAnyRole(roles)) {
        return false
      }
    }
    
    // 检查权限
    if (route.meta?.permissions) {
      const permissions = route.meta.permissions as string[]
      if (!userStore.hasAnyPermission(permissions)) {
        return false
      }
    }
    
    // 递归过滤子路由
    if (route.children) {
      route.children = filterMenuRoutes(route.children)
    }
    
    return true
  })
}
</script>

<style lang="scss" scoped>
.sidebar-menu {
  height: 100%;
  
  &__content {
    border: none;
    height: 100%;
    width: 100%;
    
    :deep(.el-menu-item),
    :deep(.el-sub-menu__title) {
      height: 48px;
      line-height: 48px;
      padding: 0 16px;
      
      &:hover {
        background-color: var(--el-menu-hover-bg-color);
      }
      
      .el-icon {
        margin-right: 8px;
        width: 20px;
        text-align: center;
      }
    }
    
    :deep(.el-menu-item.is-active) {
      background-color: var(--el-menu-active-bg-color);
      color: var(--el-menu-active-color);
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 3px;
        background-color: var(--el-color-primary);
      }
    }
    
    :deep(.el-sub-menu) {
      .el-menu-item {
        padding-left: 48px;
        
        &.is-active {
          background-color: var(--el-menu-active-bg-color);
        }
      }
    }
    
    // 折叠状态样式
    &.el-menu--collapse {
      :deep(.el-menu-item),
      :deep(.el-sub-menu__title) {
        padding: 0 16px;
        text-align: center;
        
        .el-icon {
          margin-right: 0;
        }
        
        span {
          display: none;
        }
      }
      
      :deep(.el-sub-menu) {
        .el-menu-item {
          padding-left: 16px;
        }
      }
    }
  }
}
</style>
