<template>
  <el-breadcrumb class="breadcrumb" separator="/">
    <el-breadcrumb-item
      v-for="(item, index) in breadcrumbList"
      :key="item.path"
      :to="index === breadcrumbList.length - 1 ? undefined : item.path"
    >
      <el-icon v-if="item.icon && index === 0" class="breadcrumb__icon">
        <component :is="item.icon" />
      </el-icon>
      <span>{{ item.title }}</span>
    </el-breadcrumb-item>
  </el-breadcrumb>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'

interface BreadcrumbItem {
  path: string
  title: string
  icon?: string
}

const route = useRoute()

// 面包屑列表
const breadcrumbList = computed(() => {
  const matched = route.matched.filter(item => item.meta?.title)
  const breadcrumbs: BreadcrumbItem[] = []
  
  matched.forEach((item, index) => {
    // 跳过根路由
    if (item.path === '/') {
      return
    }
    
    const breadcrumb: BreadcrumbItem = {
      path: item.path,
      title: item.meta?.title as string
    }
    
    // 第一个面包屑显示图标
    if (index === 1) {
      breadcrumb.icon = item.meta?.icon as string
    }
    
    breadcrumbs.push(breadcrumb)
  })
  
  return breadcrumbs
})
</script>

<style lang="scss" scoped>
.breadcrumb {
  font-size: 14px;
  
  &__icon {
    margin-right: 4px;
    font-size: 16px;
  }
  
  :deep(.el-breadcrumb__item) {
    .el-breadcrumb__inner {
      display: flex;
      align-items: center;
      color: var(--el-text-color-regular);
      
      &:hover {
        color: var(--el-color-primary);
      }
    }
    
    &:last-child .el-breadcrumb__inner {
      color: var(--el-text-color-primary);
      font-weight: 500;
      cursor: default;
      
      &:hover {
        color: var(--el-text-color-primary);
      }
    }
  }
}
</style>
