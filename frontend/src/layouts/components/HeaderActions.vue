<template>
  <div class="header-actions">
    <!-- 在线状态指示器 -->
    <div class="header-actions__item">
      <el-tooltip :content="appStore.isOnline ? '在线' : '离线'" placement="bottom">
        <div class="online-status" :class="{ 'online-status--offline': !appStore.isOnline }">
          <div class="online-status__dot"></div>
        </div>
      </el-tooltip>
    </div>
    
    <!-- 全屏切换 -->
    <div class="header-actions__item">
      <el-tooltip :content="isFullscreen ? '退出全屏' : '全屏'" placement="bottom">
        <el-button type="text" @click="toggleFullscreen">
          <el-icon size="18">
            <FullScreen v-if="!isFullscreen" />
            <Aim v-else />
          </el-icon>
        </el-button>
      </el-tooltip>
    </div>
    
    <!-- 主题切换 -->
    <div class="header-actions__item">
      <el-dropdown @command="handleThemeChange">
        <el-button type="text">
          <el-icon size="18">
            <Sunny v-if="appStore.settings.theme === 'light'" />
            <Moon v-else-if="appStore.settings.theme === 'dark'" />
            <Monitor v-else />
          </el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="light">
              <el-icon><Sunny /></el-icon>
              <span>浅色主题</span>
            </el-dropdown-item>
            <el-dropdown-item command="dark">
              <el-icon><Moon /></el-icon>
              <span>深色主题</span>
            </el-dropdown-item>
            <el-dropdown-item command="auto">
              <el-icon><Monitor /></el-icon>
              <span>跟随系统</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    
    <!-- 消息通知 -->
    <div class="header-actions__item">
      <el-badge :value="notificationCount" :hidden="notificationCount === 0">
        <el-button type="text" @click="showNotifications">
          <el-icon size="18">
            <Bell />
          </el-icon>
        </el-button>
      </el-badge>
    </div>
    
    <!-- 设置 -->
    <div class="header-actions__item">
      <el-tooltip content="设置" placement="bottom">
        <el-button type="text" @click="showSettings">
          <el-icon size="18">
            <Setting />
          </el-icon>
        </el-button>
      </el-tooltip>
    </div>
    
    <!-- 用户菜单 -->
    <div class="header-actions__item">
      <el-dropdown @command="handleUserMenuCommand">
        <div class="user-avatar">
          <el-avatar 
            :size="32" 
            :src="userStore.user?.avatar" 
            :alt="userStore.user?.realName"
          >
            <el-icon><User /></el-icon>
          </el-avatar>
          <span v-if="!appStore.isMobile" class="user-name">
            {{ userStore.user?.realName || userStore.user?.username }}
          </span>
          <el-icon class="user-arrow">
            <ArrowDown />
          </el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">
              <el-icon><User /></el-icon>
              <span>个人中心</span>
            </el-dropdown-item>
            <el-dropdown-item command="settings">
              <el-icon><Setting /></el-icon>
              <span>账户设置</span>
            </el-dropdown-item>
            <el-dropdown-item divided command="logout">
              <el-icon><SwitchButton /></el-icon>
              <span>退出登录</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import { ElMessageBox, ElMessage } from 'element-plus'

const router = useRouter()
const appStore = useAppStore()
const userStore = useUserStore()

// 全屏状态
const isFullscreen = ref(false)

// 通知数量
const notificationCount = computed(() => {
  // 这里应该从通知 store 获取未读消息数量
  return 0
})

// 切换全屏
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    isFullscreen.value = true
  } else {
    document.exitFullscreen()
    isFullscreen.value = false
  }
}

// 监听全屏状态变化
document.addEventListener('fullscreenchange', () => {
  isFullscreen.value = !!document.fullscreenElement
})

// 主题切换
const handleThemeChange = (theme: 'light' | 'dark' | 'auto') => {
  appStore.setTheme(theme)
  ElMessage.success(`已切换到${theme === 'light' ? '浅色' : theme === 'dark' ? '深色' : '自动'}主题`)
}

// 显示通知
const showNotifications = () => {
  ElMessage.info('通知功能开发中...')
}

// 显示设置
const showSettings = () => {
  // 这里应该打开设置面板
  ElMessage.info('设置面板开发中...')
}

// 用户菜单操作
const handleUserMenuCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/profile?tab=settings')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        await userStore.logout()
        router.push('/login')
      } catch (error) {
        // 用户取消
      }
      break
  }
}
</script>

<style lang="scss" scoped>
.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  
  &__item {
    display: flex;
    align-items: center;
  }
}

.online-status {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  
  &__dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--el-color-success);
    animation: pulse 2s infinite;
  }
  
  &--offline &__dot {
    background-color: var(--el-color-danger);
    animation: none;
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--el-color-success-rgb), 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(var(--el-color-success-rgb), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--el-color-success-rgb), 0);
  }
}

.user-avatar {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s;
  
  &:hover {
    background-color: var(--el-fill-color-light);
  }
  
  .user-name {
    font-size: 14px;
    color: var(--el-text-color-primary);
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .user-arrow {
    font-size: 12px;
    color: var(--el-text-color-regular);
    transition: transform 0.3s;
  }
  
  &:hover .user-arrow {
    transform: rotate(180deg);
  }
}

// 移动端适配
@media (max-width: 768px) {
  .header-actions {
    gap: 4px;
    
    &__item {
      &:nth-child(1),
      &:nth-child(2),
      &:nth-child(4) {
        display: none;
      }
    }
  }
  
  .user-avatar {
    padding: 4px;
    
    .user-name {
      display: none;
    }
  }
}
</style>
