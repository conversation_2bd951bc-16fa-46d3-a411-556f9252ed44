<template>
  <!-- 有子菜单的情况 -->
  <el-sub-menu 
    v-if="hasChildren" 
    :index="route.path"
    :popper-class="'sidebar-submenu-popper'"
  >
    <template #title>
      <el-icon v-if="route.meta?.icon">
        <component :is="route.meta.icon" />
      </el-icon>
      <span>{{ route.meta?.title }}</span>
    </template>
    
    <template v-for="child in route.children" :key="child.path">
      <SidebarMenuItem :route="child" :base-path="resolvePath(child.path)" />
    </template>
  </el-sub-menu>
  
  <!-- 没有子菜单的情况 -->
  <el-menu-item v-else :index="resolvePath(route.path)">
    <el-icon v-if="route.meta?.icon">
      <component :is="route.meta.icon" />
    </el-icon>
    <template #title>
      <span>{{ route.meta?.title }}</span>
    </template>
  </el-menu-item>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { RouteRecordRaw } from 'vue-router'

interface Props {
  route: RouteRecordRaw
  basePath?: string
}

const props = withDefaults(defineProps<Props>(), {
  basePath: ''
})

// 是否有子菜单
const hasChildren = computed(() => {
  const children = props.route.children
  if (!children || children.length === 0) {
    return false
  }
  
  // 过滤掉隐藏的子菜单
  const visibleChildren = children.filter(child => !child.meta?.hideInMenu)
  return visibleChildren.length > 0
})

// 解析路径
const resolvePath = (path: string) => {
  if (path.startsWith('/')) {
    return path
  }
  
  if (props.basePath) {
    return `${props.basePath}/${path}`.replace(/\/+/g, '/')
  }
  
  return `${props.route.path}/${path}`.replace(/\/+/g, '/')
}
</script>

<style lang="scss">
.sidebar-submenu-popper {
  .el-menu-item {
    padding-left: 48px !important;
  }
}
</style>
