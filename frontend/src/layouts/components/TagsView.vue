<template>
  <div class="tags-view">
    <div class="tags-view__wrapper" ref="scrollContainer">
      <div class="tags-view__content" ref="scrollContent">
        <router-link
          v-for="tag in appStore.visitedViews"
          :key="tag.path"
          :to="tag.path"
          class="tags-view__item"
          :class="{ 'tags-view__item--active': isActive(tag) }"
          @contextmenu.prevent="openContextMenu($event, tag)"
        >
          <span class="tags-view__item-title">{{ tag.title }}</span>
          <el-icon 
            v-if="!isAffix(tag)"
            class="tags-view__item-close"
            @click.prevent.stop="closeTag(tag)"
          >
            <Close />
          </el-icon>
        </router-link>
      </div>
    </div>
    
    <!-- 右键菜单 -->
    <div class="tags-view__actions">
      <el-dropdown @command="handleCommand">
        <el-button type="text" size="small">
          <el-icon><More /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="closeOthers">
              <el-icon><Close /></el-icon>
              <span>关闭其他</span>
            </el-dropdown-item>
            <el-dropdown-item command="closeAll">
              <el-icon><CircleClose /></el-icon>
              <span>关闭所有</span>
            </el-dropdown-item>
            <el-dropdown-item command="refresh">
              <el-icon><Refresh /></el-icon>
              <span>刷新页面</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    
    <!-- 右键上下文菜单 -->
    <teleport to="body">
      <div
        v-show="contextMenu.visible"
        class="context-menu"
        :style="{ left: contextMenu.x + 'px', top: contextMenu.y + 'px' }"
        @click="closeContextMenu"
      >
        <div class="context-menu__item" @click="refreshTag">
          <el-icon><Refresh /></el-icon>
          <span>刷新</span>
        </div>
        <div 
          v-if="!isAffix(contextMenu.tag)"
          class="context-menu__item" 
          @click="closeTag(contextMenu.tag)"
        >
          <el-icon><Close /></el-icon>
          <span>关闭</span>
        </div>
        <div class="context-menu__item" @click="closeOtherTags">
          <el-icon><Close /></el-icon>
          <span>关闭其他</span>
        </div>
        <div class="context-menu__item" @click="closeAllTags">
          <el-icon><CircleClose /></el-icon>
          <span>关闭所有</span>
        </div>
      </div>
    </teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAppStore } from '@/stores/app'
import type { VisitedView } from '@/stores/app'

const route = useRoute()
const router = useRouter()
const appStore = useAppStore()

const scrollContainer = ref<HTMLElement>()
const scrollContent = ref<HTMLElement>()

// 右键菜单状态
const contextMenu = reactive({
  visible: false,
  x: 0,
  y: 0,
  tag: {} as VisitedView
})

// 固定标签（不能关闭的标签）
const affixTags = ['Dashboard']

// 判断标签是否激活
const isActive = (tag: VisitedView) => {
  return tag.path === route.path
}

// 判断是否为固定标签
const isAffix = (tag: VisitedView) => {
  return affixTags.includes(tag.name)
}

// 关闭标签
const closeTag = (tag: VisitedView) => {
  if (isAffix(tag)) return
  
  const index = appStore.visitedViews.findIndex(v => v.path === tag.path)
  appStore.removeVisitedView(tag.path)
  appStore.removeCachedView(tag.name)
  
  // 如果关闭的是当前标签，需要跳转到其他标签
  if (isActive(tag)) {
    const nextTag = appStore.visitedViews[index] || appStore.visitedViews[index - 1]
    if (nextTag) {
      router.push(nextTag.path)
    } else {
      router.push('/dashboard')
    }
  }
}

// 刷新标签
const refreshTag = () => {
  const tag = contextMenu.tag
  appStore.removeCachedView(tag.name)
  
  nextTick(() => {
    router.replace({
      path: '/redirect' + tag.path,
      query: route.query
    })
  })
}

// 关闭其他标签
const closeOtherTags = () => {
  const tag = contextMenu.tag
  appStore.removeOtherVisitedViews(tag.path)
  appStore.removeOtherCachedViews(tag.name)
  
  if (!isActive(tag)) {
    router.push(tag.path)
  }
}

// 关闭所有标签
const closeAllTags = () => {
  appStore.removeAllVisitedViews()
  appStore.removeAllCachedViews()
  router.push('/dashboard')
}

// 打开右键菜单
const openContextMenu = (e: MouseEvent, tag: VisitedView) => {
  contextMenu.visible = true
  contextMenu.x = e.clientX
  contextMenu.y = e.clientY
  contextMenu.tag = tag
  
  document.addEventListener('click', closeContextMenu)
}

// 关闭右键菜单
const closeContextMenu = () => {
  contextMenu.visible = false
  document.removeEventListener('click', closeContextMenu)
}

// 处理下拉菜单命令
const handleCommand = (command: string) => {
  switch (command) {
    case 'closeOthers':
      closeOtherTags()
      break
    case 'closeAll':
      closeAllTags()
      break
    case 'refresh':
      refreshTag()
      break
  }
}

// 滚动到当前标签
const scrollToActiveTag = () => {
  if (!scrollContainer.value || !scrollContent.value) return
  
  const activeTag = document.querySelector('.tags-view__item--active') as HTMLElement
  if (!activeTag) return
  
  const containerWidth = scrollContainer.value.offsetWidth
  const contentWidth = scrollContent.value.offsetWidth
  
  if (contentWidth <= containerWidth) return
  
  const activeTagLeft = activeTag.offsetLeft
  const activeTagWidth = activeTag.offsetWidth
  const scrollLeft = scrollContainer.value.scrollLeft
  
  if (activeTagLeft < scrollLeft) {
    scrollContainer.value.scrollLeft = activeTagLeft - 20
  } else if (activeTagLeft + activeTagWidth > scrollLeft + containerWidth) {
    scrollContainer.value.scrollLeft = activeTagLeft + activeTagWidth - containerWidth + 20
  }
}

// 监听路由变化，滚动到当前标签
watch(route, () => {
  nextTick(() => {
    scrollToActiveTag()
  })
})
</script>

<style lang="scss" scoped>
.tags-view {
  display: flex;
  align-items: center;
  height: 40px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-lighter);
  
  &__wrapper {
    flex: 1;
    overflow-x: auto;
    overflow-y: hidden;
    
    &::-webkit-scrollbar {
      display: none;
    }
  }
  
  &__content {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 0 16px;
    white-space: nowrap;
  }
  
  &__item {
    display: flex;
    align-items: center;
    gap: 6px;
    height: 28px;
    padding: 0 12px;
    font-size: 12px;
    color: var(--el-text-color-regular);
    background: var(--el-fill-color-lighter);
    border: 1px solid var(--el-border-color-light);
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s;
    cursor: pointer;
    
    &:hover {
      color: var(--el-color-primary);
      background: var(--el-color-primary-light-9);
      border-color: var(--el-color-primary-light-7);
    }
    
    &--active {
      color: var(--el-color-primary);
      background: var(--el-color-primary-light-9);
      border-color: var(--el-color-primary);
    }
    
    &-title {
      max-width: 120px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    
    &-close {
      font-size: 12px;
      border-radius: 50%;
      transition: all 0.3s;
      
      &:hover {
        background: var(--el-color-primary-light-7);
        color: var(--el-color-primary);
      }
    }
  }
  
  &__actions {
    padding: 0 16px;
    border-left: 1px solid var(--el-border-color-lighter);
  }
}

.context-menu {
  position: fixed;
  z-index: 9999;
  background: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
  box-shadow: var(--el-box-shadow-light);
  padding: 4px 0;
  min-width: 120px;
  
  &__item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    font-size: 14px;
    color: var(--el-text-color-primary);
    cursor: pointer;
    transition: background-color 0.3s;
    
    &:hover {
      background: var(--el-fill-color-light);
    }
    
    .el-icon {
      font-size: 16px;
    }
  }
}

// 移动端隐藏标签页
@media (max-width: 768px) {
  .tags-view {
    display: none;
  }
}
</style>
