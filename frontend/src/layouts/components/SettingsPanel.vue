<template>
  <el-drawer
    v-model="visible"
    title="系统设置"
    direction="rtl"
    size="320px"
    :before-close="handleClose"
  >
    <div class="settings-panel">
      <!-- 主题设置 -->
      <div class="settings-section">
        <h4 class="settings-section__title">主题设置</h4>
        <div class="settings-section__content">
          <div class="theme-options">
            <div 
              class="theme-option"
              :class="{ 'theme-option--active': appStore.settings.theme === 'light' }"
              @click="setTheme('light')"
            >
              <div class="theme-option__preview theme-option__preview--light"></div>
              <span>浅色</span>
            </div>
            <div 
              class="theme-option"
              :class="{ 'theme-option--active': appStore.settings.theme === 'dark' }"
              @click="setTheme('dark')"
            >
              <div class="theme-option__preview theme-option__preview--dark"></div>
              <span>深色</span>
            </div>
            <div 
              class="theme-option"
              :class="{ 'theme-option--active': appStore.settings.theme === 'auto' }"
              @click="setTheme('auto')"
            >
              <div class="theme-option__preview theme-option__preview--auto"></div>
              <span>自动</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 主色调设置 -->
      <div class="settings-section">
        <h4 class="settings-section__title">主色调</h4>
        <div class="settings-section__content">
          <div class="color-options">
            <div
              v-for="color in colorOptions"
              :key="color.value"
              class="color-option"
              :class="{ 'color-option--active': appStore.settings.primaryColor === color.value }"
              :style="{ backgroundColor: color.value }"
              @click="setPrimaryColor(color.value)"
            >
              <el-icon v-if="appStore.settings.primaryColor === color.value">
                <Check />
              </el-icon>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 布局设置 -->
      <div class="settings-section">
        <h4 class="settings-section__title">布局设置</h4>
        <div class="settings-section__content">
          <div class="setting-item">
            <span>固定头部</span>
            <el-switch
              v-model="appStore.settings.fixedHeader"
              @change="updateSettings({ fixedHeader: $event })"
            />
          </div>
          <div class="setting-item">
            <span>显示面包屑</span>
            <el-switch
              v-model="appStore.settings.showBreadcrumb"
              @change="updateSettings({ showBreadcrumb: $event })"
            />
          </div>
          <div class="setting-item">
            <span>显示标签页</span>
            <el-switch
              v-model="appStore.settings.showTagsView"
              @change="updateSettings({ showTagsView: $event })"
            />
          </div>
        </div>
      </div>
      
      <!-- 语言设置 -->
      <div class="settings-section">
        <h4 class="settings-section__title">语言设置</h4>
        <div class="settings-section__content">
          <el-select
            v-model="appStore.settings.language"
            @change="updateSettings({ language: $event })"
          >
            <el-option label="简体中文" value="zh-CN" />
            <el-option label="English" value="en-US" />
          </el-select>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="settings-actions">
        <el-button @click="resetSettings">重置设置</el-button>
        <el-button type="primary" @click="saveSettings">保存设置</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useAppStore } from '@/stores/app'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { AppSettings } from '@/stores/app'

const appStore = useAppStore()

const visible = ref(false)

// 颜色选项
const colorOptions = [
  { name: '默认蓝', value: '#409EFF' },
  { name: '薄荷绿', value: '#00D2D3' },
  { name: '极光绿', value: '#00C9A7' },
  { name: '日暮黄', value: '#FFB800' },
  { name: '胭脂粉', value: '#F56C6C' },
  { name: '姹紫嫣红', value: '#722ED1' }
]

// 设置主题
const setTheme = (theme: 'light' | 'dark' | 'auto') => {
  appStore.setTheme(theme)
}

// 设置主色调
const setPrimaryColor = (color: string) => {
  updateSettings({ primaryColor: color })
  
  // 动态更新CSS变量
  document.documentElement.style.setProperty('--el-color-primary', color)
}

// 更新设置
const updateSettings = (settings: Partial<AppSettings>) => {
  appStore.updateSettings(settings)
}

// 重置设置
const resetSettings = async () => {
  try {
    await ElMessageBox.confirm('确定要重置所有设置吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const defaultSettings: AppSettings = {
      theme: 'light',
      primaryColor: '#409EFF',
      sidebarCollapsed: false,
      showBreadcrumb: true,
      showTagsView: true,
      fixedHeader: true,
      language: 'zh-CN'
    }
    
    appStore.updateSettings(defaultSettings)
    ElMessage.success('设置已重置')
  } catch (error) {
    // 用户取消
  }
}

// 保存设置
const saveSettings = () => {
  ElMessage.success('设置已保存')
  visible.value = false
}

// 关闭面板
const handleClose = () => {
  visible.value = false
}

// 暴露方法给父组件
defineExpose({
  show: () => {
    visible.value = true
  }
})
</script>

<style lang="scss" scoped>
.settings-panel {
  padding: 20px;
}

.settings-section {
  margin-bottom: 32px;
  
  &__title {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0 0 16px 0;
  }
  
  &__content {
    .el-select {
      width: 100%;
    }
  }
}

.theme-options {
  display: flex;
  gap: 16px;
}

.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  
  &__preview {
    width: 48px;
    height: 36px;
    border-radius: 6px;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 12px;
    }
    
    &--light {
      background: #f5f7fa;
      
      &::before {
        background: #ffffff;
      }
    }
    
    &--dark {
      background: #1d1e1f;
      
      &::before {
        background: #363637;
      }
    }
    
    &--auto {
      background: linear-gradient(45deg, #f5f7fa 50%, #1d1e1f 50%);
      
      &::before {
        background: linear-gradient(90deg, #ffffff 50%, #363637 50%);
      }
    }
  }
  
  &--active &__preview {
    border-color: var(--el-color-primary);
  }
  
  span {
    font-size: 12px;
    color: var(--el-text-color-regular);
  }
  
  &--active span {
    color: var(--el-color-primary);
    font-weight: 500;
  }
}

.color-options {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 12px;
}

.color-option {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
  
  &:hover {
    transform: scale(1.1);
  }
  
  &--active {
    transform: scale(1.1);
    box-shadow: 0 0 0 2px var(--el-bg-color), 0 0 0 4px currentColor;
  }
  
  .el-icon {
    color: white;
    font-size: 16px;
  }
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  
  span {
    font-size: 14px;
    color: var(--el-text-color-primary);
  }
}

.settings-actions {
  display: flex;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid var(--el-border-color-lighter);
  
  .el-button {
    flex: 1;
  }
}
</style>
