<template>
  <div class="main-layout">
    <!-- 侧边栏 -->
    <aside 
      class="sidebar"
      :class="{ 'sidebar--collapsed': appStore.sidebarCollapsed }"
    >
      <div class="sidebar__header">
        <div class="logo">
          <img src="/workflow-icon.svg" alt="Logo" class="logo__icon" />
          <span v-show="!appStore.sidebarCollapsed" class="logo__text">
            工作流引擎
          </span>
        </div>
      </div>
      
      <div class="sidebar__content">
        <SidebarMenu />
      </div>
    </aside>
    
    <!-- 主内容区 -->
    <div class="main-content">
      <!-- 顶部导航栏 -->
      <header class="header">
        <div class="header__left">
          <el-button
            type="text"
            class="sidebar-toggle"
            @click="appStore.toggleSidebar"
          >
            <el-icon size="20">
              <Fold v-if="!appStore.sidebarCollapsed" />
              <Expand v-else />
            </el-icon>
          </el-button>
          
          <Breadcrumb v-if="appStore.settings.showBreadcrumb" />
        </div>
        
        <div class="header__right">
          <HeaderActions />
        </div>
      </header>
      
      <!-- 标签页导航 -->
      <TagsView v-if="appStore.settings.showTagsView" />
      
      <!-- 页面内容 -->
      <main class="page-content">
        <router-view v-slot="{ Component, route }">
          <transition 
            :name="route.meta?.transition || 'fade'" 
            mode="out-in"
            appear
          >
            <keep-alive :include="appStore.cachedViews">
              <component :is="Component" :key="route.fullPath" />
            </keep-alive>
          </transition>
        </router-view>
      </main>
    </div>
    
    <!-- 设置面板 -->
    <SettingsPanel />
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useAppStore } from '@/stores/app'
import SidebarMenu from './components/SidebarMenu.vue'
import Breadcrumb from './components/Breadcrumb.vue'
import HeaderActions from './components/HeaderActions.vue'
import TagsView from './components/TagsView.vue'
import SettingsPanel from './components/SettingsPanel.vue'

const appStore = useAppStore()

// 监听窗口大小变化
const handleResize = () => {
  appStore.updateWindowSize()
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 点击遮罩关闭侧边栏（移动端）
const handleMaskClick = () => {
  if (appStore.isMobile && !appStore.sidebarCollapsed) {
    appStore.setSidebarCollapsed(true)
  }
}
</script>

<style lang="scss" scoped>
.main-layout {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

.sidebar {
  width: 240px;
  background: var(--el-bg-color);
  border-right: 1px solid var(--el-border-color-light);
  transition: width 0.3s ease;
  z-index: 1000;
  
  &--collapsed {
    width: 64px;
  }
  
  &__header {
    height: 60px;
    display: flex;
    align-items: center;
    padding: 0 16px;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }
  
  &__content {
    height: calc(100% - 60px);
    overflow-y: auto;
    overflow-x: hidden;
  }
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  
  &__icon {
    width: 32px;
    height: 32px;
    flex-shrink: 0;
  }
  
  &__text {
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    white-space: nowrap;
  }
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-lighter);
  
  &__left {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  &__right {
    display: flex;
    align-items: center;
  }
}

.sidebar-toggle {
  padding: 8px;
  
  &:hover {
    background: var(--el-fill-color-light);
  }
}

.page-content {
  flex: 1;
  overflow: auto;
  background: var(--el-bg-color-page);
}

// 移动端适配
@media (max-width: 768px) {
  .main-layout {
    position: relative;
  }
  
  .sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 2000;
    transform: translateX(0);
    transition: transform 0.3s ease;
    
    &--collapsed {
      transform: translateX(-100%);
      width: 240px; // 移动端保持原宽度，通过transform隐藏
    }
  }
  
  .main-content {
    width: 100%;
    margin-left: 0;
  }
  
  // 遮罩层
  .sidebar:not(.sidebar--collapsed)::before {
    content: '';
    position: fixed;
    top: 0;
    left: 240px;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: -1;
  }
}

// 路由过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-left-enter-active,
.slide-left-leave-active {
  transition: all 0.3s ease;
}

.slide-left-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.slide-left-leave-to {
  transform: translateX(-100%);
  opacity: 0;
}
</style>
