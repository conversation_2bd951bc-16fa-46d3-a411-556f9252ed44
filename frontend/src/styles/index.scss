// 全局样式文件

// 重置样式
@import './reset.scss';

// 变量定义
@import './variables.scss';

// 工具类
@import './utilities.scss';

// 组件样式
@import './components.scss';

// 响应式断点
@import './responsive.scss';

// 全局基础样式
html {
  box-sizing: border-box;
  font-size: 14px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.6;
  color: var(--el-text-color-primary);
  background-color: var(--el-bg-color-page);
  margin: 0;
  padding: 0;
}

// 链接样式
a {
  color: var(--el-color-primary);
  text-decoration: none;
  transition: color 0.3s;
  
  &:hover {
    color: var(--el-color-primary-light-3);
  }
  
  &:focus {
    outline: 2px solid var(--el-color-primary);
    outline-offset: 2px;
  }
}

// 按钮样式增强
.el-button {
  transition: all 0.3s;
  
  &:focus {
    outline: 2px solid var(--el-color-primary);
    outline-offset: 2px;
  }
}

// 输入框样式增强
.el-input__wrapper {
  transition: all 0.3s;
  
  &:focus-within {
    box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
  }
}

// 卡片样式
.card {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
  transition: box-shadow 0.3s;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }
}

// 页面容器
.page-container {
  padding: 24px;
  
  @media (max-width: 768px) {
    padding: 16px;
  }
}

// 内容区域
.content-area {
  background: var(--el-bg-color);
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  
  @media (max-width: 768px) {
    padding: 16px;
    margin-bottom: 16px;
  }
}

// 工具栏
.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
  margin-bottom: 24px;
  
  &__left,
  &__right {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  &__title {
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0;
  }
}

// 表格样式增强
.el-table {
  .el-table__header {
    th {
      background: var(--el-fill-color-lighter);
      font-weight: 600;
    }
  }
  
  .el-table__row {
    &:hover {
      background: var(--el-fill-color-light);
    }
  }
}

// 分页样式
.pagination-container {
  display: flex;
  justify-content: center;
  padding: 24px 0;
}

// 加载状态
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 48px;
  
  .loading-text {
    margin-left: 12px;
    color: var(--el-text-color-regular);
  }
}

// 空状态
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  color: var(--el-text-color-placeholder);
  
  .empty-icon {
    font-size: 64px;
    margin-bottom: 16px;
  }
  
  .empty-text {
    font-size: 16px;
    margin-bottom: 24px;
  }
}

// 状态标签
.status-tag {
  &--success {
    background: var(--el-color-success-light-9);
    color: var(--el-color-success);
    border-color: var(--el-color-success-light-7);
  }
  
  &--warning {
    background: var(--el-color-warning-light-9);
    color: var(--el-color-warning);
    border-color: var(--el-color-warning-light-7);
  }
  
  &--danger {
    background: var(--el-color-danger-light-9);
    color: var(--el-color-danger);
    border-color: var(--el-color-danger-light-7);
  }
  
  &--info {
    background: var(--el-color-info-light-9);
    color: var(--el-color-info);
    border-color: var(--el-color-info-light-7);
  }
}

// 动画类
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  transform: translateY(20px);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(-20px);
  opacity: 0;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--el-border-color-darker);
  border-radius: 4px;
  
  &:hover {
    background: var(--el-border-color-dark);
  }
}

// 打印样式
@media print {
  .no-print {
    display: none !important;
  }
  
  .page-container {
    padding: 0;
  }
  
  .toolbar {
    display: none;
  }
}
