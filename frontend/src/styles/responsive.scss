// 响应式样式

// 移动端优先的响应式设计

// 超小屏幕 (手机, 小于 576px)
@media (max-width: 575.98px) {
  .container {
    padding-left: 12px;
    padding-right: 12px;
  }
  
  .page-container {
    padding: 12px;
  }
  
  .content-area {
    padding: 16px;
    margin-bottom: 16px;
  }
  
  .toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    
    &__left,
    &__right {
      justify-content: center;
    }
    
    &__title {
      text-align: center;
      font-size: 16px;
    }
  }
  
  .el-table {
    font-size: 12px;
    
    .el-table__header {
      th {
        padding: 8px 4px;
      }
    }
    
    .el-table__body {
      td {
        padding: 8px 4px;
      }
    }
  }
  
  .el-dialog {
    width: 95% !important;
    margin: 0 auto;
    
    .el-dialog__body {
      padding: 16px;
    }
  }
  
  .el-drawer {
    width: 100% !important;
  }
  
  .metric-card {
    padding: 16px;
    
    &__value {
      font-size: 24px;
    }
  }
  
  .info-card {
    padding: 16px;
    
    &__header {
      .icon {
        width: 32px;
        height: 32px;
        font-size: 16px;
      }
      
      .title {
        font-size: 14px;
      }
    }
  }
}

// 小屏幕 (平板, 576px 及以上)
@media (min-width: 576px) and (max-width: 767.98px) {
  .container {
    max-width: 540px;
    padding-left: 16px;
    padding-right: 16px;
  }
  
  .page-container {
    padding: 16px;
  }
  
  .el-dialog {
    width: 90% !important;
  }
  
  .el-drawer {
    width: 80% !important;
  }
}

// 中等屏幕 (平板, 768px 及以上)
@media (min-width: 768px) and (max-width: 991.98px) {
  .container {
    max-width: 720px;
    padding-left: 20px;
    padding-right: 20px;
  }
  
  .page-container {
    padding: 20px;
  }
  
  .el-dialog {
    width: 80% !important;
  }
  
  .el-drawer {
    width: 60% !important;
  }
  
  // 网格系统调整
  .col-md-1 { width: 8.333333%; }
  .col-md-2 { width: 16.666667%; }
  .col-md-3 { width: 25%; }
  .col-md-4 { width: 33.333333%; }
  .col-md-6 { width: 50%; }
  .col-md-8 { width: 66.666667%; }
  .col-md-9 { width: 75%; }
  .col-md-12 { width: 100%; }
}

// 大屏幕 (桌面, 992px 及以上)
@media (min-width: 992px) and (max-width: 1199.98px) {
  .container {
    max-width: 960px;
    padding-left: 24px;
    padding-right: 24px;
  }
  
  .page-container {
    padding: 24px;
  }
  
  .el-dialog {
    width: 70% !important;
  }
  
  .el-drawer {
    width: 50% !important;
  }
  
  // 网格系统调整
  .col-lg-1 { width: 8.333333%; }
  .col-lg-2 { width: 16.666667%; }
  .col-lg-3 { width: 25%; }
  .col-lg-4 { width: 33.333333%; }
  .col-lg-6 { width: 50%; }
  .col-lg-8 { width: 66.666667%; }
  .col-lg-9 { width: 75%; }
  .col-lg-12 { width: 100%; }
}

// 超大屏幕 (大桌面, 1200px 及以上)
@media (min-width: 1200px) and (max-width: 1599.98px) {
  .container {
    max-width: 1140px;
    padding-left: 24px;
    padding-right: 24px;
  }
  
  .el-dialog {
    width: 60% !important;
  }
  
  .el-drawer {
    width: 40% !important;
  }
  
  // 网格系统调整
  .col-xl-1 { width: 8.333333%; }
  .col-xl-2 { width: 16.666667%; }
  .col-xl-3 { width: 25%; }
  .col-xl-4 { width: 33.333333%; }
  .col-xl-6 { width: 50%; }
  .col-xl-8 { width: 66.666667%; }
  .col-xl-9 { width: 75%; }
  .col-xl-12 { width: 100%; }
}

// 超超大屏幕 (1600px 及以上)
@media (min-width: 1600px) {
  .container {
    max-width: 1520px;
  }
  
  .el-dialog {
    width: 50% !important;
  }
  
  .el-drawer {
    width: 35% !important;
  }
}

// 响应式网格系统
.row {
  display: flex;
  flex-wrap: wrap;
  margin-left: -12px;
  margin-right: -12px;
}

.col {
  flex: 1;
  padding-left: 12px;
  padding-right: 12px;
}

// 响应式显示/隐藏
.d-xs-none { 
  @media (max-width: 575.98px) { 
    display: none !important; 
  } 
}

.d-sm-none { 
  @media (min-width: 576px) and (max-width: 767.98px) { 
    display: none !important; 
  } 
}

.d-md-none { 
  @media (min-width: 768px) and (max-width: 991.98px) { 
    display: none !important; 
  } 
}

.d-lg-none { 
  @media (min-width: 992px) and (max-width: 1199.98px) { 
    display: none !important; 
  } 
}

.d-xl-none { 
  @media (min-width: 1200px) { 
    display: none !important; 
  } 
}

// 响应式文本对齐
.text-xs-center { 
  @media (max-width: 575.98px) { 
    text-align: center !important; 
  } 
}

.text-sm-center { 
  @media (min-width: 576px) and (max-width: 767.98px) { 
    text-align: center !important; 
  } 
}

.text-md-center { 
  @media (min-width: 768px) and (max-width: 991.98px) { 
    text-align: center !important; 
  } 
}

.text-lg-center { 
  @media (min-width: 992px) and (max-width: 1199.98px) { 
    text-align: center !important; 
  } 
}

.text-xl-center { 
  @media (min-width: 1200px) { 
    text-align: center !important; 
  } 
}

// 响应式边距
@media (max-width: 575.98px) {
  .m-xs-0 { margin: 0 !important; }
  .m-xs-1 { margin: 4px !important; }
  .m-xs-2 { margin: 8px !important; }
  .m-xs-3 { margin: 12px !important; }
  .m-xs-4 { margin: 16px !important; }
  
  .p-xs-0 { padding: 0 !important; }
  .p-xs-1 { padding: 4px !important; }
  .p-xs-2 { padding: 8px !important; }
  .p-xs-3 { padding: 12px !important; }
  .p-xs-4 { padding: 16px !important; }
}

@media (min-width: 576px) and (max-width: 767.98px) {
  .m-sm-0 { margin: 0 !important; }
  .m-sm-1 { margin: 4px !important; }
  .m-sm-2 { margin: 8px !important; }
  .m-sm-3 { margin: 12px !important; }
  .m-sm-4 { margin: 16px !important; }
  
  .p-sm-0 { padding: 0 !important; }
  .p-sm-1 { padding: 4px !important; }
  .p-sm-2 { padding: 8px !important; }
  .p-sm-3 { padding: 12px !important; }
  .p-sm-4 { padding: 16px !important; }
}

// 打印样式
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break-before {
    page-break-before: always;
  }
  
  .print-break-after {
    page-break-after: always;
  }
  
  .print-break-inside-avoid {
    page-break-inside: avoid;
  }
  
  body {
    font-size: 12pt;
    line-height: 1.4;
  }
  
  .page-container {
    padding: 0;
  }
  
  .toolbar,
  .pagination-container {
    display: none;
  }
  
  .el-table {
    .el-table__header {
      th {
        background-color: #f5f7fa !important;
        -webkit-print-color-adjust: exact;
      }
    }
  }
}
