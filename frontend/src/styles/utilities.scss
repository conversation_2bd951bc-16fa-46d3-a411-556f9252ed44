// 工具类样式

// 文本对齐
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-justify { text-align: justify !important; }

// 文本颜色
.text-primary { color: var(--primary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-info { color: var(--info-color) !important; }
.text-muted { color: var(--text-color-secondary) !important; }

// 背景颜色
.bg-primary { background-color: var(--primary-color) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }
.bg-info { background-color: var(--info-color) !important; }
.bg-light { background-color: var(--fill-color-light) !important; }
.bg-dark { background-color: var(--fill-color-darker) !important; }

// 字体大小
.text-xs { font-size: var(--font-size-extra-small) !important; }
.text-sm { font-size: var(--font-size-small) !important; }
.text-base { font-size: var(--font-size-base) !important; }
.text-lg { font-size: var(--font-size-large) !important; }
.text-xl { font-size: var(--font-size-extra-large) !important; }

// 字体粗细
.font-light { font-weight: 300 !important; }
.font-normal { font-weight: 400 !important; }
.font-medium { font-weight: 500 !important; }
.font-semibold { font-weight: 600 !important; }
.font-bold { font-weight: 700 !important; }

// 显示/隐藏
.d-none { display: none !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }

// Flex 布局
.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }

.justify-start { justify-content: flex-start !important; }
.justify-end { justify-content: flex-end !important; }
.justify-center { justify-content: center !important; }
.justify-between { justify-content: space-between !important; }
.justify-around { justify-content: space-around !important; }
.justify-evenly { justify-content: space-evenly !important; }

.align-start { align-items: flex-start !important; }
.align-end { align-items: flex-end !important; }
.align-center { align-items: center !important; }
.align-baseline { align-items: baseline !important; }
.align-stretch { align-items: stretch !important; }

.flex-1 { flex: 1 !important; }
.flex-auto { flex: auto !important; }
.flex-none { flex: none !important; }

// 位置
.position-static { position: static !important; }
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

// 浮动
.float-left { float: left !important; }
.float-right { float: right !important; }
.float-none { float: none !important; }

// 清除浮动
.clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 边距 (margin)
@for $i from 0 through 10 {
  .m-#{$i} { margin: #{$i * 4}px !important; }
  .mt-#{$i} { margin-top: #{$i * 4}px !important; }
  .mr-#{$i} { margin-right: #{$i * 4}px !important; }
  .mb-#{$i} { margin-bottom: #{$i * 4}px !important; }
  .ml-#{$i} { margin-left: #{$i * 4}px !important; }
  .mx-#{$i} { 
    margin-left: #{$i * 4}px !important; 
    margin-right: #{$i * 4}px !important; 
  }
  .my-#{$i} { 
    margin-top: #{$i * 4}px !important; 
    margin-bottom: #{$i * 4}px !important; 
  }
}

// 内边距 (padding)
@for $i from 0 through 10 {
  .p-#{$i} { padding: #{$i * 4}px !important; }
  .pt-#{$i} { padding-top: #{$i * 4}px !important; }
  .pr-#{$i} { padding-right: #{$i * 4}px !important; }
  .pb-#{$i} { padding-bottom: #{$i * 4}px !important; }
  .pl-#{$i} { padding-left: #{$i * 4}px !important; }
  .px-#{$i} { 
    padding-left: #{$i * 4}px !important; 
    padding-right: #{$i * 4}px !important; 
  }
  .py-#{$i} { 
    padding-top: #{$i * 4}px !important; 
    padding-bottom: #{$i * 4}px !important; 
  }
}

// 宽度
.w-auto { width: auto !important; }
.w-full { width: 100% !important; }
.w-screen { width: 100vw !important; }

@for $i from 1 through 12 {
  .w-#{$i}\/12 { width: percentage($i / 12) !important; }
}

// 高度
.h-auto { height: auto !important; }
.h-full { height: 100% !important; }
.h-screen { height: 100vh !important; }

// 最大宽度
.max-w-xs { max-width: 20rem !important; }
.max-w-sm { max-width: 24rem !important; }
.max-w-md { max-width: 28rem !important; }
.max-w-lg { max-width: 32rem !important; }
.max-w-xl { max-width: 36rem !important; }
.max-w-2xl { max-width: 42rem !important; }
.max-w-full { max-width: 100% !important; }

// 圆角
.rounded-none { border-radius: 0 !important; }
.rounded-sm { border-radius: 2px !important; }
.rounded { border-radius: 4px !important; }
.rounded-md { border-radius: 6px !important; }
.rounded-lg { border-radius: 8px !important; }
.rounded-xl { border-radius: 12px !important; }
.rounded-full { border-radius: 9999px !important; }

// 边框
.border { border: 1px solid var(--border-color-base) !important; }
.border-0 { border: 0 !important; }
.border-t { border-top: 1px solid var(--border-color-base) !important; }
.border-r { border-right: 1px solid var(--border-color-base) !important; }
.border-b { border-bottom: 1px solid var(--border-color-base) !important; }
.border-l { border-left: 1px solid var(--border-color-base) !important; }

// 阴影
.shadow-none { box-shadow: none !important; }
.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important; }
.shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important; }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important; }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important; }

// 溢出
.overflow-auto { overflow: auto !important; }
.overflow-hidden { overflow: hidden !important; }
.overflow-visible { overflow: visible !important; }
.overflow-scroll { overflow: scroll !important; }

.overflow-x-auto { overflow-x: auto !important; }
.overflow-x-hidden { overflow-x: hidden !important; }
.overflow-x-visible { overflow-x: visible !important; }
.overflow-x-scroll { overflow-x: scroll !important; }

.overflow-y-auto { overflow-y: auto !important; }
.overflow-y-hidden { overflow-y: hidden !important; }
.overflow-y-visible { overflow-y: visible !important; }
.overflow-y-scroll { overflow-y: scroll !important; }

// 文本省略
.truncate {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

.text-ellipsis-2 {
  display: -webkit-box !important;
  -webkit-line-clamp: 2 !important;
  -webkit-box-orient: vertical !important;
  overflow: hidden !important;
}

.text-ellipsis-3 {
  display: -webkit-box !important;
  -webkit-line-clamp: 3 !important;
  -webkit-box-orient: vertical !important;
  overflow: hidden !important;
}

// 光标
.cursor-auto { cursor: auto !important; }
.cursor-default { cursor: default !important; }
.cursor-pointer { cursor: pointer !important; }
.cursor-wait { cursor: wait !important; }
.cursor-text { cursor: text !important; }
.cursor-move { cursor: move !important; }
.cursor-not-allowed { cursor: not-allowed !important; }

// 用户选择
.select-none { user-select: none !important; }
.select-text { user-select: text !important; }
.select-all { user-select: all !important; }
.select-auto { user-select: auto !important; }

// 响应式工具类
@media (max-width: 767px) {
  .hidden-xs { display: none !important; }
}

@media (min-width: 768px) and (max-width: 991px) {
  .hidden-sm { display: none !important; }
}

@media (min-width: 992px) and (max-width: 1199px) {
  .hidden-md { display: none !important; }
}

@media (min-width: 1200px) {
  .hidden-lg { display: none !important; }
}

@media (max-width: 767px) {
  .visible-xs { display: block !important; }
}

@media (min-width: 768px) and (max-width: 991px) {
  .visible-sm { display: block !important; }
}

@media (min-width: 992px) and (max-width: 1199px) {
  .visible-md { display: block !important; }
}

@media (min-width: 1200px) {
  .visible-lg { display: block !important; }
}
