// 组件样式增强

// Element Plus 组件样式覆盖
.el-button {
  &.is-loading {
    pointer-events: none;
  }
  
  &--primary {
    &:hover {
      background-color: var(--el-color-primary-light-3);
      border-color: var(--el-color-primary-light-3);
    }
    
    &:active {
      background-color: var(--el-color-primary-dark-2);
      border-color: var(--el-color-primary-dark-2);
    }
  }
}

.el-input {
  &.is-focus {
    .el-input__wrapper {
      box-shadow: 0 0 0 1px var(--el-color-primary) inset;
    }
  }
  
  &.is-error {
    .el-input__wrapper {
      box-shadow: 0 0 0 1px var(--el-color-danger) inset;
    }
  }
}

.el-table {
  .el-table__header-wrapper {
    .el-table__header {
      th {
        background-color: var(--el-fill-color-lighter);
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }
  }
  
  .el-table__body-wrapper {
    .el-table__row {
      &:hover {
        background-color: var(--el-fill-color-light);
      }
      
      &.current-row {
        background-color: var(--el-color-primary-light-9);
      }
    }
  }
  
  .el-table__empty-block {
    background-color: var(--el-bg-color);
  }
}

.el-card {
  border: 1px solid var(--el-border-color-light);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }
  
  .el-card__header {
    background-color: var(--el-fill-color-lighter);
    border-bottom: 1px solid var(--el-border-color-lighter);
    font-weight: 600;
  }
}

.el-dialog {
  .el-dialog__header {
    background-color: var(--el-fill-color-lighter);
    border-bottom: 1px solid var(--el-border-color-lighter);
    
    .el-dialog__title {
      font-weight: 600;
    }
  }
  
  .el-dialog__footer {
    border-top: 1px solid var(--el-border-color-lighter);
    background-color: var(--el-fill-color-lighter);
  }
}

.el-drawer {
  .el-drawer__header {
    background-color: var(--el-fill-color-lighter);
    border-bottom: 1px solid var(--el-border-color-lighter);
    
    .el-drawer__title {
      font-weight: 600;
    }
  }
}

.el-menu {
  &.el-menu--horizontal {
    border-bottom: 1px solid var(--el-border-color-lighter);
    
    .el-menu-item {
      &:hover {
        background-color: var(--el-fill-color-light);
      }
      
      &.is-active {
        border-bottom-color: var(--el-color-primary);
        color: var(--el-color-primary);
      }
    }
  }
  
  &.el-menu--vertical {
    .el-menu-item {
      &:hover {
        background-color: var(--el-fill-color-light);
      }
      
      &.is-active {
        background-color: var(--el-color-primary-light-9);
        color: var(--el-color-primary);
        
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: 3px;
          background-color: var(--el-color-primary);
        }
      }
    }
  }
}

.el-tabs {
  .el-tabs__header {
    .el-tabs__nav-wrap {
      &::after {
        background-color: var(--el-border-color-lighter);
      }
    }
    
    .el-tabs__item {
      &:hover {
        color: var(--el-color-primary);
      }
      
      &.is-active {
        color: var(--el-color-primary);
      }
    }
    
    .el-tabs__active-bar {
      background-color: var(--el-color-primary);
    }
  }
}

.el-pagination {
  .el-pager {
    .number {
      &:hover {
        color: var(--el-color-primary);
      }
      
      &.is-active {
        background-color: var(--el-color-primary);
        color: #fff;
      }
    }
  }
  
  .btn-prev,
  .btn-next {
    &:hover {
      color: var(--el-color-primary);
    }
  }
}

.el-form {
  .el-form-item {
    &.is-error {
      .el-form-item__label {
        color: var(--el-color-danger);
      }
    }
    
    &.is-required {
      .el-form-item__label {
        &::before {
          content: '*';
          color: var(--el-color-danger);
          margin-right: 4px;
        }
      }
    }
  }
}

.el-select {
  .el-select__wrapper {
    &:hover {
      border-color: var(--el-color-primary-light-7);
    }
    
    &.is-focused {
      border-color: var(--el-color-primary);
      box-shadow: 0 0 0 1px var(--el-color-primary) inset;
    }
  }
}

.el-date-editor {
  &:hover {
    border-color: var(--el-color-primary-light-7);
  }
  
  &.is-active {
    border-color: var(--el-color-primary);
    box-shadow: 0 0 0 1px var(--el-color-primary) inset;
  }
}

// 自定义组件样式
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  
  &--success {
    background-color: var(--el-color-success-light-9);
    color: var(--el-color-success);
  }
  
  &--warning {
    background-color: var(--el-color-warning-light-9);
    color: var(--el-color-warning);
  }
  
  &--danger {
    background-color: var(--el-color-danger-light-9);
    color: var(--el-color-danger);
  }
  
  &--info {
    background-color: var(--el-color-info-light-9);
    color: var(--el-color-info);
  }
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: var(--el-fill-color);
  border-radius: 4px;
  overflow: hidden;
  
  &__fill {
    height: 100%;
    background-color: var(--el-color-primary);
    transition: width 0.3s ease;
    
    &--success {
      background-color: var(--el-color-success);
    }
    
    &--warning {
      background-color: var(--el-color-warning);
    }
    
    &--danger {
      background-color: var(--el-color-danger);
    }
  }
}

.info-card {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
  }
  
  &__header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    
    .icon {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      font-size: 20px;
      
      &--primary {
        background-color: var(--el-color-primary-light-9);
        color: var(--el-color-primary);
      }
      
      &--success {
        background-color: var(--el-color-success-light-9);
        color: var(--el-color-success);
      }
      
      &--warning {
        background-color: var(--el-color-warning-light-9);
        color: var(--el-color-warning);
      }
      
      &--danger {
        background-color: var(--el-color-danger-light-9);
        color: var(--el-color-danger);
      }
    }
    
    .title {
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin: 0;
    }
  }
  
  &__content {
    color: var(--el-text-color-regular);
    line-height: 1.6;
  }
  
  &__footer {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid var(--el-border-color-lighter);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.metric-card {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  padding: 24px;
  text-align: center;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }
  
  &__value {
    font-size: 32px;
    font-weight: 700;
    color: var(--el-text-color-primary);
    margin-bottom: 8px;
    
    &--primary {
      color: var(--el-color-primary);
    }
    
    &--success {
      color: var(--el-color-success);
    }
    
    &--warning {
      color: var(--el-color-warning);
    }
    
    &--danger {
      color: var(--el-color-danger);
    }
  }
  
  &__label {
    font-size: 14px;
    color: var(--el-text-color-regular);
    margin-bottom: 4px;
  }
  
  &__change {
    font-size: 12px;
    
    &--up {
      color: var(--el-color-success);
      
      &::before {
        content: '↗';
        margin-right: 2px;
      }
    }
    
    &--down {
      color: var(--el-color-danger);
      
      &::before {
        content: '↘';
        margin-right: 2px;
      }
    }
  }
}
