// CSS 变量定义

:root {
  // 颜色系统
  --primary-color: #409eff;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;
  
  // 文本颜色
  --text-color-primary: #303133;
  --text-color-regular: #606266;
  --text-color-secondary: #909399;
  --text-color-placeholder: #c0c4cc;
  
  // 边框颜色
  --border-color-base: #dcdfe6;
  --border-color-light: #e4e7ed;
  --border-color-lighter: #ebeef5;
  --border-color-extra-light: #f2f6fc;
  
  // 背景颜色
  --bg-color: #ffffff;
  --bg-color-page: #f2f3f5;
  --bg-color-overlay: #ffffff;
  
  // 填充颜色
  --fill-color-darker: #e6e8eb;
  --fill-color-dark: #ebedf0;
  --fill-color: #f0f2f5;
  --fill-color-light: #f5f7fa;
  --fill-color-lighter: #fafafa;
  --fill-color-extra-light: #fafcff;
  --fill-color-blank: #ffffff;
  
  // 字体
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-size-extra-large: 20px;
  --font-size-large: 18px;
  --font-size-medium: 16px;
  --font-size-base: 14px;
  --font-size-small: 13px;
  --font-size-extra-small: 12px;
  
  // 行高
  --line-height-base: 1.6;
  
  // 间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;
  
  // 圆角
  --border-radius-base: 4px;
  --border-radius-small: 2px;
  --border-radius-large: 8px;
  --border-radius-round: 20px;
  --border-radius-circle: 50%;
  
  // 阴影
  --box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --box-shadow-dark: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.12);
  --box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  // 过渡
  --transition-duration: 0.3s;
  --transition-function: cubic-bezier(0.645, 0.045, 0.355, 1);
  
  // Z-index
  --z-index-normal: 1;
  --z-index-top: 1000;
  --z-index-popper: 2000;
  
  // 断点
  --breakpoint-xs: 480px;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-xxl: 1600px;
}

// 深色主题
[data-theme='dark'] {
  // 文本颜色
  --text-color-primary: #e5eaf3;
  --text-color-regular: #cfd3dc;
  --text-color-secondary: #a3a6ad;
  --text-color-placeholder: #8d9095;
  
  // 边框颜色
  --border-color-base: #4c4d4f;
  --border-color-light: #414243;
  --border-color-lighter: #363637;
  --border-color-extra-light: #2b2b2c;
  
  // 背景颜色
  --bg-color: #1d1e1f;
  --bg-color-page: #0a0a0a;
  --bg-color-overlay: #1d1e1f;
  
  // 填充颜色
  --fill-color-darker: #48494a;
  --fill-color-dark: #3d3e3f;
  --fill-color: #303133;
  --fill-color-light: #262727;
  --fill-color-lighter: #1d1e1f;
  --fill-color-extra-light: #191a1b;
  --fill-color-blank: #141414;
}

// SCSS 变量 (用于计算和函数)
$primary-color: #409eff;
$success-color: #67c23a;
$warning-color: #e6a23c;
$danger-color: #f56c6c;
$info-color: #909399;

$font-size-base: 14px;
$font-size-small: 13px;
$font-size-large: 16px;

$spacing-unit: 8px;
$border-radius-base: 4px;

// 响应式断点
$breakpoints: (
  xs: 480px,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1600px
);

// 媒体查询 mixin
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}.";
  }
}

// 响应式断点 mixin (max-width)
@mixin respond-below($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (max-width: map-get($breakpoints, $breakpoint) - 1px) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}.";
  }
}

// 文本省略 mixin
@mixin text-ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

// 清除浮动 mixin
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 居中 mixin
@mixin center($horizontal: true, $vertical: true) {
  position: absolute;
  
  @if $horizontal and $vertical {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  } @else if $horizontal {
    left: 50%;
    transform: translate(-50%, 0);
  } @else if $vertical {
    top: 50%;
    transform: translate(0, -50%);
  }
}

// Flex 居中 mixin
@mixin flex-center($direction: row) {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: $direction;
}

// 阴影 mixin
@mixin box-shadow($level: 1) {
  @if $level == 1 {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  } @else if $level == 2 {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.12);
  } @else if $level == 3 {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
}
