<template>
  <div id="app" class="workflow-app">
    <!-- 全局加载遮罩 -->
    <el-loading-service 
      v-if="appStore.globalLoading" 
      :text="appStore.loadingText"
      background="rgba(0, 0, 0, 0.7)"
    />
    
    <!-- 路由视图 -->
    <router-view v-slot="{ Component, route }">
      <transition 
        :name="route.meta?.transition || 'fade'" 
        mode="out-in"
        appear
      >
        <keep-alive :include="keepAliveComponents">
          <component :is="Component" :key="route.fullPath" />
        </keep-alive>
      </transition>
    </router-view>
    
    <!-- 全局消息提示 -->
    <GlobalMessage />
    
    <!-- 全局确认对话框 -->
    <GlobalConfirm />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted } from 'vue'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import GlobalMessage from '@/components/global/GlobalMessage.vue'
import GlobalConfirm from '@/components/global/GlobalConfirm.vue'

const appStore = useAppStore()
const userStore = useUserStore()

// 需要缓存的组件
const keepAliveComponents = computed(() => {
  return appStore.cachedViews
})

// 初始化应用
onMounted(async () => {
  try {
    // 初始化主题
    appStore.initTheme()
    
    // 检查用户登录状态
    await userStore.checkAuth()
    
    // 初始化应用配置
    await appStore.initApp()
    
  } catch (error) {
    console.error('应用初始化失败:', error)
  }
})

// 监听窗口大小变化
const handleResize = () => {
  appStore.updateWindowSize()
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
  handleResize() // 初始化窗口大小
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 监听在线状态
const handleOnline = () => {
  appStore.setOnlineStatus(true)
}

const handleOffline = () => {
  appStore.setOnlineStatus(false)
}

onMounted(() => {
  window.addEventListener('online', handleOnline)
  window.addEventListener('offline', handleOffline)
  appStore.setOnlineStatus(navigator.onLine)
})

onUnmounted(() => {
  window.removeEventListener('online', handleOnline)
  window.removeEventListener('offline', handleOffline)
})
</script>

<style lang="scss">
.workflow-app {
  height: 100vh;
  overflow: hidden;
}

// 路由过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-left-enter-active,
.slide-left-leave-active {
  transition: all 0.3s ease;
}

.slide-left-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.slide-left-leave-to {
  transform: translateX(-100%);
  opacity: 0;
}

.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.3s ease;
}

.slide-right-enter-from {
  transform: translateX(-100%);
  opacity: 0;
}

.slide-right-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

// 全局滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--el-border-color-darker);
  border-radius: 4px;
  
  &:hover {
    background: var(--el-border-color-dark);
  }
}

// 响应式断点
@media (max-width: 768px) {
  .workflow-app {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .workflow-app {
    font-size: 12px;
  }
}
</style>
