import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'
import router from '@/router'

// API 响应接口
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: string
  traceId?: string
}

// 请求配置接口
export interface RequestConfig extends AxiosRequestConfig {
  skipAuth?: boolean
  skipErrorHandler?: boolean
  showLoading?: boolean
  loadingText?: string
}

// 创建 axios 实例
const service: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
service.interceptors.request.use(
  (config: RequestConfig) => {
    const userStore = useUserStore()
    const appStore = useAppStore()
    
    // 显示加载状态
    if (config.showLoading) {
      appStore.setGlobalLoading(true, config.loadingText)
    }
    
    // 添加认证头
    if (!config.skipAuth && userStore.token) {
      config.headers = config.headers || {}
      config.headers.Authorization = `Bearer ${userStore.token}`
    }
    
    // 添加请求ID
    config.headers = config.headers || {}
    config.headers['X-Request-Id'] = generateRequestId()
    
    // 添加时间戳
    config.headers['X-Timestamp'] = Date.now().toString()
    
    console.log(`🚀 API请求: ${config.method?.toUpperCase()} ${config.url}`, {
      params: config.params,
      data: config.data
    })
    
    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const appStore = useAppStore()
    const config = response.config as RequestConfig
    
    // 隐藏加载状态
    if (config.showLoading) {
      appStore.setGlobalLoading(false)
    }
    
    const { code, message, data } = response.data
    
    console.log(`✅ API响应: ${response.config.method?.toUpperCase()} ${response.config.url}`, {
      code,
      message,
      data
    })
    
    // 成功响应
    if (code === 200) {
      return response.data
    }
    
    // 业务错误
    if (!config.skipErrorHandler) {
      ElMessage.error(message || '请求失败')
    }
    
    return Promise.reject(new Error(message || '请求失败'))
  },
  async (error) => {
    const appStore = useAppStore()
    const userStore = useUserStore()
    const config = error.config as RequestConfig
    
    // 隐藏加载状态
    if (config?.showLoading) {
      appStore.setGlobalLoading(false)
    }
    
    console.error('API响应错误:', error)
    
    const { response } = error
    
    if (response) {
      const { status, data } = response
      
      switch (status) {
        case 401:
          // 未授权，尝试刷新令牌
          if (!config?.skipAuth && userStore.refreshToken) {
            try {
              const refreshed = await userStore.refreshAccessToken()
              if (refreshed) {
                // 重新发送原请求
                return service.request(config)
              }
            } catch (refreshError) {
              console.error('刷新令牌失败:', refreshError)
            }
          }
          
          // 刷新失败或没有刷新令牌，跳转到登录页
          userStore.clearAuth()
          router.push('/login')
          
          if (!config?.skipErrorHandler) {
            ElMessage.error('登录已过期，请重新登录')
          }
          break
          
        case 403:
          if (!config?.skipErrorHandler) {
            ElMessage.error('访问被拒绝，权限不足')
          }
          router.push('/403')
          break
          
        case 404:
          if (!config?.skipErrorHandler) {
            ElMessage.error('请求的资源不存在')
          }
          break
          
        case 500:
          if (!config?.skipErrorHandler) {
            ElMessage.error('服务器内部错误')
          }
          break
          
        default:
          if (!config?.skipErrorHandler) {
            const message = data?.message || `请求失败 (${status})`
            ElMessage.error(message)
          }
      }
    } else if (error.code === 'ECONNABORTED') {
      // 请求超时
      if (!config?.skipErrorHandler) {
        ElMessage.error('请求超时，请稍后重试')
      }
    } else if (error.message === 'Network Error') {
      // 网络错误
      if (!config?.skipErrorHandler) {
        ElMessage.error('网络连接失败，请检查网络')
      }
    } else {
      // 其他错误
      if (!config?.skipErrorHandler) {
        ElMessage.error(error.message || '请求失败')
      }
    }
    
    return Promise.reject(error)
  }
)

// 生成请求ID
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// 请求方法封装
export const request = {
  get<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    return service.get(url, config)
  },
  
  post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return service.post(url, data, config)
  },
  
  put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return service.put(url, data, config)
  },
  
  delete<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    return service.delete(url, config)
  },
  
  patch<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return service.patch(url, data, config)
  }
}

// 文件上传
export const uploadFile = (url: string, file: File, onProgress?: (progress: number) => void): Promise<ApiResponse> => {
  const formData = new FormData()
  formData.append('file', file)
  
  return service.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(progress)
      }
    }
  })
}

// 文件下载
export const downloadFile = async (url: string, filename?: string): Promise<void> => {
  try {
    const response = await service.get(url, {
      responseType: 'blob',
      skipErrorHandler: true
    })
    
    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  } catch (error) {
    console.error('文件下载失败:', error)
    ElMessage.error('文件下载失败')
  }
}

// 设置 axios 默认配置
export const setupAxios = () => {
  // 设置默认请求头
  axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest'
  
  // 设置默认超时时间
  axios.defaults.timeout = 30000
  
  console.log('✅ Axios 配置完成')
}

export default service
