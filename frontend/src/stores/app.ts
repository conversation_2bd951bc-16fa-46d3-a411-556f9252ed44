import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { RouteLocationNormalized } from 'vue-router'

export interface VisitedView {
  name: string
  path: string
  title: string
  meta?: any
}

export interface AppSettings {
  theme: 'light' | 'dark' | 'auto'
  primaryColor: string
  sidebarCollapsed: boolean
  showBreadcrumb: boolean
  showTagsView: boolean
  fixedHeader: boolean
  language: string
}

export const useAppStore = defineStore('app', () => {
  // 状态
  const globalLoading = ref(false)
  const loadingText = ref('加载中...')
  const sidebarCollapsed = ref(false)
  const windowWidth = ref(window.innerWidth)
  const windowHeight = ref(window.innerHeight)
  const isOnline = ref(navigator.onLine)
  
  // 访问历史
  const visitedViews = ref<VisitedView[]>([])
  const cachedViews = ref<string[]>([])
  
  // 应用设置
  const settings = ref<AppSettings>({
    theme: 'light',
    primaryColor: '#409EFF',
    sidebarCollapsed: false,
    showBreadcrumb: true,
    showTagsView: true,
    fixedHeader: true,
    language: 'zh-CN'
  })
  
  // 计算属性
  const isMobile = computed(() => windowWidth.value < 768)
  const isTablet = computed(() => windowWidth.value >= 768 && windowWidth.value < 1024)
  const isDesktop = computed(() => windowWidth.value >= 1024)
  
  const device = computed(() => {
    if (isMobile.value) return 'mobile'
    if (isTablet.value) return 'tablet'
    return 'desktop'
  })
  
  // 方法
  const setGlobalLoading = (loading: boolean, text = '加载中...') => {
    globalLoading.value = loading
    loadingText.value = text
  }
  
  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
    localStorage.setItem('sidebarCollapsed', String(sidebarCollapsed.value))
  }
  
  const setSidebarCollapsed = (collapsed: boolean) => {
    sidebarCollapsed.value = collapsed
    localStorage.setItem('sidebarCollapsed', String(collapsed))
  }
  
  const updateWindowSize = () => {
    windowWidth.value = window.innerWidth
    windowHeight.value = window.innerHeight
    
    // 移动端自动收起侧边栏
    if (isMobile.value && !sidebarCollapsed.value) {
      setSidebarCollapsed(true)
    }
  }
  
  const setOnlineStatus = (online: boolean) => {
    isOnline.value = online
  }
  
  // 访问历史管理
  const addVisitedView = (view: VisitedView) => {
    const existingIndex = visitedViews.value.findIndex(v => v.path === view.path)
    if (existingIndex > -1) {
      // 更新现有视图
      visitedViews.value[existingIndex] = { ...visitedViews.value[existingIndex], ...view }
    } else {
      // 添加新视图
      visitedViews.value.push(view)
    }
  }
  
  const removeVisitedView = (path: string) => {
    const index = visitedViews.value.findIndex(v => v.path === path)
    if (index > -1) {
      visitedViews.value.splice(index, 1)
    }
  }
  
  const removeOtherVisitedViews = (path: string) => {
    visitedViews.value = visitedViews.value.filter(v => v.path === path)
  }
  
  const removeAllVisitedViews = () => {
    visitedViews.value = []
  }
  
  // 缓存管理
  const addCachedView = (name: string) => {
    if (!cachedViews.value.includes(name)) {
      cachedViews.value.push(name)
    }
  }
  
  const removeCachedView = (name: string) => {
    const index = cachedViews.value.indexOf(name)
    if (index > -1) {
      cachedViews.value.splice(index, 1)
    }
  }
  
  const removeOtherCachedViews = (name: string) => {
    cachedViews.value = cachedViews.value.filter(v => v === name)
  }
  
  const removeAllCachedViews = () => {
    cachedViews.value = []
  }
  
  // 设置管理
  const updateSettings = (newSettings: Partial<AppSettings>) => {
    settings.value = { ...settings.value, ...newSettings }
    localStorage.setItem('appSettings', JSON.stringify(settings.value))
  }
  
  const initTheme = () => {
    const savedTheme = localStorage.getItem('theme')
    if (savedTheme) {
      settings.value.theme = savedTheme as 'light' | 'dark' | 'auto'
    }
    
    applyTheme()
  }
  
  const applyTheme = () => {
    const { theme } = settings.value
    const html = document.documentElement
    
    if (theme === 'dark') {
      html.classList.add('dark')
    } else if (theme === 'light') {
      html.classList.remove('dark')
    } else {
      // auto 模式
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      if (prefersDark) {
        html.classList.add('dark')
      } else {
        html.classList.remove('dark')
      }
    }
  }
  
  const setTheme = (theme: 'light' | 'dark' | 'auto') => {
    settings.value.theme = theme
    localStorage.setItem('theme', theme)
    applyTheme()
  }
  
  // 初始化应用
  const initApp = async () => {
    try {
      // 恢复设置
      const savedSettings = localStorage.getItem('appSettings')
      if (savedSettings) {
        const parsed = JSON.parse(savedSettings)
        settings.value = { ...settings.value, ...parsed }
      }
      
      // 恢复侧边栏状态
      const savedSidebarState = localStorage.getItem('sidebarCollapsed')
      if (savedSidebarState) {
        sidebarCollapsed.value = savedSidebarState === 'true'
      }
      
      // 监听系统主题变化
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      mediaQuery.addEventListener('change', () => {
        if (settings.value.theme === 'auto') {
          applyTheme()
        }
      })
      
    } catch (error) {
      console.error('应用初始化失败:', error)
    }
  }
  
  // 重置应用状态
  const resetApp = () => {
    visitedViews.value = []
    cachedViews.value = []
    globalLoading.value = false
    loadingText.value = '加载中...'
  }
  
  return {
    // 状态
    globalLoading,
    loadingText,
    sidebarCollapsed,
    windowWidth,
    windowHeight,
    isOnline,
    visitedViews,
    cachedViews,
    settings,
    
    // 计算属性
    isMobile,
    isTablet,
    isDesktop,
    device,
    
    // 方法
    setGlobalLoading,
    toggleSidebar,
    setSidebarCollapsed,
    updateWindowSize,
    setOnlineStatus,
    addVisitedView,
    removeVisitedView,
    removeOtherVisitedViews,
    removeAllVisitedViews,
    addCachedView,
    removeCachedView,
    removeOtherCachedViews,
    removeAllCachedViews,
    updateSettings,
    initTheme,
    setTheme,
    initApp,
    resetApp
  }
})
