import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authApi } from '@/api/auth'
import type { User, LoginRequest, RegisterRequest } from '@/types/auth'
import { ElMessage } from 'element-plus'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string>('')
  const refreshToken = ref<string>('')
  const permissions = ref<string[]>([])
  const roles = ref<string[]>([])
  
  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isSystemAdmin = computed(() => permissions.value.includes('SYSTEM_ADMIN'))
  const isTenantAdmin = computed(() => permissions.value.includes('TENANT_ADMIN'))
  
  const userInfo = computed(() => {
    if (!user.value) return null
    return {
      id: user.value.id,
      username: user.value.username,
      email: user.value.email,
      realName: user.value.realName,
      avatar: user.value.avatar,
      department: user.value.department,
      position: user.value.position
    }
  })
  
  // 方法
  const setToken = (accessToken: string, refreshTokenValue?: string) => {
    token.value = accessToken
    if (refreshTokenValue) {
      refreshToken.value = refreshTokenValue
    }
    
    // 保存到本地存储
    localStorage.setItem('access_token', accessToken)
    if (refreshTokenValue) {
      localStorage.setItem('refresh_token', refreshTokenValue)
    }
  }
  
  const setUser = (userData: User) => {
    user.value = userData
    permissions.value = userData.authorities || []
    roles.value = userData.roles?.map(role => role.code) || []
  }
  
  const clearAuth = () => {
    user.value = null
    token.value = ''
    refreshToken.value = ''
    permissions.value = []
    roles.value = []
    
    // 清除本地存储
    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
  }
  
  // 登录
  const login = async (loginData: LoginRequest) => {
    try {
      const response = await authApi.login(loginData)
      
      if (response.data) {
        const { accessToken, refreshToken: refreshTokenValue, user: userData } = response.data
        
        setToken(accessToken, refreshTokenValue)
        setUser(userData)
        
        ElMessage.success('登录成功')
        return true
      }
      
      return false
    } catch (error: any) {
      console.error('登录失败:', error)
      ElMessage.error(error.message || '登录失败')
      return false
    }
  }
  
  // 注册
  const register = async (registerData: RegisterRequest) => {
    try {
      const response = await authApi.register(registerData)
      
      if (response.data) {
        ElMessage.success('注册成功，请登录')
        return true
      }
      
      return false
    } catch (error: any) {
      console.error('注册失败:', error)
      ElMessage.error(error.message || '注册失败')
      return false
    }
  }
  
  // 登出
  const logout = async () => {
    try {
      await authApi.logout()
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      clearAuth()
      ElMessage.success('已退出登录')
    }
  }
  
  // 获取当前用户信息
  const getCurrentUser = async () => {
    try {
      const response = await authApi.getCurrentUser()
      
      if (response.data) {
        setUser(response.data)
        return response.data
      }
      
      return null
    } catch (error) {
      console.error('获取用户信息失败:', error)
      clearAuth()
      throw error
    }
  }
  
  // 刷新令牌
  const refreshAccessToken = async () => {
    try {
      if (!refreshToken.value) {
        throw new Error('没有刷新令牌')
      }
      
      const response = await authApi.refreshToken({ refreshToken: refreshToken.value })
      
      if (response.data) {
        const { accessToken, user: userData } = response.data
        setToken(accessToken)
        setUser(userData)
        return true
      }
      
      return false
    } catch (error) {
      console.error('刷新令牌失败:', error)
      clearAuth()
      return false
    }
  }
  
  // 检查认证状态
  const checkAuth = async () => {
    const savedToken = localStorage.getItem('access_token')
    const savedRefreshToken = localStorage.getItem('refresh_token')
    
    if (savedToken) {
      token.value = savedToken
      if (savedRefreshToken) {
        refreshToken.value = savedRefreshToken
      }
      
      try {
        await getCurrentUser()
        return true
      } catch (error) {
        // 尝试刷新令牌
        if (savedRefreshToken) {
          const refreshed = await refreshAccessToken()
          if (refreshed) {
            return true
          }
        }
        
        clearAuth()
        return false
      }
    }
    
    return false
  }
  
  // 修改密码
  const changePassword = async (oldPassword: string, newPassword: string) => {
    try {
      await authApi.changePassword(oldPassword, newPassword)
      ElMessage.success('密码修改成功')
      return true
    } catch (error: any) {
      console.error('修改密码失败:', error)
      ElMessage.error(error.message || '修改密码失败')
      return false
    }
  }
  
  // 权限检查
  const hasPermission = (permission: string) => {
    return permissions.value.includes(permission)
  }
  
  const hasAnyPermission = (permissionList: string[]) => {
    return permissionList.some(permission => permissions.value.includes(permission))
  }
  
  const hasAllPermissions = (permissionList: string[]) => {
    return permissionList.every(permission => permissions.value.includes(permission))
  }
  
  // 角色检查
  const hasRole = (role: string) => {
    return roles.value.includes(role)
  }
  
  const hasAnyRole = (roleList: string[]) => {
    return roleList.some(role => roles.value.includes(role))
  }
  
  const hasAllRoles = (roleList: string[]) => {
    return roleList.every(role => roles.value.includes(role))
  }
  
  // 更新用户信息
  const updateUserInfo = (userData: Partial<User>) => {
    if (user.value) {
      user.value = { ...user.value, ...userData }
    }
  }
  
  return {
    // 状态
    user,
    token,
    refreshToken,
    permissions,
    roles,
    
    // 计算属性
    isAuthenticated,
    isSystemAdmin,
    isTenantAdmin,
    userInfo,
    
    // 方法
    setToken,
    setUser,
    clearAuth,
    login,
    register,
    logout,
    getCurrentUser,
    refreshAccessToken,
    checkAuth,
    changePassword,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    hasAnyRole,
    hasAllRoles,
    updateUserInfo
  }
})
