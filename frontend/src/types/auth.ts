/**
 * 认证相关类型定义
 */

// 用户角色
export interface Role {
  id: string
  name: string
  code: string
  description?: string
  permissions: Permission[]
}

// 权限
export interface Permission {
  id: string
  name: string
  code: string
  description?: string
  resource?: string
  action?: string
}

// 用户信息
export interface User {
  id: string
  username: string
  email: string
  realName: string
  phone?: string
  avatar?: string
  department?: string
  position?: string
  enabled: boolean
  accountNonExpired: boolean
  accountNonLocked: boolean
  credentialsNonExpired: boolean
  roles: Role[]
  authorities: string[]
  createdAt: string
  updatedAt: string
  lastLoginAt?: string
  lastLoginIp?: string
}

// 登录请求
export interface LoginRequest {
  username: string
  password: string
  rememberMe?: boolean
}

// 登录响应
export interface LoginResponse {
  accessToken: string
  refreshToken: string
  tokenType: string
  expiresIn: number
  user: User
}

// 注册请求
export interface RegisterRequest {
  username: string
  email: string
  password: string
  confirmPassword: string
  realName: string
  phone?: string
  department?: string
  position?: string
}

// 刷新令牌请求
export interface RefreshTokenRequest {
  refreshToken: string
}

// 修改密码请求
export interface ChangePasswordRequest {
  oldPassword: string
  newPassword: string
  confirmPassword: string
}

// 重置密码请求
export interface ResetPasswordRequest {
  token: string
  newPassword: string
  confirmPassword: string
}

// 用户查询参数
export interface UserQueryParams {
  page?: number
  size?: number
  sort?: string
  direction?: 'asc' | 'desc'
  username?: string
  email?: string
  realName?: string
  department?: string
  enabled?: boolean
  keyword?: string
}

// 用户创建请求
export interface CreateUserRequest {
  username: string
  email: string
  password: string
  realName: string
  phone?: string
  avatar?: string
  department?: string
  position?: string
  enabled?: boolean
  roleIds: string[]
}

// 用户更新请求
export interface UpdateUserRequest {
  email?: string
  realName?: string
  phone?: string
  avatar?: string
  department?: string
  position?: string
  enabled?: boolean
  roleIds?: string[]
}

// 角色查询参数
export interface RoleQueryParams {
  page?: number
  size?: number
  sort?: string
  direction?: 'asc' | 'desc'
  name?: string
  code?: string
  keyword?: string
}

// 角色创建请求
export interface CreateRoleRequest {
  name: string
  code: string
  description?: string
  permissionIds: string[]
}

// 角色更新请求
export interface UpdateRoleRequest {
  name?: string
  description?: string
  permissionIds?: string[]
}

// 权限查询参数
export interface PermissionQueryParams {
  page?: number
  size?: number
  sort?: string
  direction?: 'asc' | 'desc'
  name?: string
  code?: string
  resource?: string
  action?: string
  keyword?: string
}

// 权限创建请求
export interface CreatePermissionRequest {
  name: string
  code: string
  description?: string
  resource?: string
  action?: string
}

// 权限更新请求
export interface UpdatePermissionRequest {
  name?: string
  description?: string
  resource?: string
  action?: string
}

// 登录日志
export interface LoginLog {
  id: string
  userId?: string
  username?: string
  ip: string
  userAgent: string
  success: boolean
  errorMessage?: string
  loginTime: string
}

// 操作日志
export interface OperationLog {
  id: string
  userId: string
  username: string
  operation: string
  resource: string
  method: string
  params?: string
  result?: string
  ip: string
  userAgent: string
  duration: number
  success: boolean
  errorMessage?: string
  operationTime: string
}

// 在线用户
export interface OnlineUser {
  userId: string
  username: string
  realName: string
  ip: string
  location?: string
  browser?: string
  os?: string
  loginTime: string
  lastActiveTime: string
}

// 用户统计
export interface UserStatistics {
  totalUsers: number
  activeUsers: number
  newUsersToday: number
  newUsersThisWeek: number
  newUsersThisMonth: number
  onlineUsers: number
  usersByDepartment: Record<string, number>
  usersByRole: Record<string, number>
  loginStatistics: {
    date: string
    count: number
  }[]
}
