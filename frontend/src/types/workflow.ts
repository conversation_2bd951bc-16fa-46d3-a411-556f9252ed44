/**
 * 工作流相关类型定义
 */

// 工作流状态
export enum WorkflowStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  DEPRECATED = 'DEPRECATED'
}

// 实例状态
export enum InstanceStatus {
  PENDING = 'PENDING',
  RUNNING = 'RUNNING',
  SUSPENDED = 'SUSPENDED',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
  TIMEOUT = 'TIMEOUT'
}

// 节点状态
export enum NodeStatus {
  PENDING = 'PENDING',
  RUNNING = 'RUNNING',
  WAITING = 'WAITING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  SKIPPED = 'SKIPPED',
  CANCELLED = 'CANCELLED',
  TIMEOUT = 'TIMEOUT'
}

// 节点类型
export enum NodeType {
  START = 'START',
  END = 'END',
  TASK = 'TASK',
  DECISION = 'DECISION',
  PARALLEL = 'PARALLEL',
  MERGE = 'MERGE',
  NOTIFICATION = 'NOTIFICATION',
  TIMER = 'TIMER',
  SCRIPT = 'SCRIPT',
  HTTP = 'HTTP',
  SUBPROCESS = 'SUBPROCESS'
}

// 工作流定义
export interface WorkflowDefinition {
  id: string
  name: string
  version: string
  description?: string
  category?: string
  dslContent: string
  status: WorkflowStatus
  active: boolean
  tags?: string[]
  variables?: Record<string, any>
  executionCount: number
  successCount: number
  failureCount: number
  avgExecutionTime?: number
  createdBy: string
  createdAt: string
  updatedBy?: string
  updatedAt?: string
  publishedBy?: string
  publishedAt?: string
}

// 工作流实例
export interface WorkflowInstance {
  id: string
  workflowDefinitionId: string
  instanceName?: string
  businessKey?: string
  status: InstanceStatus
  currentNodeId?: string
  currentNodeName?: string
  priority?: number
  tenantId?: string
  parentInstanceId?: string
  rootInstanceId?: string
  retryCount: number
  maxRetryCount: number
  errorMessage?: string
  inputData?: Record<string, any>
  outputData?: Record<string, any>
  variables?: Record<string, any>
  createdBy: string
  createdAt: string
  updatedAt?: string
  startTime?: string
  endTime?: string
  durationMs?: number
}

// 节点执行
export interface NodeExecution {
  id: string
  instanceId: string
  nodeId: string
  nodeName: string
  nodeType: NodeType
  status: NodeStatus
  executorId?: string
  executorName?: string
  priority?: number
  retryCount: number
  maxRetryCount: number
  timeoutMs?: number
  errorMessage?: string
  errorStackTrace?: string
  inputData?: Record<string, any>
  outputData?: Record<string, any>
  createdAt: string
  startTime?: string
  endTime?: string
  durationMs?: number
}

// 工作流节点定义
export interface WorkflowNode {
  id: string
  type: NodeType
  name: string
  description?: string
  x: number
  y: number
  width?: number
  height?: number
  properties?: Record<string, any>
  style?: Record<string, any>
}

// 工作流连线定义
export interface WorkflowEdge {
  id: string
  sourceNodeId: string
  targetNodeId: string
  sourceAnchor?: string
  targetAnchor?: string
  label?: string
  condition?: string
  properties?: Record<string, any>
  style?: Record<string, any>
}

// 工作流图形定义
export interface WorkflowGraph {
  nodes: WorkflowNode[]
  edges: WorkflowEdge[]
  variables?: Record<string, any>
  settings?: Record<string, any>
}

// 工作流定义查询参数
export interface WorkflowDefinitionQueryParams {
  page?: number
  size?: number
  sort?: string
  direction?: 'asc' | 'desc'
  name?: string
  status?: WorkflowStatus
  category?: string
  createdBy?: string
  keyword?: string
}

// 工作流定义创建请求
export interface CreateWorkflowDefinitionRequest {
  name: string
  version: string
  description?: string
  category?: string
  dslContent: string
  tags?: string[]
}

// 工作流定义更新请求
export interface UpdateWorkflowDefinitionRequest {
  description?: string
  category?: string
  dslContent?: string
  tags?: string[]
}

// 工作流实例查询参数
export interface WorkflowInstanceQueryParams {
  page?: number
  size?: number
  sort?: string
  direction?: 'asc' | 'desc'
  workflowDefinitionId?: string
  status?: InstanceStatus
  createdBy?: string
  businessKey?: string
  keyword?: string
}

// 工作流执行请求
export interface WorkflowExecutionRequest {
  workflowDefinitionId: string
  businessKey?: string
  inputData?: Record<string, any>
  priority?: number
  tenantId?: string
  parentInstanceId?: string
  maxRetryCount?: number
  timeoutMs?: number
  async?: boolean
  callbackUrl?: string
  extensions?: Record<string, any>
}

// 工作流统计信息
export interface WorkflowStatistics {
  totalDefinitions: number
  activeDefinitions: number
  totalInstances: number
  runningInstances: number
  completedInstances: number
  failedInstances: number
  avgExecutionTime: number
  successRate: number
  definitionsByCategory: Record<string, number>
  instancesByStatus: Record<string, number>
  executionTrend: {
    date: string
    count: number
    successCount: number
    failureCount: number
  }[]
}

// 节点执行统计
export interface NodeExecutionStatistics {
  totalExecutions: number
  successfulExecutions: number
  failedExecutions: number
  avgExecutionTime: number
  successRate: number
  executionsByType: Record<string, number>
  executionsByStatus: Record<string, number>
  performanceMetrics: {
    nodeType: string
    avgDuration: number
    successRate: number
    count: number
  }[]
}

// 工作流监控数据
export interface WorkflowMonitorData {
  instanceId: string
  workflowName: string
  status: InstanceStatus
  currentNodeId?: string
  currentNodeName?: string
  completedNodes: number
  failedNodes: number
  totalNodes: number
  progressPercentage: number
  executionDuration: number
  startTime?: string
  endTime?: string
  suspended: boolean
  cancelled: boolean
}

// DSL 验证结果
export interface DslValidationResult {
  valid: boolean
  errors: string[]
  warnings: string[]
  workflowInfo?: {
    name: string
    version: string
    description?: string
    nodeCount: number
    variables?: Record<string, any>
  }
}

// 工作流导出数据
export interface WorkflowExportData {
  definition: WorkflowDefinition
  graph: WorkflowGraph
  metadata: {
    exportTime: string
    exportBy: string
    version: string
  }
}

// 工作流导入请求
export interface WorkflowImportRequest {
  name?: string
  version?: string
  data: WorkflowExportData
  overwrite?: boolean
}
