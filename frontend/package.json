{"name": "workflow-engine-frontend", "version": "1.0.0", "description": "企业级工作流引擎前端可视化设计器", "private": true, "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@logicflow/core": "^1.2.26", "@logicflow/extension": "^1.2.26", "vue": "^3.3.8", "vue-router": "^4.2.5", "pinia": "^2.1.7", "axios": "^1.6.2", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.1.0", "echarts": "^5.4.3", "vue-echarts": "^6.6.1", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "monaco-editor": "^0.44.0", "js-yaml": "^4.1.0", "file-saver": "^2.0.5", "jszip": "^3.10.1"}, "devDependencies": {"@types/node": "^20.9.0", "@types/lodash-es": "^4.17.12", "@types/js-yaml": "^4.0.9", "@types/file-saver": "^2.0.7", "@vitejs/plugin-vue": "^4.5.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.4.0", "eslint": "^8.53.0", "eslint-plugin-vue": "^9.18.1", "prettier": "^3.1.0", "typescript": "~5.2.0", "unplugin-auto-import": "^0.16.7", "unplugin-vue-components": "^0.25.2", "vite": "^5.0.0", "vue-tsc": "^1.8.22"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}