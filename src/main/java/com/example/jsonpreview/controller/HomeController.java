package com.example.jsonpreview.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
public class HomeController {

    @GetMapping("/")
    public Map<String, Object> home() {
        Map<String, Object> response = new HashMap<>();
        response.put("message", "欢迎使用JSON预览应用");
        response.put("status", "运行中");
        response.put("version", "0.0.1-SNAPSHOT");
        return response;
    }

    @GetMapping("/health")
    public Map<String, String> health() {
        Map<String, String> response = new HashMap<>();
        response.put("status", "健康");
        response.put("timestamp", java.time.LocalDateTime.now().toString());
        return response;
    }
} 